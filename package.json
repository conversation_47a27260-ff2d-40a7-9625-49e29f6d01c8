{"private": true, "scripts": {"build": "node ..\\nuxt-base\\buildversion.js && nuxt build", "build-mac": "node ../nuxt-base/buildversion.js; nuxt build", "devs": "nuxt dev --port 3000 --https", "dev": "nuxt dev --host 0.0.0.0", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "devDependencies": {"@nuxt/devtools": "2.4.0", "@nuxtjs/google-fonts": "^3.2.0", "nuxt": "3.17.2", "nuxt-delay-hydration": "^1.3.8", "nuxt-vitalizer": "^0.10.0"}, "dependencies": {"@googlemaps/js-api-loader": "^1.16.8", "@sentry/nuxt": "^9.15.0", "@vueform/slider": "^2.1.10", "less": "^4.3.0", "less-loader": "^12.3.0", "swiper": "^11.2.6", "vee-validate": "4.15.0"}}