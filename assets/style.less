/*------- normalize -------*/
*{margin: 0; padding: 0; border: 0; outline: none; -webkit-tap-highlight-color: transparent; box-sizing: border-box; -webkit-font-smoothing: antialiased; -webkit-backface-visibility: hidden;}
img{max-width: 100%; height: auto;}
html{font-family: sans-serif; /* 1 */ -ms-text-size-adjust: 100%; /* 2 */ -webkit-text-size-adjust: 100%; scroll-padding-top: 100px;}
article, aside, details, figcaption, figure, footer, header, hgroup, main, menu, nav, section, summary{display: block;}
audio, canvas, progress, video{display: inline-block; /* 1 */}
audio:not([controls]){display: none; height: 0;}
progress{vertical-align: baseline;}
[hidden], template{display: none;}
a{background-color: transparent; -webkit-text-decoration-skip: objects; /* 2 */}
a:active, a:hover{outline: 0; -webkit-tap-highlight-color: transparent;}
abbr[title]{border-bottom: 1px dotted;}
b, strong{font-weight: bold;}
dfn{font-style: italic;}
mark{background: #ff0; color: #000;}
small{font-size: 80%;}
sub, sup{font-size: 75%; line-height: 0; position: relative; vertical-align: baseline;}
sup{top: -0.5em;}
sub{bottom: -0.25em;}
svg:not(:root){overflow: hidden;}
hr{box-sizing: border-box; height: 0; border-bottom: 1px solid #ccc; margin-bottom: 10px; border-top-style: none; border-right-style: none; border-left-style: none;}
pre{overflow: auto;}
pre.debug{font-size: 14px !important;}
code, kbd, pre, samp{font-family: monospace, monospace; font-size: 1em;}
button, input, optgroup, select, textarea{color: inherit; /* 1 */ font: inherit; /* 2 */ margin: 0; /* 3 */ -webkit-appearance: none;}
button{overflow: visible;}
button, select{text-transform: none;}
button, html input[type="button"], input[type="reset"], input[type="submit"]{-webkit-appearance: button; /* 2 */ cursor: pointer; /* 3 */}
button[disabled], html input[disabled]{cursor: default;}
button::-moz-focus-inner, [type="button"]::-moz-focus-inner, [type="reset"]::-moz-focus-inner, [type="submit"]::-moz-focus-inner{border-style: none; padding: 0;}
input{line-height: normal; border-radius: 0; box-shadow: none;}
input[type="checkbox"], input[type="radio"]{box-sizing: border-box; /* 1 */ padding: 0; /* 2 */}
input[type="number"]::-webkit-inner-spin-button, input[type="number"]::-webkit-outer-spin-button{height: auto;}
[type="search"]::-webkit-search-cancel-button, [type="search"]::-webkit-search-decoration{-webkit-appearance: none;}
input[type=text], input[type=email], input[type=password], input[type=tel], input[type=search]{-webkit-appearance: none;}
input[type=number] {-moz-appearance:textfield; -webkit-appearance: textfield; -ms-appearance: textfield; -webkit-appearance: none;}
input::-webkit-outer-spin-button, input::-webkit-inner-spin-button {-webkit-appearance: none;}
::-webkit-input-placeholder{color: inherit; opacity: 1;}
fieldset{border: none; margin: 0; padding: 0;}
textarea{overflow: auto; resize: vertical;}
optgroup{font-weight: bold;}
table{border-collapse: collapse; border-spacing: 0;}
input:-webkit-autofill, textarea:-webkit-autofill, select:-webkit-autofill{-webkit-box-shadow: 0 0 0 30px #fff inset; box-shadow: 0 0 0 30px #fff inset;} /* set browser autocomplete bg yellow color to white */
/*------- /normalize -------*/

/*------- /vars -------*/
:root{
	// layout
	--pageWidth: 1280px;
	--borderRadius: 4px;
	
	// colors
	--colorWhite: #fff;
	--colorBaseBlue: #0059c2;
	--colorBorderLightForms: #E5E6E8;
	--colorLightBlueBackground: #F4F6FA;
	--colorBase: #000C1A;
	--colorTextLightGray: #7F858C;
	--colorLightGray: #EFF0F1;
	--colorGrayFooter: #C7CACD;
	--colorYellow: #FFCC00;
	--colorOrange: #FFA200;
	--colorDarkBlue: #00428F;
	--colorBaseDarker: #0051B0;
	--colorBaseLighter: #3E82D2;
	--colorIconLightBlue: #CCDEF3;
	--colorIconMiddleBlue: #CCDEF3;
	--colorGreen: #09B46C;
	--colorLightGreen: #00FFB0;
	--colorRed: #E5454E;
	
	// text
	--font: "Inter", sans-serif;
	--fonti: "icomoon";
	--fontSizeDisplayBig: 56px;
	--fontSizeDisplaySmall: 48px;
	--fontSizeH1: 39px;
	--fontSizeH2: 32px;
	--fontSizeH3: 28px;
	--fontSizeH4: 24px;
	--fontSizeSpecial: 20px;
	--fontSize: 16px;
	--fontSizeSmall: 14px;
	--fontSizeLabel: 12px;
	--fontSizeLabelSmall: 11px;
	--lineHeight: 1.5;

	// forms
	--inputHeight: 56px;
	--inputPadding: 0 20px;
	--inputBorderRadius: 4px;
	
	--acOffsetTop: 53px;
	--acBorderColor: var(--colorBaseBlue);
	--acHoverBackgroundColor: var(--colorLightBlueBackground);
}

@media (max-width: 1300px){
	:root{
		--pageWidth: 100%;
		--fontSizeH1: 28px;
		--fontSizeH2: 24px;
		--fontSizeH3: 20px;
		--fontSizeH4: 18px;				
		--fontSizeSpecial: 18px;
	}
}

@media (max-width: 980px){
	:root{
		--fontSize: 15px;
		--fontSizeH1: 26px;
		--fontSizeH2: 23px;
		--fontSizeH3: 19px;
	}
}
/*------- /vars -------*/

/*------- fonts -------*/
@font-face {
    font-family: 'icomoon';
    src: url('assets/fonts/icomoon.woff?v1') format('woff');
    font-weight: normal;
    font-style: normal;
	font-display: swap;
}
/*------- /fonts -------*/

/*------- selectors -------*/
/*
::-webkit-scrollbar { -webkit-appearance: none; width: 5px; }
::-webkit-scrollbar-thumb {
	background-color: @lightGray; border-radius: 5px;
	box-shadow: 0 0 1px rgba(255,255,255,.5);
}
*/
body{background: #fff; color: var(--textColor); font: var(--fontSize)/var(--lineHeight) var(--font);}
a, .link{
	color: var(--colorBaseBlue); text-decoration: underline; .transition(text-decoration-color,0.3s); cursor: pointer;
	@media (min-width: @h){
		&:hover{text-decoration-color: transparent;}
	}
}
ul, ol{margin: 0; padding: 0;}
h1, h2, h3, h4{
	padding: 20px 0 12px; font-family: var(--font); font-weight: bold; line-height: 1.3;
	a, a:hover{text-decoration: none;}
}
h1{
	font-size: var(--fontSizeH1); padding: 0 0 24px;
	@media (max-width: @t){padding-bottom: 10px;}
	@media (max-width: @m){padding-bottom: 16px;}
}
h2{font-size: var(--fontSizeH2);}
h3{font-size: var(--fontSizeH3);}
h4{font-size: var(--fontSizeH4);}
p{padding-bottom: 20px;}
/*------- /selectors -------*/

/*------- forms -------*/
label{padding: 0 0 4px 0; display: inline-block;}
select{-moz-appearance: none; -o-appearance:none; -webkit-appearance: none; -ms-appearance: none;}
select::-ms-expand {display: none;}
input, textarea, select{
	-webkit-appearance: none; background-color: var(--colorWhite); border-radius: var(--inputBorderRadius); padding: 0 20px; border: 1px solid var(--colorBorderLightForms); font-size: 16px; width: 100%; height: var(--inputHeight); font-family: var(--font); color: var(--colorBase); line-height: normal; .transition(border-color);
	@media(max-width: @m){padding: 0 16px; font-size: 15px;}
}
input:disabled, textarea:disabled, input:disabled+label, .disabled{cursor: not-allowed !important; color: #ccc;}
input:hover, textarea:hover, select:hover, input:focus, textarea:focus, select:focus{border-color: var(--colorBaseBlue); outline: 0;}
input[type=submit], button{border: none; display: inline-block;}
input[type=checkbox], input[type=radio]{padding: 0; height: auto; border: none;}
textarea{height: 130px; padding-top: 10px; padding-bottom: 10px; line-height: 19px;}
legend{
	font-size: 16px; line-height: 18px; font-weight: bold;
	a{text-decoration: none;}
}
input[type=checkbox], input[type=radio]{position: absolute; left: -9999px; display: inline;}
input[type=checkbox] + label, input[type=radio] + label{
	cursor: pointer; position: relative; padding: 1px 0 4px 34px; min-height: 20px; line-height: 20px; font-size: 14px; text-align: left;
	@media (max-width: @t){padding-left: 25px;}
}
input[type=radio] + label{width: 100%;}

input[type=checkbox] + label:before{
	.pseudo(22px, 22px); text-indent: 1px; background: var(--inputBg); color: #fff; border: 1px solid var(--colorBorderLightForms); left: 0; text-align: center; top: 0; .icon-check(); font: 10px/22px var(--fonti); border-radius: 3px; color: transparent; .transition(all);
	@media (max-width: @m){width: 18px; height: 18px; line-height: 18px;}	
}
input[type=radio] + label:before{.pseudo(22px, 22px); border-radius: 50%; background: var(--inputBg); color: #fff; border: 1px solid var(--colorBorderLightForms); left: 0; text-align: center; top: 0; .transition(all);}
input[type=radio] + label:after{.pseudo(16px,16px); background: var(--colorBaseBlue); top: 4px; left: 4px; border-radius: 50%; visibility: hidden; opacity: 0; transition: opacity .3s, visiblity .3s;}

input[type=checkbox]:checked + label:before{background: var(--colorBaseBlue); border-color: var(--colorBaseBlue); color: var(--colorWhite);}
input[type=radio]:checked + label{font-weight: bold;}
input[type=radio]:checked + label:before{border-color: var(--colorBaseBlue);}
input[type=radio]:checked + label:after{visibility: visible; opacity: 1;}

select{background: var(--colorWhite) url(assets/images/select-arrow.svg) no-repeat; background-size: 12px; background-position: right 20px center; padding-right: 40px; cursor: pointer;}
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
textarea:-webkit-autofill,
textarea:-webkit-autofill:hover,
textarea:-webkit-autofill:focus,
select:-webkit-autofill,
select:-webkit-autofill:hover,
select:-webkit-autofill:focus {
  -webkit-box-shadow: 0 0 0px 1000px #ffffff inset !important;
}

.form-animated-label{
	p, .field{position: relative;padding-bottom: 10px;}
	label{position: absolute; top: 16px; left: 20px; padding: 0; cursor: text; z-index: 10; .transition(all); font-size: 16px; line-height: 22px;} 
	.focus,.ffl-floated,.floating-label{
		label{top: 7px; font-size: 12px; line-height: 16px; font-weight: normal; color: var(--colorBaseBlue);}
		input,select{padding-top: 15px; font-size: 16px;}
		textarea{padding-top: 25px;}
	}
	p.field{
		input label{pointer-events: none;}
	}
	.field.err input{border-color: var(--colorRed);}
	.field-remember_me{padding-top: 5px;}
	input[type=checkbox] + label, input[type=radio] + label{position: relative; left: auto; top: auto; cursor: pointer; padding: 2px 0 4px 32px; min-height: 24px; line-height: 20px; font-size: 14px; color: var(--textColor); text-align: left;}
	input[type=radio] + label{font-size: 16px; line-height: 22px; padding-top: 1px;}
	@media(max-width: @m){
		input[type=radio] + label{font-size: 15px;}
		.focus,.ffl-floated,.floating-label{
			input,select,textarea{font-size: 15px;}
		}
		label{font-size: 15px; left: 16px; top: 17px;}
	}
}
.form-login-forgotten-cnt{display: flex; align-items: center; width: 100%; padding-top: 5px;}
.link-forgotten-password{
	text-decoration: underline; font-size: 14px; line-height: 18px; text-underline-offset: 3px; text-decoration-color: var(--colorBaseBlue); .transition(text-decoration-color); display: block; flex-grow: 1; text-align: center;
	&:hover{text-decoration-color: transparent;}
	@media(max-width: @m){font-size: 13px; line-height: 16px;}
}
.field-newsletter{
	padding: 20px 80px 16px 20px !important; background: var(--colorLightBlueBackground); border-radius: var(--inputBorderRadius); position: relative; margin-bottom: 8px;
	&:after{.pseudo(48px, 48px); top: 50%; margin-top: -24px; right: 16px; background: url(assets/images/custom-icons/newsletter2.svg) center center no-repeat; background-size: contain;}
	input[type=checkbox] + label{
		padding-left: 34px;
		&:before{background: var(--colorWhite); border-color: var(--colorBaseBlue)}
	}
	input[type=checkbox]:checked + label:before{background: var(--colorBaseBlue);}
	@media(max-width: @m){
		padding: 16px 80px 12px 16px !important;
		input[type=checkbox] + label{font-size: 13px; line-height: 18px; padding-top: 0; padding-bottom: 0;}
		&:after{top: 16px; margin-top: 0;}
	}
}
form.loading{
	position: relative; &:before{.pseudo(auto,auto); background: rgba(255,255,255,0.5); top: 0; right: 0; bottom: 0; left: 0; z-index: 100;}
}
.autocomplete-container{
	border-top: 0!important; border-radius: 0 0 var(--inputBorderRadius) var(--inputBorderRadius); padding: 0 5px 5px 0;
	ul{
		&::-webkit-scrollbar{-webkit-appearance: none; width: 5px; background: var(--colorBorderLightForms); border-radius: var(--borderRadius);}
		&::-webkit-scrollbar-thumb{background-color: var(--colorBaseBlue); border-radius: 5px;box-shadow: 0 0 1px rgba(255,255,255,.5);}
	}
}
.field-tooltip{
	font-size: 12px; line-height: 1.2; color: var(--colorTextLightGray); padding: 5px 0 0 20px;
	@media (max-width: @m){padding-left: 15px;}
}
/*------- /forms -------*/

/*------- tables -------*/
.table{
	width: 100%; border-spacing: 0; margin: 10px 0; font-size: 14px;
	th{font-weight: bold; font-size: 14px; text-align: left; padding: 10px 15px; color: var(--colorBaseBlue);}
	td{padding: 10px 15px;}
	tbody tr:nth-child(even){background: var(--colorLightBlueBackground);}
}
.table-row{display: table; width: 100%;}
.table-col{display: table-cell;}
@media (max-width: @m){
	.table-wrapper {
		overflow-x: auto; -webkit-overflow-scrolling:touch; width: 100%; position: relative;
		/*
		&:before{ .pseudo(30px,28px); background-size: cover; bottom: 0; right: 4px;}
		&.ios{
			padding-bottom: 40px;
			&:before{.pseudo(30px,28px); background-size: cover; bottom: 0; right: 4px;}
		}
		*/
	}
}
/*------- /tables -------*/

/*------- info messages -------*/
.error{
	color: var(--colorRed); display: block; padding: 5px 0 0 20px; font-size: 12px; line-height: 17px;
	@media(max-width: @m){font-size: 11px; line-height: 15px; padding-left: 16px;}
}
.global-error, .global-success, .global-warning{
	display: flex; font-size: 14px; margin: 0 0 15px 0; line-height: 1.5; padding: 10px 15px; background: var(--colorRed); color: #fff; position: relative; border-radius: var(--inputBorderRadius);
	&:before{.icon-info3; font: 20px/1 var(--fonti); color: #fff; margin: 0.5px 8px 0 0;}
}
.global-success{
	background-color: var(--colorGreen);
	&:before{.icon-check;}
}
.global-warning{
	background-color: var(--colorOrange);
}
/*------- /info messages -------*/

/*------- buttons -------*/
.btn, input[type='submit'], button:not(.basic) {
	position: relative; display: inline-flex; cursor: pointer; justify-content: center; align-items: center; text-decoration: none; padding: 13px 24px; font-weight: bold; font-size: var(--fontSize); line-height: 1.4; border-radius: var(--inputBorderRadius); color: #fff; background: var(--colorBaseBlue); box-shadow: 0 8px 16px 0 rgba(0, 89, 194, 0.2); .transition(background,0.3s);
	&:disabled{cursor: default; pointer-events: none; opacity: .5;}
	&.loading{
		color: transparent!important; pointer-events: none;;
		.lds-ring-cnt{position: absolute; top: 0; right: 0; bottom: 0; left: 0; display: flex; align-items: center; justify-content: center;}
	}
	@media (max-width: @ms){display: block; text-align: center;}
	@media (min-width: @h){
		&:not(:disabled):hover {background: var(--colorDarkBlue); color: #fff;}
	}
}
.btn-white:not(.basic) {
	background: #fff; color: var(--colorBaseBlue); border: 1px solid #fff; padding: 12px 24px; .transition(border-color,0.3s);
	@media (min-width: @h){
		&:not(:disabled):hover {background: #fff; border-color: var(--colorBaseBlue); color: var(--colorBaseBlue);}
	}
}
.btn-border:not(.basic) {
	background: #fff; color: var(--colorBaseBlue); border: 1px solid var(--colorBaseBlue); padding: 12px 24px; box-shadow: none;
	@media (min-width: @h){
		&:not(:disabled):hover {background: var(--colorLightBlueBackground); color: var(--colorBaseBlue);}
	}
}
.btn-green:not(.basic) {
	background: var(--colorGreen); color: #fff; box-shadow: 0 8px 13px 0 rgba(9,180,108,0.23);
	@media (min-width: @h){
		&:not(:disabled):hover {background: #059059;}	
	}
}
/*------- /buttons -------*/

/*------- helpers -------*/
.wrapper {
	max-width: var(--pageWidth); margin: auto;
	@media (max-width: @t){padding: 0 40px;}
	@media (max-width: @m){padding: 0 16px;}
}
.cms-wrapper{
	display: flex;
	@media (max-width: @t){display: block;}
}
.main-content {
	background: #fff; max-width: 856px; padding: 56px 0 88px 80px; flex-grow: 1;
	@media (max-width: @t){margin: auto; padding: 40px 0 72px; max-width: 768px;}
	@media (max-width: @m){padding: 24px 0 56px;}
}
.first-title{margin-top: 0; padding-top: 0;}
.extra, .extra2{font-weight: bold; font-size: var(--fontSizeSpecial); line-height: 1.4;}
.extra{
	padding: 27px 32px 24px; border-radius: var(--inputBorderRadius); background: var(--colorBaseBlue); color: #fff; margin: 16px 0 24px;
	a{color: #fff;}
	@media (max-width: @ms){margin: 16px -16px 24px; border-radius: 0;}
}
.extra2{padding: 4px 0 14px;}
.list{
	list-style: none; padding: 0; margin: 0 0 16px 16px; line-height: 1.4;
	@media (max-width: @m){margin-left: 0;}
	li{
		position: relative; padding: 0 0 3px 16px;
		&:before{
			.pseudo(5px, 5px); border: 2px solid var(--colorBaseBlue); top: 8px; left: 0; border-radius: 100%;
			@media (max-width: @m){top: 7px;}
		}
	}
}
.cms-content, .base-modal.quick:not(.gallery){
	word-wrap: break-word;
	ul{.list;}
	ol{margin: 0 0 16px 32px;}
	.btn{margin: 10px 0;}
	img{
		border-radius: var(--inputBorderRadius); margin: 15px 0 10px;
		@media (max-width: @m){margin: 10px 0 6px;}
	}
	iframe{
		width: 100%; height: auto; display: block; aspect-ratio: 16 / 9; margin: 15px 0 22px;
		@media (max-width: @m){margin: 10px 0 18px;}
	}
}
.image-wrapper{
	font-size: 14px; text-align: center; padding-bottom: 12px; display: block;
	@media (max-width: @m){font-size: 12px; padding-bottom: 8px;}
	.image-title{display: block;}
}
.terms-link{color: var(--colorBase); text-decoration-color: var(--colorBaseBlue); text-underline-offset: 3px; display: inline-block; vertical-align: top; padding: 3px 0;}
/*------- /helpers -------*/

/*------- splide -------*/
.splide.is-overflow .splide__arrows {display: block;}
.splide__arrows {display: none;}
.splide__arrow{
	display: flex; align-items: center; justify-content: center; width: 50px; height: 50px; min-width: 0; background: transparent; border: none; border-radius: 0; padding: 0; font-size: 0; line-height: 0; position: absolute; top: 50%; border-radius: 50%; z-index: 1; box-shadow: none; cursor: pointer;
	&:before{.icon-arrow2(); font: 34px/1 var(--fonti); color: var(--colorWhite); position: absolute; z-index: 1; margin-left: -3px; .transition(color);}
	&>*{display: none;}
	@media (min-width: @h){
		&:hover{
			background: transparent;
			&:before{color: var(--colorBaseBlue);}
		}
	}

	@media (max-width: @t){
		width: 44px; height: 44px; background: var(--colorWhite); border: 1px solid var(--borderColor);
		&:before{font-size: 18px;}
	}
}
.splide__arrow--next{
	right: 53px; 
	&:before{transform: scaleX(-1); text-indent: -6px;}

	@media (max-width: @t){right: -23px;}
}
.splide__arrow--prev{
	left: 53px;

	@media (max-width: @t){left: -23px;}
}
.splide__arrow:disabled{display: none; cursor: default;}
.splide__pagination, .swiper-pagination{
	position: absolute; bottom: 48px; left: calc((100vw - var(--pageWidth)) / 2); right: 0; display: flex; align-items: center; justify-content: flex-start!important;
	li{display: block; position: relative; width: 14px; height: 14px; margin-right: 12px!important;}
	button{
		width: 14px; height: 14px; border-radius: 50%; background: rgba(255,255,255,0.4); padding: 0; box-shadow: 0 0 8px rgba(0,12,26,0.4);
		&.is-active{background: var(--colorBaseBlue);}	
		@media (min-width: @h){
			&:hover{background: var(--colorBaseBlue);}
		}
	}
	.splide__slide{
		display: block; position: relative; width: 14px; height: 14px; margin-right: 12px!important; border-radius: 50%; background: rgba(255,255,255,0.4); padding: 0; box-shadow: 0 0 8px rgba(0,12,26,0.4); cursor: pointer; z-index: 2; transition: background 0.3s;
		&.is-active{background: var(--colorBaseBlue);}	
		@media (min-width: @h){
			&:hover{background: var(--colorBaseBlue);}
		}
	}
	@media (max-width: @t){
		bottom: 40px; padding-left: 40px; padding-right: 40px;
		li, .splide__slide{width: 12px; height: 12px; margin-right: 10px!important;}
		button{width: 12px; height: 12px;}
		
	}
	@media (max-width: @m){
		bottom: 24px; padding-left: 24px; padding-right: 24px; left: 0;
		li, .splide__slide{margin-right: 8px!important;}
	}	
}
/*------- /splide -------*/

.base-loader{
	padding: 30px 0;
	svg{display: block; margin: auto;}
}
.image-loader{
	padding: 0;
	svg{width: 35px; height: auto;}
}