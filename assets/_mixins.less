.transition (@element: color, @speed: .3s) {
	transition: @arguments;
	-webkit-backface-visibility: hidden;
	-webkit-tap-highlight-color: transparent;
}
.grayscale (@bw: 100%) {
	filter: grayscale(@bw);
	-webkit-filter: grayscale(@bw);
	-moz-filter: grayscale(@bw);
}
.gradient(@startColor: #eee, @endColor: white) {
	background: @startColor;
	background-image: linear-gradient(180deg, @startColor 0%, @endColor 100%);
	background-image: -webkit-linear-gradient(180deg, @startColor 0%, @endColor 100%);
	background-image: -moz-linear-gradient(@startColor 0%, @endColor 100%);
	background-image: -ms-linear-gradient(180deg, @startColor 0%, @endColor 100%);
}
.horizontal-gradient (@startColor: #eee, @endColor: white) {
 	background-color: @startColor;
	background-image: -webkit-linear-gradient(90deg, @startColor, @endColor);
	background-image: -ms-linear-gradient(90deg, @startColor, @endColor);
	background-image: linear-gradient(90deg, @startColor, @endColor);
}
.pseudo(@width, @height) {
	content:"";
	position: absolute;
	display: block;
	width:@width;
	height:@height;
}
.animation (@name, @duration: 300ms, @delay: 0, @ease: ease) {
	-webkit-animation: @name @duration @delay @ease;
	-moz-animation:    @name @duration @delay @ease;
	-ms-animation:     @name @duration @delay @ease;
}
.transform(@string){
	transform: @string;
	-webkit-transform: @string;
	-ms-transform: 		 @string;
}
.scale (@factor) {
	transform: scale(@factor);
	-webkit-transform: scale(@factor);
	-ms-transform: 		 scale(@factor);
}
.scaleX(@factor) {
	transform: scaleX(@factor);
	-webkit-transform: scaleX(@factor);
	-ms-transform: 		 scaleX(@factor);	
}
.scaleY (@factor) {
	transform: scaleY(@factor);
	-webkit-transform: scaleY(@factor);
	-ms-transform: scaleY(@factor);
}
.rotate (@deg) {
	transform: rotate(@deg);
	-webkit-transform: rotate(@deg);
	-ms-transform: 		 rotate(@deg);
}
.translate (@x, @y:0) {
	transform: translate(@x, @y);
	-webkit-transform: translate(@x, @y);
	-ms-transform: translate(@x, @y);
}
.translate3d (@x, @y: 0, @z: 0) {
	transform: translate3d(@x, @y, @z);
	-webkit-transform: translate3d(@x, @y, @z);
	-ms-transform: translate3d(@x, @y, @z);
}
.clear() { 
	/**zoom: 1;*/ clear:both;
	&:before, &:after {content:""; display: table; }
	&:after { clear: both; }
}
.placeholder(@color,@focusColor) {
	&::-webkit-input-placeholder { color: @color; }
	&:-ms-input-placeholder { color: @color; }
	&::-moz-placeholder { color: @color; }
	&:focus {
		&::-webkit-input-placeholder { color: @focusColor; }
		&:-ms-input-placeholder { color: @focusColor; }
		&::-moz-placeholder { color: @focusColor; }
	}
}

// icons
.icon-truck2(){
  content: "\e912";
}
.icon-arrow4(){
  content: "\e904";
}
.icon-close() {
  content: "\e920";
}
.icon-pdf() {
  content: "\e918";
}
.icon-zoom-out() {
  content: "\e919";
}
.icon-zoom-in() {
  content: "\e91a";
}
.icon-check() {
  content: "\e91b";
}
.icon-minus() {
  content: "\e91c";
}
.icon-plus() {
  content: "\e91d";
}
.icon-service2() {
  content: "\e91e";
}
.icon-arrow3() {
  content: "\e91f";
}
.icon-arrow() {
  content: "\e916";
}
.icon-arrow2() {
  content: "\e917";
}
.icon-cards() {
  content: "\e900";
}
.icon-cart() {
  content: "\e901";
}
.icon-compare() {
  content: "\e902";
}
.icon-doc() {
  content: "\e903";
}
.icon-fb() {
  content: "\e905";
}
.icon-gift() {
  content: "\e906";
}
.icon-guarantee() {
  content: "\e907";
}
.icon-help() {
  content: "\e908";
}
.icon-info2() {
  content: "\e909";
}
.icon-link() {
  content: "\e90a";
}
.icon-mail() {
  content: "\e90b";
}
.icon-search() {
  content: "\e90c";
}
.icon-service() {
  content: "\e90d";
}
.icon-truck() {
  content: "\e90e";
}
.icon-user() {
  content: "\e90f";
}
.icon-viber() {
  content: "\e910";
}
.icon-whatsapp() {
  content: "\e911";
}
.icon-arrow-nav() {
  content: "\e913";
}
.icon-info3() {
  content: "\e914";
}
.icon-clear() {
  content: "\e921";
}
.icon-filters() {
  content: "\e915";
}
.icon-mobile() {
  content: "\e922";
}
.icon-store() {
  content: "\e923";
}
.icon-calculator() {
  content: "\e924";
}