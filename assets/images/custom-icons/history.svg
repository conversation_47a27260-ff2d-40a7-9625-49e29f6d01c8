<?xml version="1.0" encoding="UTF-8"?>
<svg width="38px" height="38px" viewBox="0 0 38 38" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>A27E64B2-656E-4F22-88F5-C3E07C73BF41</title>
    <defs>
        <polygon id="path-1" points="0 36.5714286 36.5714286 36.5714286 36.5714286 0 0 0"></polygon>
    </defs>
    <g id="Desktop" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Prijava" transform="translate(-1053.000000, -607.000000)">
            <g id="Content" transform="translate(424.000000, 200.000000)">
                <g id="Novi-ste-korisnik" transform="translate(598.000000, 40.000000)">
                    <g id="Benefiti" transform="translate(0.000000, 183.000000)">
                        <g id="3" transform="translate(0.000000, 161.000000)">
                            <g id="Group-15" transform="translate(18.000000, 10.000000)">
                                <g id="Group-4" transform="translate(13.000000, 13.000000)">
                                    <g id="to-do-list" transform="translate(0.714286, 0.714286)">
                                        <g id="g2853" transform="translate(18.285714, 18.285714) scale(-1, 1) rotate(-180.000000) translate(-18.285714, -18.285714) ">
                                            <mask id="mask-2" fill="white">
                                                <use xlink:href="#path-1"></use>
                                            </mask>
                                            <g id="path2859"></g>
                                            <path d="M30.6785714,0.535714286 L5.89285714,0.535714286 C5.14328571,0.535714286 4.53571429,1.14335714 4.53571429,1.89285714 L4.53571429,30.5506429 C4.53571429,31.3002143 5.14328571,31.9077857 5.89285714,31.9077857 L30.6785714,31.9077857 C31.4280714,31.9077857 32.0357143,31.3002143 32.0357143,30.5506429 L32.0357143,1.89285714 C32.0357143,1.14335714 31.4280714,0.535714286 30.6785714,0.535714286" id="path2865" fill="#FFFFFF" fill-rule="nonzero"></path>
                                            <path d="M18.2857143,29.1428571 L18.2857143,29.1428571 C17.5243571,29.1428571 16.9071429,29.7600714 16.9071429,30.5214286 L16.9071429,34.6571429 C16.9071429,35.4185 17.5243571,36.0357143 18.2857143,36.0357143 C19.0470714,36.0357143 19.6642857,35.4185 19.6642857,34.6571429 L19.6642857,30.5214286 C19.6642857,29.7600714 19.0470714,29.1428571 18.2857143,29.1428571" id="path2869" fill="#CCDEF3" fill-rule="nonzero"></path>
                                            <path d="M10.4276643,29.1428571 L10.4276643,29.1428571 C9.66630714,29.1428571 9.04909286,29.7600714 9.04909286,30.5214286 L9.04909286,34.6571429 C9.04909286,35.4185 9.66630714,36.0357143 10.4276643,36.0357143 C11.1890214,36.0357143 11.8062357,35.4185 11.8062357,34.6571429 L11.8062357,30.5214286 C11.8062357,29.7600714 11.1890214,29.1428571 10.4276643,29.1428571" id="path2873" fill="#CCDEF3" fill-rule="nonzero"></path>
                                            <path d="M26.1437643,29.1428571 L26.1437643,29.1428571 C25.3824071,29.1428571 24.7651929,29.7600714 24.7651929,30.5214286 L24.7651929,34.6571429 C24.7651929,35.4185 25.3824071,36.0357143 26.1437643,36.0357143 C26.9051214,36.0357143 27.5223357,35.4185 27.5223357,34.6571429 L27.5223357,30.5214286 C27.5223357,29.7600714 26.9051214,29.1428571 26.1437643,29.1428571" id="path2877" fill="#CCDEF3" fill-rule="nonzero"></path>
                                            <path d="M27.1242357,21.0226714 L15.8385214,21.0226714 C15.2073786,21.0226714 14.6956643,21.5343143 14.6956643,22.1655286 C14.6956643,22.7967429 15.2073786,23.3083857 15.8385214,23.3083857 L27.1242357,23.3083857 C27.75545,23.3083857 28.2670929,22.7967429 28.2670929,22.1655286 C28.2670929,21.5343143 27.75545,21.0226714 27.1242357,21.0226714" id="path2881" fill="#CCDEF3" fill-rule="nonzero"></path>
                                            <path d="M27.1242357,13.9360357 L15.8385214,13.9360357 C15.2073786,13.9360357 14.6956643,14.44775 14.6956643,15.0788929 C14.6956643,15.7101071 15.2073786,16.22175 15.8385214,16.22175 L27.1242357,16.22175 C27.75545,16.22175 28.2670929,15.7101071 28.2670929,15.0788929 C28.2670929,14.44775 27.75545,13.9360357 27.1242357,13.9360357" id="path2885" fill="#CCDEF3" fill-rule="nonzero"></path>
                                            <path d="M27.1242357,6.8494 L15.8385214,6.8494 C15.2073786,6.8494 14.6956643,7.36111429 14.6956643,7.99225714 C14.6956643,8.62347143 15.2073786,9.13511429 15.8385214,9.13511429 L27.1242357,9.13511429 C27.75545,9.13511429 28.2670929,8.62347143 28.2670929,7.99225714 C28.2670929,7.36111429 27.75545,6.8494 27.1242357,6.8494" id="path2889" fill="#CCDEF3" fill-rule="nonzero"></path>
                                            <path d="M18.2857143,29.1428571 L18.2857143,29.1428571 C17.5243571,29.1428571 16.9071429,29.7600714 16.9071429,30.5214286 L16.9071429,34.6571429 C16.9071429,35.4185 17.5243571,36.0357143 18.2857143,36.0357143 C19.0470714,36.0357143 19.6642857,35.4185 19.6642857,34.6571429 L19.6642857,30.5214286 C19.6642857,29.7600714 19.0470714,29.1428571 18.2857143,29.1428571 Z" id="path2901" stroke="#000C1A" stroke-width="0.931360219" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="31.54206848144531,2.173174095153809,17.19911956787109,0"></path>
                                            <path d="M10.4276643,29.1428571 L10.4276643,29.1428571 C9.66630714,29.1428571 9.04909286,29.7600714 9.04909286,30.5214286 L9.04909286,34.6571429 C9.04909286,35.4185 9.66630714,36.0357143 10.4276643,36.0357143 C11.1890214,36.0357143 11.8062357,35.4185 11.8062357,34.6571429 L11.8062357,30.5214286 C11.8062357,29.7600714 11.1890214,29.1428571 10.4276643,29.1428571 Z" id="path2905" stroke="#000C1A" stroke-width="0.931360219" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="31.54206848144531,2.173174095153809,17.19911956787109,0"></path>
                                            <path d="M26.1437643,29.1428571 C25.3824071,29.1428571 24.7651929,29.7600714 24.7651929,30.5214286 L24.7651929,34.6571429 C24.7651929,35.4185 25.3824071,36.0357143 26.1437643,36.0357143 C26.9051214,36.0357143 27.5223357,35.4185 27.5223357,34.6571429 L27.5223357,30.5214286 C27.5223357,29.7600714 26.9051214,29.1428571 26.1437643,29.1428571 Z" id="path2909" stroke="#000C1A" stroke-width="0.931360219" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="31.54206848144531,2.173174095153809,17.19911956787109,0"></path>
                                            <path d="M30.6785714,31.9077857 L27.5222857,31.9077857 L27.5222857,30.5214286 C27.5222857,29.7600714 26.9051429,29.1428571 26.1437857,29.1428571 C25.3824286,29.1428571 24.7651429,29.7600714 24.7651429,30.5214286 L24.7651429,31.9077857 L19.6642857,31.9077857 L19.6642857,30.5214286 C19.6642857,29.7600714 19.0470714,29.1428571 18.2857143,29.1428571 C17.5243571,29.1428571 16.9071429,29.7600714 16.9071429,30.5214286 L16.9071429,31.9077857 L11.8062857,31.9077857 L11.8062857,30.5214286 C11.8062857,29.7600714 11.189,29.1428571 10.4276429,29.1428571 C9.66628571,29.1428571 9.04914286,29.7600714 9.04914286,30.5214286 L9.04914286,31.9077857 L5.89285714,31.9077857 C5.14335714,31.9077857 4.53571429,31.3002143 4.53571429,30.5506429 L4.53571429,1.89285714 C4.53571429,1.14335714 5.14335714,0.535714286 5.89285714,0.535714286 L30.6785714,0.535714286 C31.4280714,0.535714286 32.0357143,1.14335714 32.0357143,1.89285714 L32.0357143,30.5506429 C32.0357143,31.3002143 31.4280714,31.9077857 30.6785714,31.9077857 Z" id="path2913" stroke="#000C1A" stroke-width="0.931360219" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="37.06813659667969,2.173174095153809,11.98350143432617,0"></path>
                                            <path d="M27.1242357,21.0226714 L15.8385214,21.0226714 C15.2073786,21.0226714 14.6956643,21.5343143 14.6956643,22.1655286 C14.6956643,22.7967429 15.2073786,23.3083857 15.8385214,23.3083857 L27.1242357,23.3083857 C27.75545,23.3083857 28.2670929,22.7967429 28.2670929,22.1655286 C28.2670929,21.5343143 27.75545,21.0226714 27.1242357,21.0226714 Z" id="path2917" stroke="#000000" stroke-width="0.931360219" stroke-linecap="round" stroke-linejoin="round"></path>
                                            <path d="M27.1242357,13.9360357 L15.8385214,13.9360357 C15.2073786,13.9360357 14.6956643,14.44775 14.6956643,15.0788929 C14.6956643,15.7101071 15.2073786,16.22175 15.8385214,16.22175 L27.1242357,16.22175 C27.75545,16.22175 28.2670929,15.7101071 28.2670929,15.0788929 C28.2670929,14.44775 27.75545,13.9360357 27.1242357,13.9360357 Z" id="path2921" stroke="#000C1A" stroke-width="0.931360219" stroke-linecap="round" stroke-linejoin="round"></path>
                                            <path d="M27.1242357,6.8494 L15.8385214,6.8494 C15.2073786,6.8494 14.6956643,7.36111429 14.6956643,7.99225714 C14.6956643,8.62347143 15.2073786,9.13511429 15.8385214,9.13511429 L27.1242357,9.13511429 C27.75545,9.13511429 28.2670929,8.62347143 28.2670929,7.99225714 C28.2670929,7.36111429 27.75545,6.8494 27.1242357,6.8494 Z" id="path2925" stroke="#000C1A" stroke-width="0.931360219" stroke-linecap="round" stroke-linejoin="round"></path>
                                            <polyline id="path2929" stroke="#000C1A" stroke-width="0.931360219" stroke-linecap="round" stroke-linejoin="round" points="8.79931429 22.9243143 9.81617143 21.2726714 13.1555286 25.2901"></polyline>
                                            <polyline id="path2933" stroke="#000C1A" stroke-width="0.931360219" stroke-linecap="round" stroke-linejoin="round" points="8.79931429 15.8528857 9.81617143 14.2012429 13.1555286 18.2186714"></polyline>
                                            <polyline id="path2937" stroke="#000C1A" stroke-width="0.931360219" stroke-linecap="round" stroke-linejoin="round" points="8.79931429 8.73381429 9.81617143 7.08217143 13.1555286 11.0996"></polyline>
                                        </g>
                                    </g>
                                </g>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>