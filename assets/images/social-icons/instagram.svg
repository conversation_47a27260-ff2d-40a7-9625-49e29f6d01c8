<?xml version="1.0" encoding="UTF-8"?>
<svg width="41px" height="40px" viewBox="0 0 41 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>EDFC5557-E25F-4828-B9C8-A7030917C253</title>
    <defs>
        <linearGradient x1="8.42204014%" y1="91.6080519%" x2="91.5779411%" y2="8.39109063%" id="linearGradient-1">
            <stop stop-color="#FFD600" offset="0%"></stop>
            <stop stop-color="#FF0100" offset="50%"></stop>
            <stop stop-color="#D800B9" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="8.3918274%" y1="91.5694345%" x2="91.6081538%" y2="8.42970888%" id="linearGradient-2">
            <stop stop-color="#FFD600" offset="0%"></stop>
            <stop stop-color="#FF0100" offset="50%"></stop>
            <stop stop-color="#D800B9" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="14.6448468%" y1="85.3231484%" x2="85.3554617%" y2="14.6770838%" id="linearGradient-3">
            <stop stop-color="#FF6400" offset="0%"></stop>
            <stop stop-color="#FF0100" offset="50%"></stop>
            <stop stop-color="#FD0056" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="14.6471982%" y1="85.3216833%" x2="85.3546499%" y2="14.6787791%" id="linearGradient-4">
            <stop stop-color="#F30072" offset="0%"></stop>
            <stop stop-color="#E50097" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Desktop" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="CMS---Uvjeti-kupnje" transform="translate(-416.000000, -4480.000000)">
            <g id="Group-25" transform="translate(0.000000, 3224.000000)">
                <g id="Desktop-/-Footer-/-Bez-Loyalty" transform="translate(0.000000, 560.000000)">
                    <g id="Group-3" transform="translate(368.000000, 696.000000)">
                        <g id="Insta" transform="translate(48.000000, 0.000000)">
                            <ellipse id="Oval-Copy-2" stroke="url(#linearGradient-1)" stroke-width="2" cx="20.2463687" cy="20" rx="18.9927374" ry="19"></ellipse>
                            <g id="instagram" transform="translate(11.027863, 10.769231)" fill-rule="nonzero">
                                <path d="M18.4145909,5.42503388 C18.3696388,4.44245426 18.2135041,3.77143803 17.9852206,3.18436925 C17.7534141,2.56842593 17.3898515,2.01051337 16.9200383,1.54936579 C16.458821,1.07976708 15.9005127,0.716229539 15.284288,0.484388094 C14.6968102,0.256349612 14.0256284,0.100427064 13.0426,0.0557771683 C12.0577397,0.0107047091 11.7430747,0 9.23505587,0 C6.72703698,0 6.41237208,0.0107047091 5.42751178,0.0554954474 C4.44448337,0.100427064 3.77330155,0.256490454 3.18582372,0.484669779 C2.56959906,0.716370417 2.01143167,1.07976708 1.55007346,1.54936579 C1.08026027,2.01037252 0.716556679,2.56828505 0.48460934,3.1842284 C0.2564667,3.77143803 0.100472935,4.44245426 0.0558026447,5.42489303 C0.0107095985,6.40944455 0,6.72382495 0,9.23069881 C0,11.7377135 0.0107095985,12.0522348 0.0558026447,13.0366454 C0.100613841,14.0190842 0.25674855,14.6901004 0.48503206,15.2773101 C0.716838528,15.8931125 1.08040121,16.4511659 1.55021437,16.9121727 C2.01143167,17.3817713 2.56973997,17.745168 3.18596462,17.9768687 C3.77330155,18.2051888 4.44462427,18.3611114 5.42765268,18.406043 C6.41265393,18.4509746 6.72717793,18.4615385 9.23519677,18.4615385 C11.7432157,18.4615385 12.0578806,18.4509746 13.0427409,18.406043 C14.0257693,18.3611114 14.6969511,18.2051888 15.2844289,17.9768687 C16.5249105,17.4974104 17.5055433,16.5172253 17.9852206,15.2773101 C18.213645,14.6901004 18.3696388,14.0190842 18.4145909,13.0366454 C18.4594021,12.0520939 18.4701117,11.7377135 18.4701117,9.23083965 C18.4701117,6.72382495 18.4594021,6.40944455 18.4145909,5.42503388 Z M16.7522076,12.9610082 C16.7112011,13.8610489 16.560703,14.3498034 16.4343016,14.6750293 C16.1235823,15.4801361 15.4870658,16.116362 14.6815913,16.4269395 C14.3562168,16.5532832 13.867239,16.7037126 12.9667873,16.7447003 C11.9932002,16.7892094 11.7010818,16.7985056 9.23505587,16.7985056 C6.76888899,16.7985056 6.47691152,16.7892094 5.50318353,16.7447003 C4.60287269,16.7037126 4.11389495,16.5532832 3.78837955,16.4269395 C3.38733328,16.2789046 3.02447517,16.0429785 2.72657921,15.7363449 C2.41980559,15.4385849 2.18377167,15.0760334 2.03566922,14.6750293 C1.90926775,14.3498034 1.75876971,13.8610489 1.71776325,12.9610082 C1.67337477,11.9877248 1.66393341,11.6957398 1.66393341,9.23098049 C1.66393341,6.76608038 1.67337477,6.47423621 1.71776325,5.50081197 C1.75891062,4.60077128 1.90926775,4.11201678 2.03566922,3.78679082 C2.18377167,3.38578682 2.4199465,3.02309436 2.72657921,2.72533441 C3.02447517,2.41870084 3.38733328,2.18277468 3.78852045,2.03488069 C4.11389495,1.90839609 4.60287269,1.7581076 5.50332448,1.71697901 C6.47705243,1.6726108 6.76917081,1.66317375 9.23505587,1.66317375 L9.23491496,1.66317375 C11.7008,1.66317375 11.9929184,1.6726108 12.9667873,1.71711985 C13.867239,1.7581076 14.3560758,1.90853696 14.6815913,2.03488069 C15.0826375,2.18291552 15.4454957,2.41884169 15.7433916,2.72533441 C16.0501652,3.02309436 16.2861992,3.38578682 16.4341607,3.78679082 C16.560703,4.11201678 16.7112011,4.60077128 16.7522076,5.50081197 C16.7965961,6.47409536 16.8060374,6.76608038 16.8060374,9.23083965 C16.8060374,11.6957398 16.796737,11.9875839 16.7522076,12.9610082 Z" id="Shape" fill="url(#linearGradient-2)"></path>
                                <path d="M9.23491496,4.49062544 C6.61585445,4.49062544 4.49267654,6.6129749 4.49267654,9.23083965 C4.49267654,11.8487044 6.61585445,13.970913 9.23491496,13.970913 C11.8541163,13.970913 13.9772942,11.8487044 13.9772942,9.23083965 C13.9772942,6.6129749 11.8541163,4.49062544 9.23491496,4.49062544 Z M9.23491496,12.3077392 C7.53490712,12.3075984 6.15660995,10.9300714 6.15675089,9.23069881 C6.15675089,7.5314671 7.53490712,6.15379918 9.23505587,6.15379918 C10.9352046,6.15394006 12.3133608,7.5314671 12.3133608,9.23069881 C12.3133608,10.9300714 10.9350637,12.3077392 9.23491496,12.3077392 Z" id="Shape" fill="url(#linearGradient-3)"></path>
                                <path d="M15.2728738,4.30343389 C15.2728738,4.91515168 14.7767094,5.41108957 14.1647122,5.41108957 C13.5525741,5.41108957 13.0564097,4.91515168 13.0564097,4.30343389 C13.0564097,3.69157525 13.5525741,3.19563736 14.1647122,3.19563736 C14.7767094,3.19563736 15.2728738,3.69157525 15.2728738,4.30343389 L15.2728738,4.30343389 Z" id="Path" fill="url(#linearGradient-4)"></path>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>