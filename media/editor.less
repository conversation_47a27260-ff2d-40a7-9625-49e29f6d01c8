@import "../assets/_vars.less";
@import "https://fonts.googleapis.com/css2?family=Inter:wght@400;700&display=block";
:root{
	// layout
	--pageWidth: 1280px;
	
	// colors
	--colorWhite: #fff;
	--colorBaseBlue: #0059c2;
	--colorBorderLightForms: #E5E6E8;
	--colorLightBlueBackground: #F4F6FA;
	--colorBase: #000C1A;
	--colorTextLightGray: #7F858C;
	--colorLightGray: #EFF0F1;
	--colorGrayFooter: #C7CACD;
	--colorYellow: #FFCC00;
	--colorOrange: #FFA200;
	--colorDarkBlue: #00428F;
	--colorBaseDarker: #0051B0;
	--colorBaseLighter: #3E82D2;
	--colorIconLightBlue: #CCDEF3;
	--colorIconMiddleBlue: #CCDEF3;
	--colorGreen: #09B46C;
	--colorLightGreen: #00FFB0;
	--colorRed: #E5454E;
	
	// text
	--font: "Inter", sans-serif;
	--fonti: "icomoon";
	--fontSizeDisplayBig: 56px;
	--fontSizeDisplaySmall: 48px;
	--fontSizeH1: 39px;
	--fontSizeH2: 32px;
	--fontSizeH3: 28px;
	--fontSizeH4: 24px;
	--fontSizeSpecial: 20px;
	--fontSize: 16px;
	--fontSizeSmall: 14px;
	--fontSizeLabel: 12px;
	--fontSizeLabelSmall: 11px;
	--lineHeight: 1.5;

	// forms
	--inputHeight: 56px;
	--inputPadding: 0 20px;
	--inputBorderRadius: 4px;
}

/*------- helpers -------*/
.float-left { float: left; }
.float-right { float: right; }
.strong { font-weight: bold; }
.italic { font-style: italic; }
.uppercase { text-transform: uppercase; }

.first{margin-left:0 !important;}
.last{margin-right:0 !important;}

.image-left, .alignleft { float: left; margin: 5px 20px 10px 0px; }
.image-right, .alignright { float: right; margin: 5px 0px 10px 20px; }

.align-left {text-align:left;}
.align-right {text-align:right;}
.center {text-align:center;}

.underline { text-decoration:underline; }
.nounderline { text-decoration:none; }
.rounded { border-radius: var(----inputBorderRadius);}

.red {color: var(--colorRed);}
.green {color: var(--colorGreen);}
.orange {color: var(--colorOrange);}

.first-title { margin-top: 0; padding-top: 0; }
/*------- /helpers -------*/

/*------- selectors -------*/
* { margin: 0; padding: 0; border: none; }
body{background: #fff; padding: 10px 15px; color: var(--textColor); font: var(--fontSize)/var(--lineHeight) var(--font);}
a, .link{color: var(--colorBaseBlue); text-decoration: underline;}
ul, ol{margin: 0; padding: 0;}
h1, h2, h3, h4{
	padding: 20px 0 12px; font-family: var(--font); font-weight: bold; line-height: 1.3;
	a, a:hover{text-decoration: none;}
}
h1{font-size: var(--fontSizeH1); padding: 0 0 24px;}
h2{font-size: var(--fontSizeH2);}
h3{font-size: var(--fontSizeH3);}
h4{font-size: var(--fontSizeH4);}
p{padding-bottom: 20px;}
/*------- /selectors -------*/

/*------- tables -------*/
table{
	border-top: 1px dotted #ccc; border-left: 1px dotted #ccc;
	td{border-bottom: 1px dotted #ccc; border-right: 1px dotted #ccc; padding: 10px 15px;}
}
.table{
	width: 100%; border-spacing: 0; margin: 10px 0; font-size: 14px;
	th{font-weight: bold; font-size: 14px; text-align: left; padding: 10px 15px; color: var(--colorBaseBlue);}
	td{padding: 10px 15px;}
	tbody tr:nth-child(even){background: var(--colorLightBlueBackground);}
}
/*------- /tables -------*/

/*------- buttons -------*/
.btn {
	position: relative; display: inline-flex; justify-content: center; align-items: center; text-decoration: none; padding: 13px 24px; font-weight: bold; font-size: var(--fontSize); line-height: 1.4; border-radius: var(--inputBorderRadius); color: #fff; background: var(--colorBaseBlue); box-shadow: 0 8px 16px 0 rgba(0, 89, 194, 0.2); margin: 10px 0;
}
.btn-white {
	background: #fff; color: var(--colorBaseBlue); border: 1px solid #fff; padding: 12px 24px;
}
.btn-border {
	background: #fff; color: var(--colorBaseBlue); border: 1px solid var(--colorBaseBlue); padding: 12px 24px; box-shadow: none;
}
.btn-green {
	background: var(--colorGreen); color: #fff; box-shadow: 0 8px 13px 0 rgba(9,180,108,0.23);
}
/*------- /buttons -------*/

/*------- extras -------*/
.extra, .extra2{font-weight: bold; font-size: var(--fontSizeSpecial); line-height: 1.4;}
.extra{padding: 27px 32px 24px; border-radius: var(--inputBorderRadius); background: var(--colorBaseBlue); color: #fff; margin: 16px 0 24px;}
.extra2{padding: 4px 0 14px;}
/*------- /extras -------*/
