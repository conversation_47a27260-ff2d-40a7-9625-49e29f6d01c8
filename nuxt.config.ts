// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
	extends: ['../nuxt-base'],
	css: ['@/assets/style.less'],
	vite: {
		css: {
			preprocessorOptions: {
				less: {
					additionalData: `@import "@/assets/_vars.less"; @import "@/assets/_mixins.less";`,
				},
			},
		},
		define: {
			__GTM_TRACKING__: JSON.stringify(true),
			__FB_CAPI_TRACKING__: JSON.stringify(true),
			__HAPI_TRACKING__: JSON.stringify(true),
			__CATALOG_MAIN_LANDING__: JSON.stringify(true),
			__LOCATIONS__: JSON.stringify(true),
		},
	},
	runtimeConfig: {
		fbPixelIds: {
			default: '515720375820902',
		},
		fbAccessToken: 'EAAMUJ8ZAdcckBO5i9CbB4PKIkoqYqNqhXarkjAB6DNUHM3muKUjMSZBuseXXdu4XlZB5vnLYTWjVGHcov6jUI4QAzOCAdxZBqpP1CLCLBAwTZBtKL5gqbQUYguslJeuLC4kbskjLIOkNHYh8dtrmNDBUZCPztxy1pjXt4NyiZBZB6pjTOT5kGXZBgB8CtFQZBOvbj7RwZDZD',
	},
	//modules: ['@sentry/nuxt/module'],
	modules: ['@nuxtjs/google-fonts', 'nuxt-vitalizer'],
	vitalizer: {
		disablePrefetchLinks: true,
		disablePreloadLinks: true,
		disableStylesheets: true,
	},
	googleFonts: {
		display: 'swap',
		subsets: 'latin',
		families: {
			'Inter': [400, 700],
		},
	},
	/*
	sentry: {
		sourceMapsUploadOptions: {
			org: 'marker-doo',
			project: 'm-bikeshop',
			authToken: '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',
		},
	},
	*/
	dir: {
		'public': 'media',
	},
	compressPublicAssets: {
		gzip: true,
		brotli: true,
	},
	$production: {
		sourcemap: {
			server: false,
			client: false,
		},
		routeRules: {
			'*': {
				swr: 120,
				cache: {base: 'db'},
			},
		},
	},
	nitro: {
		minify: true,
		storage: {
			db: {
				driver: 'redis',
				host: 'redis_mbikeshop_markerdev_info_nuxt',
				port: 6379,
				username: 'default',
				password: 'LqBKEDGSBM',
				base: 'mbike_dev', // mp: mbike, dev: mbike_dev
			},
		},
		devStorage: {
			db: {
				driver: 'fs',
				base: '.app_cache_data',
			},
		},
	},
	devtools: {
		enabled: false,
	},
	experimental: {
		asyncContext: true,
		checkOutdatedBuildInterval: 900000,
		emitRouteChunkError: 'automatic-immediate',
	},
	compatibilityDate: '2024-07-23',
});
