<template>
	<ClientOnly>
		<div class="auth-layout">
			<div class="wrapper cms-wrapper auth-wrapper">
				<AuthAsideMenu />
				<div class="auth-content">
					<slot name="authContent" />
				</div>
			</div>
		</div>
		<template #fallback>
			<BaseThemeUiLoading />
		</template>
	</ClientOnly>
</template>

<style lang="less">
	@media(max-width: @m){
		.page-auth{
			.d-categories{display: none !important;}
		}
	}
</style>

<style lang="less" scoped>
	.auth-layout{position: relative;}
	.auth-content{
		flex-grow: 1; background: var(--colorWhite); max-width: 100%; padding: 56px 0 88px 80px;
		:deep(h1){font-size: var(--fontSizeH2); padding-bottom: 20px;}
		@media(max-width: @t){
			padding: 33px 30px 40px;
			:deep(h1){padding-bottom: 16px; display: none;}
		}
		@media(max-width: @m){padding: 32px 0 40px;}
	}
	.page-auth-change_password{
		.auth-content{max-width: 480px;}
		@media(max-width: @m){
			.auth-content{max-width: 100%; padding-right: 16px; padding-left: 16px;}
		}
	}
	.auth-wrapper{
		@media(max-width: @t){padding: 0;}
	}
</style>
