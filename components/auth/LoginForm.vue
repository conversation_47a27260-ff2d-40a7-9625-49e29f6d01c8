<template>
	<BaseCmsLabel tag="div" class="login-subtitle" code="login_subtitle" />
	<BaseAuthLoginForm class="form-animated-label" v-slot="{loading, fields, formError, urls}" :submit-url="mode == 'checkout' ? 'webshop_customer' : 'auth_my_webshoporder'">
		<div class="global-error" v-if="formError">
			<BaseCmsLabel :code="formError" />
		</div>
		<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage, floatingLabel}">
			<p class="field" :class="['field-' + item.name, {'ffl-floated': floatingLabel}, {'err': errorMessage}]">
				<BaseFormInput :id="mode == 'userBox' ? item.name+'ub' : item.name" />
				<BaseCmsLabel tag="label" :for="mode == 'userBox' ? item.name+'ub' : item.name" :code="item.name" />
				<span class="error" v-show="errorMessage" v-html="errorMessage" />
			</p>
		</BaseFormField>
		<div class="form-login-forgotten-cnt">
			<button type="submit" class="login-button" :class="{'loading': loading}"><UiLoader v-if="loading" /><BaseCmsLabel code="login" /></button>
			<NuxtLink :to="urls.auth_forgotten_password" class="link-forgotten-password" data-auth_login="forgotten_password" data-siteform_response="show_hide" data-siteform_response_hide="-1">
				<BaseCmsLabel code="forgotten_password_userbox" />
			</NuxtLink>
		</div>
	</BaseAuthLoginForm>
</template>

<script setup>
	const props = defineProps(['mode']);
</script>

<style scoped lang="less">
	.login-subtitle{font-weight: bold; padding-bottom: 8px; color: var(--colorBase); line-height: 24px; font-size: var(--fontSize);}
	.login-button{
		@media(max-width: @m){width: 40%; min-width: 136px;}
	}
	.field-remember_me{
		input[type=checkbox] + label{padding-left: 34px;}
	}
</style>
