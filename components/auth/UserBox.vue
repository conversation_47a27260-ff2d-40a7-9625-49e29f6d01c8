<template>
	<BaseAuthUser v-slot="{isLoggedIn, urls}">
		<div class="aw" :class="[{'active': userBoxDropdown}]" ref="aw">
			<NuxtLink v-show="mobileBreakpoint" :to="urls.auth" class="btn-header btn-aw">
				<span class="btn-header-text"><BaseCmsLabel code="header_btn_my_acc" /></span>
			</NuxtLink>
			<div v-show="!mobileBreakpoint" class="btn-header btn-aw" @click="userBoxDropdown = !userBoxDropdown">
				<span class="btn-header-text"><BaseCmsLabel code="header_btn_my_acc" /></span>
			</div>
			<ClientOnly>
				<div v-if="isLoggedIn" class="aw-popup loggedin">
					<NuxtLink :to="urls.auth_my_webshoporder">
						<BaseCmsLabel tag="span" code="my_orders" />
					</NuxtLink>
					<NuxtLink :to="urls.auth_my_webshopcoupon">
						<BaseCmsLabel tag="span" code="my_coupons" />
					</NuxtLink>
					<NuxtLink :to="urls.auth_edit">
						<BaseCmsLabel tag="span" code="edit_profile" />
					</NuxtLink>
					<NuxtLink :to="urls.auth_change_password">
						<BaseCmsLabel tag="span" code="change_password" />
					</NuxtLink>
					<NuxtLink :to="`${urls.auth_logout}?redirect=/`">
						<BaseCmsLabel tag="span" code="logout" />
					</NuxtLink>
				</div>
				<div v-else class="aw-popup user-logout not-loggedin">
					<LazyAuthSocialLogin mode="userBox" />
					<LazyAuthLoginForm mode="userBox" />
					<div class="aw-signup">
						<BaseCmsLabel tag="div" class="aw-signup-title" code="new_user" />
						<NuxtLink :to="urls.auth_signup" class="btn btn-white btn-signup-on-login">
							<BaseCmsLabel code="signup_on_login" />
						</NuxtLink>
					</div>
				</div>
			</ClientOnly>
		</div>
	</BaseAuthUser>
</template>

<script setup>
	const {onClickOutside, onMediaQuery} = useDom();

	// close dropdown when clicked outside
	const userBoxDropdown = ref(false);
	const aw = ref();
	onClickOutside(aw, () => {
		userBoxDropdown.value = false;
	});

	// rwd
	const {matches: mobileBreakpoint} = onMediaQuery({
		query: '(max-width: 1300px)',
	});

	// close dropdown on route change
	const route = useRoute();
	watch(
		() => route.fullPath,
		() => {
			userBoxDropdown.value = false;
		}
	);
</script>

<style lang="less" scoped>
	.aw {
		position: relative; height: 100%;
		&.active {
			.btn-header{background: var(--colorDarkBlue);}
			.aw-popup,
			.aw-login-section{display: flex;}
			.btn-header:after{.pseudo(10px,10px); bottom: -6px; background: var(--colorWhite); .rotate(45deg); z-index: 1112;}
		}
		@media (max-width: @m){display: none; order: 1;}
	}
	.btn-aw:before {
		.icon-user();
		font: 20px/1 var(--fonti);
		@media (max-width: @t){font-size: 16px;}
	}
	.aw-close{
		width: 30px; height: 30px; background: var(--colorWhite); display: flex; align-items: center; justify-content: center; position: absolute; top: 20px; right: 20px; cursor: pointer;
		&:after{.pseudo(auto,auto); .icon-close(); font: 14px/14px var(--fonti); color: var(--colorRed);}
	}
	.aw-popup {
		padding: 30px 30px 38px; min-width: 148px; display: none; flex-direction: column; position: absolute; top: 80px; left: 50%; background: var(--colorWhite); font-size: 14px; line-height: 18px; font-weight: normal; box-shadow: 0 16px 40px 0 rgba(0, 12, 26, 0.16); transform: translateX(-50%); z-index: 1111; border-bottom: 4px solid var(--colorBaseBlue);
		@media (max-width: @t){display: none!important;}
		/*
		&:before {
			.pseudo(auto,20px); left: 0; right: 0; top: -20px; z-index: 1;
		}
		*/
		&.loggedin{
			align-items: center;
			a {
				text-decoration: none; color: var(--colorBase); margin-bottom: 12px; .transition(color);
				&:hover {
					color: var(--colorBaseBlue);
				}
				&:last-child{margin-bottom: 0;}
			}
		}
		&.not-loggedin{
			min-width: 416px; padding: 30px 48px 40px; text-align: left; left: 100%;
			@media (max-width: @l){left: 35px;}
			@media (max-width: @t){padding: 20px 25px 30px; min-width: 330px;}
			form{width: 100%;}
		}
		@media (max-width: @m){display: none;}
	}

	//aw signup
	.aw-signup{width: 100%; padding-top: 27px;}
	.aw-signup-title{font-size: 20px; line-height: 28px; font-weight: bold; padding-bottom: 13px;}
	.btn-signup-on-login{width: 100%;}

	@media (max-width: @m){
		.active-nav-m{
			.aw{
				width: 100%; display: flex; align-items: center; height: auto;
				a{
					padding: 14px 16px 17px;
					&:before{.pseudo(20px, 23px); left: 21px; background: url(assets/images/custom-icons/auth.svg) center center no-repeat; background-size: contain;}
				}
			}
		}
	}
</style>
