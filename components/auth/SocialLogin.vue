<template>
	<!-- FIXME social login - za sad su hardko<PERSON><PERSON> gumbi https://markerdoo.eu.teamwork.com/app/tasks/26070218 -->
	<div class="auth-social" :class="{'auth-social-checkout': mode == 'checkout', 'auth-social-user-box': mode == 'userBox', 'auth-social-login': mode == 'login', 'auth-social-register': mode == 'register'}">
		<BaseCmsLabel v-if="mode == 'register'" class="login-social-title register-social-title" tag="div" code="register_social_title" />
		<BaseCmsLabel v-else class="login-social-title" tag="div" code="login_social_title" />
		<BaseAuthSocialLogin v-slot="{socialLogin, loadingFacebook, loadingGoogle}">
			<div class="auth-social-icons">
				<button class="btn btn-border btn-facebook" :class="{'loading': loadingFacebook}" @click="socialLogin('Facebook')"><UiLoader v-if="loadingFacebook" />Facebook</button>
				<button class="btn btn-border btn-google" :class="{'loading': loadingGoogle}" @click="socialLogin('Google')"><UiLoader v-if="loadingGoogle" />Google</button>
			</div>
		</BaseAuthSocialLogin>
	</div>
</template>

<script setup>
	const props = defineProps(['mode']);
</script>

<style lang="less" scoped>
	.btn.loading:after{opacity: 0;}
	:deep(.lds-ring) div{border-color: var(--colorBaseBlue) transparent transparent transparent;}
	.auth-social{padding-bottom: 16px;}
	.login-social-title{font-size: 20px; line-height: 28px; font-weight: bold; padding-bottom: 13px;}
	.auth-social-icons{
		display: flex; width: 100%;
		.btn{
			width: 100%; padding: 0; min-height: 56px; display: flex; align-items: center; justify-content: center; font-size: 0; line-height: 0; border-color: var(--colorBorderLightForms); margin-right: 8px; position: relative;
			&:after{.pseudo(78px,15px); background: url(assets/images/facebook.svg) top left no-repeat; background-size: contain;}
			&:last-child{margin-right: 0;}
		}
		.btn-google{
			&:after{.pseudo(60px,19px); background: url(assets/images/google.svg) top left no-repeat; background-size: contain;}
		}
	}

	//social login
	.auth-social-login,.auth-social-register,.auth-social-checkout{
		padding-bottom: 24px;
		.login-social-title{
			font-size: 24px; line-height: 33px; padding-bottom: 12px;
			@media(max-width: @m){font-size: 19px; line-height: 22px; padding-bottom: 8px;}
		}
		.auth-social-icons{
			width: auto;
			.btn{
				width: 128px; min-height: 48px;
				&:after{width: 72px; height: 14px;}
			}
			.btn-google:after{width: 56px; height: 18px;}
			@media(max-width: @m){
				.btn{width: 100%;}
			}
		}
	}

	//checkout login
	.auth-social-checkout{padding-top: 48px;}
</style>
