<template>
	<div class="auth-sidebar">
		<BaseAuthUser v-slot="{urls, user, currentUrl}">
			<div class="auth-aside-intro">
				<BaseCmsLabel tag="div" class="auth-myprofile" code="header_btn_my_acc" />
				<div class="auth-fullname">{{user?.first_name}} {{user?.last_name}}</div>
			</div>
			<ul class="auth-sidebar-nav" ref="sidebarNavList" id="sidebarNavListId">
				<li class="auth-li auth-li-orders" :class="{'active': urls.auth_my_webshoporder == currentUrl}">
					<NuxtLink :to="urls.auth_my_webshoporder">
						<BaseCmsLabel tag="span" code="my_orders" />
					</NuxtLink>
				</li>
				<li class="auth-li auth-li-coupons" :class="{'active': urls.auth_my_webshopcoupon == currentUrl}">
					<NuxtLink :to="urls.auth_my_webshopcoupon">
						<BaseCmsLabel tag="span" code="my_coupons" />
					</NuxtLink>
				</li>
				<li class="auth-li auth-li-edit-profile" :class="{'active': urls.auth_edit == currentUrl}">
					<NuxtLink :to="urls.auth_edit">
						<BaseCmsLabel tag="span" code="edit_profile" />
					</NuxtLink>
				</li>
				<li class="auth-li auth-li-change-password" :class="{'active': urls.auth_change_password == currentUrl}">
					<NuxtLink :to="urls.auth_change_password">
						<BaseCmsLabel tag="span" code="change_password" />
					</NuxtLink>
				</li>
				<li class="auth-li-logout">
					<NuxtLink :to="urls.auth_logout+'?redirect=/'">
						<BaseCmsLabel tag="span" code="logout" />
					</NuxtLink>
				</li>
			</ul>
		</BaseAuthUser>
	</div>
</template>

<script setup>
	const sidebarNavList = ref();

	function scrollToActiveItem() {
		if (sidebarNavList.value) {
			const activeItem = sidebarNavList.value.querySelector('.auth-li.active');
			if (activeItem) {
				const container = sidebarNavList.value;
				let scrollLeft = activeItem.getBoundingClientRect().left - container.getBoundingClientRect().left - 45;

				container.scrollTo({
					left: scrollLeft,
					behavior: 'smooth'
				});
			}
		}
	};

	onMounted(() => {
		scrollToActiveItem();
	});
</script>

<style lang="less" scoped>
	.auth-fullname{
		font-size: 20px; line-height: 28px; font-weight: bold;
		@media(max-width: @t){font-size: 19px; line-height: 26px;}
	}
	.auth-sidebar {
		position: relative; width: 320px; padding: 56px 80px 80px 0; flex: 1 1 auto; flex-shrink: 0; max-width: 320px;
		&:before {.pseudo(auto,auto); top: 0; left: -50vw; right: 0; bottom: 0; background: #f4f6fa; z-index: -1;}
		@media(max-width: @t){
			width: 100%; padding: 30px 30px 0; background: unset; max-width: unset; flex: unset;
			&:before{display: none;}
		}
		@media(max-width: @m){padding: 16px 16px 0;}
	}
	.auth-aside-intro{
		padding-bottom: 23px;
		@media(max-width: @t){padding-bottom: 16px; text-align: center;}
		@media(max-width: @m){text-align: left;}
	}
	.auth-myprofile{
		text-transform: uppercase; color: var(--colorBaseBlue); font-size: var(--fontSizeLabel); line-height: var(--lineHeight); font-weight: bold;
		@media(max-width: @t){padding-bottom: 2px;}
	}
	.auth-sidebar-nav {
		background: var(--colorWhite); font-size: var(--fontSizeSmall); line-height: var(--lineHeight); border-radius: var(--inputBorderRadius); margin: 0; box-shadow: 0 0 40px 0 rgba(0,89,194,0.1); list-style: none;
		a {
			text-decoration: none; color: var(--colorBase); display: block; width: 100%; padding: 8px 24px; font-weight: bold; .transition(color); margin-top: -1px; border-top: 1px solid transparent; border-bottom: 1px solid transparent;
			&:hover{color: var(--colorBaseBlue);}
			span{
				position: relative; padding-left: 34px; min-height: 34px; display: flex; align-items: center;
				&:before{.pseudo(18px,18px); background: url('/assets/images/custom-icons/cart.svg') center no-repeat; background-size: contain; display: flex; align-items: center; justify-content: center; left: 4px; top: 7px;}
			}
		}
		li{
			&:first-child{
				a{border-top: unset;}
			}
		}
		.router-link-active {color: var(--colorBaseBlue); border-color: var(--colorBorderLightForms); border-top: 1px solid var(--colorBorderLightForms);}
		@media(max-width: @t){
			display: flex; margin: 0 -30px; border-radius: unset; border-top: 1px solid var(--colorBorderLightForms); border-bottom: 1px solid var(--colorBorderLightForms); box-shadow: unset;
			li{flex-grow: 1; font-size: 13px;}
			a{
				border: unset !important; display: flex; justify-content: center; margin-top: 0; position: relative;
				&:after{.pseudo(auto,2px); left: 0; right: 0; bottom: 0; background: var(--colorBaseBlue); border-radius: 1px; visibility: hidden; opacity: 0;}
				span{
					padding-left: 25px;
					&:before{width: 16px; height: 16px; left: 3px; top: 7px;}
				}
				&.router-link-active{
					&:after{visibility: visible; opacity: 1;}
				}
			}
		}
		@media(max-width: @m){margin: 0 -16px; padding: 0 16px;}
		@media(max-width: @ms){
			overflow-x: auto; overflow-y: unset;
			&::-webkit-scrollbar{display: none;}
			&::-webkit-scrollbar-thumb{display: none;}
			li{
				flex-grow: unset; margin-right: 18px; flex-shrink: 0;
				&:last-child{margin-right: 0}
			}
			a{
				padding: 8px 0; flex-shrink: 0; position: relative;
			}
		}
	}
	.auth-li-orders{
		&.router-link-active{
			a{border-top: unset !important}
		}
	}
	.auth-li-coupons{
		a{
			span{
				&:before{.pseudo(20px,14px); background: url('/assets/images/custom-icons/coupons.svg') center no-repeat; background-size: contain; top: 11px}
			}
		}
		@media(max-width: @t){
			a{
				span:before{width: 16px; height: 11px; left: 2px; top: 11px;}
			}
		}
	}
	.auth-li-edit-profile{
		a{
			span{
				&:before{.pseudo(20px,17px); background: url('/assets/images/custom-icons/user.svg') center no-repeat; background-size: contain; left: 6px; top: 9px;}
			}
		}
		@media(max-width: @t){
			a{
				span:before{width: 19px; height: 16px; left: 0; top: 8px;}
			}
		}
	}
	.auth-li-change-password{
		a{
			span{
				&:before{.pseudo(15px,19px); background: url('/assets/images/custom-icons/lock.svg') center no-repeat; background-size: contain; left: 6px; top: 7px;}
			}
		}
		@media(max-width: @t){
			a{
				span:before{width: 13px; height: 17px; left: 4px; top: 6px;}
			}
		}
	}
	.auth-li-logout{
		a{
			span{
				&:before{.pseudo(16px,16px); background: url('/assets/images/custom-icons/logout.svg') center no-repeat; background-size: contain; left: 9px; top: 10px;}
			}
		}
		@media(max-width: @t){
			a{
				span:before{width: 14px; height: 14px; left: 5px; top: 9px;}
			}
		}
	}
</style>
