<template>
	<div class="terms-container cms-content" v-html="pageData?.content"></div>
	<BaseCmsLabel code="accept_terms_new_window" tag="div" class="terms-container-link" />
</template>

<script setup>
	const page = usePage();
	const pageData = ref();

	onMounted(async () => {
		pageData.value = await page.fetch({slug: '/uslovi-koristenja-kupovine/'});
	});
</script>

<style scoped lang="less">
	//terms container
	.terms-container{
		border-radius: var(--inputBorderRadius); border: 1px solid var(--colorBorderLightForms); height: 95px; overflow: auto; padding: 10px 16px; font-size: 12px; line-height: 17px; color: var(--colorTextLightGray); margin-bottom: 8px;
		&::-webkit-scrollbar {-webkit-appearance: none; width: 3px; background-color: var(--colorBorderLightForms); border-radius: 6px;}
		&::-webkit-scrollbar-thumb {background-color: var(--colorBase); border-radius: 6px;}
		&::-webkit-scrollbar-track{border: unset; border-radius: 6px;}
		:deep(img){display: none;}
		:deep(.btn){margin: 0;}
		:deep(.terms-link){color: var(--colorTextLightGray); text-decoration-color: var(--colorTextLightGray); pointer-events: none;}
		:deep(.extra){font-size: 12px; line-height: 17px; padding: 10px 16px;}
		:deep(.extra2){font-size: 12px; line-height: 17px;}
		:deep(h2){font-size: 14px; line-height: 18px; padding: 10px 0;}
		:deep(h3){font-size: 14px; line-height: 18px; padding: 10px 0;}
		:deep(ul>li:before){top: 3px;}
	}
	.terms-container-link{
		margin-bottom: 16px; display: flex; align-items: baseline;
		:deep(a){
			font-size: 12px; display: flex; line-height: 18px; color: var(--colorBase); text-decoration: underline; text-decoration-color: var(--colorBaseBlue); text-underline-offset: 3px; transition: text-decoration .3s;
			&:hover{text-decoration-color: transparent;}
		}
	}
</style>
