<template>
	<div v-if="itemsData.length" class="orders">
		<BaseUiAccordion>
			<BaseUiAccordionPanel v-for="item in itemsData" :key="item.id" :id="item.id" v-slot="{onToggle, active}">
				<div class="order-row" :class="{'active': active}">
					<div class="table-order" @click="onToggle">
						<div class="table-col-cnt col-order-num-cnt">
							<div class="table-order-date"><BaseCmsLabel code="order_num" /> <BaseUtilsFormatDate :date="item.datetime_created" /></div>
							<div class="table-order-num">#{{item.id}}</div>
						</div>
						<div class="table-col-cnt col-order-total-cnt">
							<div class="table-order-total"><BaseUtilsFormatCurrency :price="item.total" /></div>
							<div class="table-order-total-products">({{item.order_items.length}} <BaseCmsLabel v-if="item.order_items.length > 1" code="products" /><BaseCmsLabel v-else code="product2" />)</div>
						</div>
						<div class="table-col-cnt col-status">
							<div class="col-order-status-label"><BaseCmsLabel code="status" />: {{item.status.title}}</div>
							<div class="order-status">
								<span class="order-status-bar" :style="getStatusStyle(item.status)"></span>
							</div>
						</div>
						<div class="table-col col-order-btns" :class="{'active':active}">
							<div><BaseCmsLabel code="details" /><span class="plus"></span></div>
						</div>
					</div>
					<div v-if="item.order_items" v-show="active" class="order-details">
						<div class="w-table w-table-details">
							<div class="wp wp-details" v-for="item in item.order_items" :key="item.id">
								<div class="wp-image">
									<BaseUiImage :data="item.main_image_thumbs?.['width150-height150']" default="/images/no-image-104.jpg" loading="lazy" />
								</div>
								<div class="wp-cnt-all">
									<div class="wp-cnt">
										<div class="wp-manufacturer-title">{{ item.manufacturer_title }}</div>
										<div class="wp-title">{{ item.title }}</div>
										<div class="wp-code">
											<BaseCmsLabel code="id" />: <span>{{ item.code }}</span>
										</div>
										<div class="wp-attributes" v-if="item.description">{{item.description}}</div>
									</div>
									<div class="wp-total">
										<div class="wp-price">
											<template v-if="item.basic_price > item.price">
												<div class="wp-price-old line-through"><BaseUtilsFormatCurrency :price="item.basic_price" /></div>
												<div class="wp-price-current wp-price-discount red"><BaseUtilsFormatCurrency :price="item.price" /></div>
											</template>
											<template v-else>
												<div class="wp-price-current product_total"><BaseUtilsFormatCurrency :price="item.price" /></div>
											</template>
										</div>
										<div class="wp-qty-count wp-price-old">
											<span class="product_qty">{{ parseInt(item.qty).toFixed(0) }}</span> x <BaseUtilsFormatCurrency :price="item.price" />
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="wp-total-sum">
							<div class="total-sum-col total-sum-shipping" v-if="item.delivery_titles">
								<strong><BaseCmsLabel code="shipping" />:</strong>
								<span>{{item.delivery_titles}}</span>
							</div>
							<div class="total-sum-col total-sum-payment" v-if="item.payment_titles">
								<strong><BaseCmsLabel code="payment" />:</strong>
								<span>{{item.payment_titles}}</span>
							</div>
							<div class="total-sum-col total-sum-total-price">
								<strong><BaseCmsLabel code="total_to_pay2" />:</strong>
								<span><BaseUtilsFormatCurrency :price="item.total" /></span>
							</div>
						</div>
					</div>
				</div>
			</BaseUiAccordionPanel>
		</BaseUiAccordion>
	</div>
	<BaseCmsLabel v-else code="auth_no_orders" tag="div" class="auth-no-orders auth-no-content" />
</template>

<script setup>
	const labels = useLabels();
	const props = defineProps(['items', 'onLoadMore', 'loading', 'pagination', 'mode']);

	const itemsData = computed(() => {
		if(props.mode === 'dashboard') {
			return props.items ? [props.items[0]] : null
		} else {
			return props.items ? props.items : null
		}
	});

	function getStatusStyle(status) {
		let width = '30%';
		let color = 'var(--colorBase)';

		if (status.id === '10' || status.id === '13') {
			width = '30%';
		}
		if (status.id === '16' || status.id === '19') {
			width = '60%';
			color = 'var(--colorBaseBlue)';
		}
		if (status.id === '22' || status.id === '25' || status.id === '28') {
			width = '100%';
			color = 'var(--colorRed)';
		}
		if (status.id === '22') {
			color = 'var(--colorGreen)';
		}

		return `width: ${width}; background: ${color};`;
	}
</script>

<style lang="less" scoped>
	.order-row{
		box-shadow: 0 10px 40px 0 rgba(0,89,194,0.08); border-radius: var(--inputBorderRadius); margin-bottom: 8px; border: 1px solid var(--colorBorderLightForms); background: var(--colorWhite);
		&:last-child{margin-bottom: 0;}
		@media(max-width: @m){
			box-shadow: unset; border: unset; border-radius: unset; margin-bottom: 0;
			&:first-of-type{
				.table-order{padding-top: 0;}
				.col-order-btns{top: 0;}
			}
			&.active{
				:deep(.table-order-date){color: var(--colorBaseBlue);}
			}
		}
	}
	.auth-no-content{
		@media(max-width: @m){padding: 0 16px;}
	}
	.table-order{
		display: flex; padding: 24px 121px 24px 32px; position: relative; cursor: pointer;
		@media(max-width: @m){flex-flow: column; padding: 24px 90px 0 16px;}
	}
	.order-details{
		display: flex; padding: 0 32px 27px;
		@media(max-width: @m){display: block; padding: 24px 16px 0;}
	}
	.table-col-cnt{
		font-size: 14px; line-height: 18px;
		@media(max-width: @m){font-size: 13px; line-height: 18px;}
	}
	.table-order-date,.table-order-total{
		font-weight: bold; padding-bottom: 3px;
		@media(max-width: @m){padding-bottom: 0;}
	}
	.table-order-date{
		@media(max-width: @m){font-size: 15px; line-height: 20px; padding-bottom: 2px;}
	}
	.table-order-num{
		@media(max-width: @m){padding-bottom: 2px;}
	}
	.col-order-num-cnt{
		width: 333px; padding-right: 10px;
		@media(max-width: @m){width: 100%; padding-right: 0;}
	}
	.col-order-total-cnt{
		margin-right: 32px; width: 120px; text-align: right;
		@media(max-width: @m){margin-right: 0; width: 100%; text-align: left; display: flex; align-items: center; padding-bottom: 2px;}
	}
	.table-order-total-products{
		@media(max-width: @m){padding-left: 3px;}
	}
	.col-status{
		width: auto; flex-grow: 1;
		@media(max-width: @m){margin-right: -75px;}
	}
	.col-order-btns{
		position: absolute; right: 32px; top: 24px;
		div{
			font-size: 12px; line-height: 15px; font-weight: bold; padding-right: 18px; color: var(--colorBaseBlue); position: relative;
			.plus{
				width: 10px; height: 10px; position: absolute; right: 0; top: 3px;
				&:before{.pseudo(10px,2px); background: var(--colorBaseBlue); right: 0; top: calc(~"50% - 1px");}
				&:after{.pseudo(2px,10px); background: var(--colorBaseBlue); right: calc(~"50% - 1px"); top: 0; .transition(opacity);}
			}
		}
		&.active{
			:deep(div){color: var(--colorBase);}
			:deep(.plus:after){display: none;}
		}
		@media(max-width: @m){
			right: 16px; top: 24px;
		}
	}
	.order-status{
		width: 100%; height: auto; background: var(--colorBorderLightForms); margin-top: 8px; border-radius: 3px;
		@media(max-width: @m){margin-top: 5px;}
	}
	.order-status-bar{display: block; width: 30%; background: var(--colorBase); height: 100%; background-size: contain; border-radius: 3px; height: 4px;}
	.order-status-bar-black{background: var(--colorBase);}
	.order-status-bar-blue{background: var(--colorBaseBlue); width: 60%;}
	.order-status-bar-green{background: var(--colorGreen); width: 100%;}
	.order-status-bar-red{background: var(--colorRed); width: 100%;}

	.w-table-details{
		display: flex; flex-flow: column; width: 452px; margin-right: 32px; row-gap: 16px; flex-shrink: 0;
		@media(max-width: @m){width: 100%; margin-right: 0; row-gap: 24px;}
	}
	.wp-details{display: flex; align-items: center;}
	.wp-image{
		flex-shrink: 0; width: 88px; height: 88px;
		:deep(img){display: block; width: auto; height: auto; max-width: 100%; max-height: 100%;}
		@media(max-width: @m){width: 104px; height: 104px;}
	}
	.wp-cnt-all{
		display: flex; align-items: center; padding: 2px 0 2px 16px; flex-grow: 1;
		@media(max-width: @m){display: block; padding: 0 0 0 16px; width: 100%;}
	}
	.wp-cnt{flex-grow: 1;}
	.wp-manufacturer-title{font-size: 12px; line-height: 14px; font-weight: bold; color: var(--colorBaseBlue); padding-bottom: 2px; text-transform: uppercase;}
	.wp-title{
		font-size: 14px; line-height: 20px; font-weight: bold; color: var(--colorBase); padding-bottom: 2px;
		@media(max-width: @m){font-size: 13px; line-height: 18px;}
	}
	.wp-code,.wp-attributes{font-size: 12px; line-height: 1.5;}
	.wp-total{
		flex-shrink: 0; width: 110px; text-align: right; font-size: 12px; line-height: 16px;
		@media(max-width: @m){width: 100%; text-align: left; margin-top: 8px; font-size: 11px; line-height: 14px;}
	}
	.wp-price-current{
		font-weight: bold; font-size: 14px; line-height: 20px;
		@media(max-width: @m){font-size: 13px; line-height: 18px;}
	}
	.wp-price-discount{color: var(--colorRed);}
	.wp-price-old{text-decoration: line-through;}
	.wp-qty-count{color: var(--colorTextLightGray); text-decoration: none; padding-top: 1px;}

	.wp-total-sum{
		background: var(--colorLightBlueBackground); flex-grow: 1; border-radius: var(--inputBorderRadius); padding: 25px 30px; position: relative;
		&:before{.pseudo(10px,10px); top: -3px; background: var(--colorLightBlueBackground); left: 35px; transform: rotate(45deg);}
		@media(max-width: @m){
			margin: 24px -16px 0; border-radius: unset; padding: 16px 16px 14px;
			&:before{left: 62px;}
		}
	}
	.total-sum-col{
		font-size: 14px; line-height: 18px; margin-bottom: 12px; padding-left: 44px; width: 100%; position: relative;
		:deep(strong){display: block; padding-bottom: 1px;}
		&:last-child{margin-bottom: 0;}
		&:before{.pseudo(32px,21px); background: url(assets/images/custom-icons/shipping.svg) top left no-repeat; background-size: contain; left: 0; top: 2px;}
		@media(max-width: @m){
			font-size: 13px; line-height: 17px;
			&:deep(strong){padding-bottom: 0;}
			&:before{width: 27px; height: 21px; top: 4px;}
		}
	}
	.total-sum-payment{
		&:before{.pseudo(30px,23px); background: url(assets/images/custom-icons/payment.svg) top left no-repeat; background-size: contain; left: 2px;}
		@media(max-width: @m){
			&:before{width: 25px; height: 19px; left: 5px;}
		}
	}
	.total-sum-total-price{
		&:before{.pseudo(24px,29px); background: url(assets/images/custom-icons/total.svg) top left no-repeat; background-size: contain; left: 6px;}
		@media(max-width: @m){
			&:before{width: 23px; height: 27px; left: 6px;}
		}
	}
</style>
