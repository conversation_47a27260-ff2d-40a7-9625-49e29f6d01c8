<template>
	<ClientOnly>
		<div class="login-register">
			<div class="wrapper">
				<div class="lr-cols">
					<div class="lr-col1">
						<slot name="col1" />
					</div>
					<div class="lr-col2">
						<slot name="col2" />
					</div>
				</div>
			</div>
		</div>
		<template #fallback>
			<BaseThemeUiLoading />
		</template>
	</ClientOnly>
</template>

<style lang="less">
	@media(max-width: @m){
		.page-auth{
			.newsletter,.d-categories{display: none !important;}
		}
	}
</style>

<style lang="less" scoped>
	.login-register{
		padding: 56px 0 88px; background: var(--colorLightBlueBackground);
		@media (max-width: @t){padding: 30px 0 40px;}
		@media(max-width: @m){
			padding: 24px 0 48px; background: var(--colorWhite);
			:deep(input[type=checkbox] + label, input[type=radio] + label){font-size: 13px; line-height: 22px;}
			:deep(input[type=checkbox] + label:before){width: 22px; height: 22px; line-height: 22px; font-size: 12px;}
		}
	}
	.lr-cols{
		display: flex; padding: 0 104px;
		@media(max-width: @t){padding: 0;}
		@media(max-width: @m){display: block;}
	}
	.lr-col1{
		width: 512px; background: var(--colorWhite); border-radius: 4px; padding: 40px 56px 56px; flex-shrink: 0; margin-right: 104px; box-shadow: 0 0 40px 0 rgba(0,89,194,0.1); align-self: baseline;
		@media(max-width: @t){width: 50%; flex-grow: 1; margin-right: 50px;}
		@media(max-width: @m){margin: 0; width: 100%; padding: 0; flex-shrink: unset; flex-grow: 1; background: unset; box-shadow: unset;}
	}
	.lr-col2{
		flex-grow: 1; padding: 40px 0 5px 0; max-width: 400px;
		:deep(h2){padding-top: 0; padding-bottom: 8px; font-size: 24px; line-height: 32px;}
		//:deep(.cms-content){max-width: 420px;}
		@media(max-width: @t){flex-shrink: 0;}
		@media(max-width: @m){
			max-width: unset; padding-bottom: 0;
			:deep(h2){font-size: 19px; line-height: 22px;}
		}
	}
</style>
