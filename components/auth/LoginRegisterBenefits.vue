<template>
	<BaseCmsRotator :fetch="{code: 'lr_benefits', limit: 3}" v-slot="{items}">
		<div v-if="items?.length" class="lr-benefits" :class="'lr-'+[mode]">
			<div class="lr-benefit" v-for="item in items" :key="item.id">
				<NuxtLink v-if="item.link" :to="item.link">
					<div v-if="item.image" class="lr-benefit-img"><BaseUiImage :src="item.image_upload_path" width="64" height="64" /></div>
					<div v-if="item.title" class="lr-benefit-title">{{ item.title }}</div>
					<div v-if="item.content" class="lr-benefit-content">{{ item.content }}</div>
				</NuxtLink>
				<template v-else>
					<div v-if="item.image" class="lr-benefit-img"><BaseUiImage :src="item.image_upload_path" width="64" height="64" /></div>
					<div v-if="item.title" class="lr-benefit-title">{{ item.title }}</div>
					<div v-if="item.content" class="lr-benefit-content">{{ item.content }}</div>
				</template>
			</div>
		</div>
	</BaseCmsRotator>
</template>

<script setup>
	const props = defineProps(['mode']);
</script>

<style lang="less" scoped>
	.lr-benefits{padding-top: 38px;
		@media(max-width: @m){padding-top: 32px;}
	}
	.lr-benefit{
		position: relative; font-size: 14px; line-height: 19px; margin-bottom: 16px; min-height: 64px; padding-left: 80px; display: flex; flex-flow: column; justify-content: center;
		&:last-child{margin-bottom: 0;}
		a{
			text-decoration: none; color: var(--colorBase); .transition(color); width: 100%;
			&:hover{color: var(--colorBaseBlue);}
		}
		@media(max-width: @m){font-size: 12px; line-height: 16px; padding-left: 72px; min-height: 56px;}
	}
	.lr-benefit-img{
		width: 64px; height: 64px; background: var(--colorWhite); box-shadow: 0 8px 18px 0 rgba(0,89,194,0.1); border-radius: 50%; position: absolute; left: 0; top: 0; display: flex; align-items: center; justify-content: center;
		:deep(img){display: block; width: auto; height: auto;}
		@media(max-width: @m){
			width: 56px; height: 56px; box-shadow: unset; background: var(--colorLightBlueBackground);
			:deep(img){max-height: 58%;}
		}
	}
	.lr-benefit-title{
		font-size: 16px; line-height: 24px; font-weight: bold; display: block; padding-bottom: 1px;
		@media(max-width: @m){font-size: 15px; left: 21px;}
	}

	//register
	.lr-register{
		padding-top: 20px;
		@media(max-width: @m){padding-top: 10px;}
	}
</style>
