<template>
	<BaseThemeUiModal name="newsletter-leaving" v-slot="{onClose}" :cookie="true" cookie-expire="1y" :page-leave="pageLeave" :scroll-trigger="isMobile ? 3 : 0" :key-closable="false" :style="labelImage.length ? { backgroundImage: `url(${labelImage[0].url})` } : {}">
		<BaseCmsLogo class="logo" id="logo" />
		<div class="nw-leaving-cnt">
			<div class="nw-title-cnt" v-html="labels.get('newsletter_widget_content')"></div>
			<BaseNewsletterSignupForm class="nw-leaving-form" event="leave" v-slot="{fields, gdprFields, status}">
				<template v-if="!status?.success">
					<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage}">
						<template v-if="item.type != 'hidden'">
							<BaseFormInput class="nw-input" :placeholder="labels.get('enter_email')" />
							<span class="error" v-show="errorMessage" v-html="errorMessage" />
						</template>
						<BaseFormInput v-else />
					</BaseFormField>
					<button class="btn nw-button" type="submit"><BaseCmsLabel code="newsletter_signup" /></button>
					<div class="nw-gdpr-checkbox" v-if="gdprFields">
						<BaseFormField v-for="field in gdprFields" :key="field.name" :item="field">
							<BaseFormInput v-if="field.type == 'hidden'" />
							<p class="field" v-else v-interpolation>
								<BaseFormInput :id="`nl-leaving-${field.name}`" />
								<label :for="`nl-leaving-${field.name}`" v-html="labels.get('newsletter_gpdr_link')"></label>
							</p>
						</BaseFormField>
					</div>
					<BaseCmsLabel class="nw-close" code="nw_close" tag="div" @click="onClose" />
				</template>
				<BaseCmsLabel v-show="status?.success" tag="div" class="nw-success" code="success_subscribe" />
			</BaseNewsletterSignupForm>
		</div>
	</BaseThemeUiModal>
</template>

<script setup>
	const labels = useLabels();
	const {onMediaQuery} = useDom();
	const {matches: isMobile} = onMediaQuery({
		query: '(max-width: 990px)',
	});
	const {gdprApproved} = useGdpr();
	const pageLeave = computed(() => {
		return (!isMobile.value && gdprApproved()?.length) ? true : false;
	});

	const { extractImageUrls } = useImages();
	const labelImage = computed(() => {
		const label = isMobile.value ? labels.get('nw_leaving_mobile') : labels.get('nw_leaving');
		return (label) ? extractImageUrls(label) : [];
	})
</script>

<style lang="less" scoped>
	.logo {
		position: absolute; top: 13%; left: 0; display: block; width: 180px; height: 46px; margin-right: 0; background: url(assets/images/logo.svg) no-repeat center center; background-size: contain;
		@media (max-width: @t){width: 133px; height: 34px; top: 44px;}
		@media (max-width: @m){width: 118px; height: 30px; top: 18px;}
	}
	:deep(.newsletter-leaving){
		background-size: cover; background-position: center; background-repeat: no-repeat;
		.base-modal-mask{background: none;}
	}
	:deep(.base-modal-toolbar){
		top: 104px; right: 104px;
		@media (max-width: @t){right: 40px; top: 40px;}
		@media (max-width: @m){right: 7px; top: 7px;}
	}
	:deep(.base-modal){
		.base-modal-cnt-close{
			width: 56px; height: 56px; position: fixed; top: 10%; right: 5%; background: #fff; border-radius: 100%; .transition(background-color);
			@media (max-width: @t){background: none; right: 8px!important; top: 8px!important;}
			&:before{
				color: var(--colorBase)!important; .transition(color);
				@media (max-width: @t){color: #fff!important; font-size: 23px!important;}
			}
			&:hover{
				background: var(--colorBaseBlue);
				&:before{color: #fff!important;}
			}
			@media (max-width: @m){background: none; box-shadow: none;}
		}
	}
	:deep(.base-modal-cnt){
		background: none!important; max-width: 1280px!important; width: 100%!important; box-shadow: none!important; position: absolute!important; top: 0px!important; left: 0!important; right: 0; margin: auto; padding: 0!important; border-radius: 0!important; height: 100vh; max-height: 100vh!important;
		@media (max-width: @t){max-width: unset!important; width: auto!important; left: 40px!important; right: 40px;}
		@media (max-width: @m){left: 16px!important; right: 16px; display: flex; align-items: center;}
	}
	.nw-leaving-cnt{
		width: 100%; height: 100%; position: absolute; display: block; max-width: 424px; width: 100%; height: auto; top: 50%; transform: translateY(-50%);
		@media (max-width: @m){max-width: 100%;}
	}
	.nw-title-cnt {
		position: relative; display: block; color: rgba(255,255,255,0.6); padding-top: 96px;
		&:before{.pseudo(80px,80px); background: url('/assets/images/custom-icons/newsletter.svg') center no-repeat; background-size: cover; top: 0; left: 0;}
		@media (max-width: @t){
			padding-top: 80px;
			&:before{width: 65px; height: 65px;}
		}
		@media (max-width: @m){
			padding-top: 68px;
			&:before{width: 56px; height: 56px;}
		}
	}
	:deep(.nw-title-cnt) {
		p:first-child {
			color: var(--colorWhite); padding: 0 0 13px; font-size: 24px; line-height: 33px; font-weight: bold;
			strong{background: var(--colorYellow); color: var(--colorBase); border-radius: var(--inputBorderRadius); padding: 2px 6px;}
			@media (max-width: @t) {
				font-size: 23px; line-height: 28px; padding-bottom: 15px; position: relative;
				strong{padding: 1px 4px;}
			}
			@media (max-width: @m){
				text-shadow: 1px 2px 0 rgba(0, 12, 26, 0.5);
				strong{text-shadow: none!important; white-space: nowrap;}
			}
		}
		p:last-of-type{
			padding-bottom: 29px; padding-right: 47px; line-height: 1.4;
			@media (max-width: @t){padding-bottom: 24px; padding-right: 0;}
		}
	}
	:deep(.nw-leaving-form){
		position: relative;
		.nw-input{
			position: relative; display: block; border: none!important; box-shadow: 0 5px 20px rgba(0, 12, 26, 0.05); padding-right: 102px;
			@media (max-width: @m){}
		}
		&.loading:before{display: none;}
	}
	.nw-button{
		position: absolute; width: 97px; height: 40px; top: 8px; right: 8px; display: flex; align-items: center; justify-content: center; padding: 0;
		@media (max-width: @m){width: 87px;}
	}
	.nw-gdpr-checkbox {
		position: relative; display: block; margin-top: 20px;
		label {
			display: block; color: var(--colorWhite); font-size: 12px; line-height: 15px;
			&:before{width: 20px; height: 20px; top: -3px;}
		}
		p{padding-bottom: 0;}
		:deep(a){
			color: var(--colorBaseLighter); font-weight: bold; text-decoration: underline; text-underline-offset: 3px; transition: text-decoration-color 0.3s;
		&:hover {text-decoration-color: transparent;}
		}
		@media (max-width: @t) {
			margin-right: 16px;
			label{
				padding-left: 30px; font-size: 13px; line-height: 17px;
				&:before{width: 18px; height: 18px; top: -2px;}
			}
		}
	}
	.nw-close{
		position: relative; display: block; font-size: 13px; line-height: 1.3; font-weight: bold; margin-top: 36px; text-decoration: underline; color: var(--colorWhite); text-decoration-color: var(--colorWhite); text-underline-offset: 3px; cursor: pointer;
		&:hover{text-decoration-color: transparent;}
		@media (max-width: @t){margin-top: 30px;}
	}
	.nw-success{color: var(--colorWhite);}

	:deep(.base-modal-toolbar){
		@media (max-width: @l){right: 44px;}
		@media (max-width: @t){right: 40px;}
		@media (max-width: @m){right: 7px;}
	}
</style>
