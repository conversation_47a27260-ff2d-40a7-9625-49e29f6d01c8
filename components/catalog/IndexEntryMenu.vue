<template>
	<article class="cp">
		<div class="cp-left">
			<figure class="cp-image">
				<NuxtLink :to="item.url_without_domain" @click="gtmTrack('selectItem', {items: item})">
					<BaseUiImage loading="lazy" :data="item.main_image_thumbs?.['width400-height400']" default="/images/no-image-80.jpg" :title="item.main_image_title" :alt="item.main_image_description ? item.main_image_description : item.title" />
				</NuxtLink>
			</figure>
			<div v-if="item.variation_request === '1' && item.variation_discount_percent_max > 0" class="cp-badge cp-badge-discount">
				<span> -{{ item.variation_discount_percent_max }}% </span>
			</div>
			<div v-else-if="item.variation_request !== '1' && item.discount_percent > 0" class="cp-badge cp-badge-discount">
				<span> -{{ item.discount_percent }}% </span>
			</div>
		</div>
		<div class="cp-cnt">
			<div class="cp-manufacturer" v-if="item.manufacturer_title">
				<NuxtLink :to="item.manufacturer_url_without_domain">{{item.manufacturer_title}}</NuxtLink>
			</div>
			<div class="cp-title">
				<NuxtLink :to="item.url_without_domain" @click="gtmTrack('selectItem', {items: item})">{{item.title}}</NuxtLink>
			</div>

			<div class="cp-price">
				<template v-if="item.variation_request === '1' && item.variation_price_same !== '1' && item.variation_price_min > 0">
					<BaseCmsLabel class="cp-price-label" code="price_from" tag="div" />
					<div class="cp-current-price"><BaseUtilsFormatCurrency :price="item.variation_price_min" /></div>
				</template>
				<template v-else>
					<template v-if="(item.discount_percent_custom > 0 || item.price_custom < item.basic_price_custom)">
						<div class="cp-old-price"><BaseUtilsFormatCurrency :price="item.basic_price_custom" /></div>
						<div class="cp-current-price cp-discount-price"><BaseUtilsFormatCurrency :price="item.price_custom" /></div>
					</template>
					<template v-else>
						<div class="cp-current-price"><BaseUtilsFormatCurrency :price="item.price_custom" /></div>
					</template>
				</template>
			</div>
		</div>
	</article>
</template>

<script setup>
	const {gtmTrack} = useGtm();
	const props = defineProps(['item']);
</script>

<style lang="less" scoped>
	.cp{
		position: relative; display: flex; align-items: center; border: 1px solid var(--colorLightGray); background: var(--colorWhite); width: 100%; max-height: 112px; margin-top: -1px; .transition(box-shadow); padding: 16px;
		@media (min-width: @h){
			&:hover{z-index: 1; box-shadow: 0 0 18px rgba(0,89,194,0.12);}
		}
		@media (max-width: @t){max-height: 84px; padding: 12px 35px 12px 15px;}
	}
	.cp-left{
		flex-grow: 0; flex-shrink: 0; position: relative; display: flex; align-items: center; justify-content: center; width: 80px; height: 80px; max-width: 80px; margin-right: 24px;
		@media (max-width: @t){width: 61px; height: 61px; max-width: 61px; margin-right: 10px;}
	}
	.cp-image{
		flex-grow: 1; align-items: center; justify-content: center; display: flex;
		a{
			display: flex; align-items: center; justify-content: center;
			img{width: auto; height: auto; max-width: 100%; max-height: 100%; display: block;}
		}
	}
	.cp-badge{
		padding: 3px 5px; background: var(--colorRed); border-radius: var(--inputBorderRadius); position: absolute; top: -1px; left: -3px; color: var(--colorWhite); font-size: 11px; line-height: 1.3; font-weight: bold; text-transform: uppercase;
		@media (max-width: @t){padding: 2px 4px 3px; font-size: 9px; top: -4px; left: -8px;}
	}
	.cp-cnt{position: relative; display: flex; flex-flow: column; flex-grow: 1; flex-shrink: 1;}
	.cp-manufacturer{
		font-size: 11px; line-height: 1.1; font-weight: bold; text-transform: uppercase; padding-bottom: 2px; color: var(--colorBaseBlue);
		a{
			text-decoration: none; font-weight: bold; color: var(--colorBaseBlue);
			&:hover{text-decoration: underline;}
		}
		@media (max-width: @t){font-size: 9px; padding-bottom: 0;}
	}
	.cp-title{
		font-size: 12px;
		a{
			color: var(--colorBase); text-decoration: none;
			&:hover{color: var(--colorBaseBlue);}
		}
		@media (max-width: @t){font-size: 10px; line-height: 1.4;}
	}
	.cp-price{
		display: flex; align-items: center; font-size: 12px; line-height: 1.3; padding-top: 7px;
		@media (max-width: @t){font-size: 9px; line-height: 1.2; padding-top: 6px;}
	}
	.cp-old-price, .cp-price-label{
		padding-right: 6px;
		@media (max-width: @t){padding-right: 4px;}
	}
	.cp-old-price{text-decoration: line-through;}
	.cp-current-price{font-weight: bold;}
	.cp-discount-price{color: var(--colorRed);}
</style>
