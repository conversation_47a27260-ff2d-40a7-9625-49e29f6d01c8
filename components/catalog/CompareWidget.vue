<template>
	<BaseCatalogCompareWidget v-slot="{counter, compareUrl}">
		<NuxtLink :to="compareUrl" class="btn-header cw-compare" :class="{'active': counter}">
			<span class="btn-header-text"><BaseCmsLabel code="header_btn_compare_product" /></span>
			<ClientOnly>
				<span class="btn-header-counter cw-compare-counter">{{counter}}</span>
			</ClientOnly>
		</NuxtLink>
	</BaseCatalogCompareWidget>
</template>

<style lang="less" scoped>
	.cw-compare {
		&:before {
			.icon-compare();
			font: 19px/1 var(--fonti);
		}
		@media (min-width: @h){
			&:hover{text-decoration: none; color: var(--colorWhite); background: var(--colorDarkBlue);}
		}
		&.active{
			.cw-compare-counter{display: flex; border: 2px solid var(--colorBaseBlue); color: var(--colorBase); background: var(--colorWhite);}
		}
		@media (max-width: @t){
			width: 64px;
			&:before {font-size: 17px;}
		}
		@media (max-width: @m){
			width: 20px; min-width: 20px; font-size: 0; line-height: 0; margin: 0 10px;
			&:before{font-size: 20px;}
		}
	}
	.cw-compare-counter{
		right: 18px;
		@media (max-width: @t){right: 10px;}
		@media (max-width: @m){right: -13px;}
	}
</style>
