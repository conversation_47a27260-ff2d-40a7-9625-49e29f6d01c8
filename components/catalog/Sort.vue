<template>
	<BaseCatalogSort :sort-options="['cheaper', 'new', 'expensive', 'az', 'za']" v-slot="{items: sortOptions, selected, onSort}">
		<div class="sort">
			<select class="sort-select" @change="onSort">
				<option :value="sort" v-for="sort in sortOptions" :selected="sort == selected" :key="sort"><BaseCmsLabel :code="'sort_'+sort" /></option>
			</select>
		</div>
	</BaseCatalogSort>
</template>

<style lang="less" scoped>
	.sort{
		@media (max-width: @m){flex: 1 1 50%;}
	}
	:deep(.sort-select){
		height: 48px; width: 152px; font-size: 14px;
		@media (max-width: @m){width: 100%; border: 0; border-left: 1px solid var(--colorDarkBlue); border-radius: 0; background: var(--colorBaseBlue) url(assets/images/select-arrow-white.svg) no-repeat; background-position: right 16px center; color: #fff; font-size: 13px; font-weight: bold; padding: 0 40px 0 16px;}
	}
	.c-toolbar-right .sort{
		@media (max-width: @m){display: none;}
	}
</style>
