<template>
	<div class="speciallist-widget" v-if="items?.length">
		<div class="wrapper speciallist-wrapper">
			<slot name="header" />
			<div class="speciallist-items-container">
				<BaseUiSwiper class="speciallist-items" :options="sliderOptions">
					<BaseUiSwiperSlide v-for="item in items" :key="item.id">
						<CatalogIndexEntry :item="item" mode="slider" :options="options" />
					</BaseUiSwiperSlide>
				</BaseUiSwiper>
			</div>
		</div>
	</div>
</template>

<script setup>
	const props = defineProps(['items', 'perPage', 'list', 'options']);
	const sliderOptions = {
		slidesPerView: props.perPage ? props.perPage : 5,
		slidesPerGroup: props.perPage ? props.perPage : 5,
		navigation: {
			enabled: true,
			wrapperClass: 'splide__arrows',
			nextElClass: 'splide__arrow splide__arrow--next',
			prevElClass: 'splide__arrow splide__arrow--prev'
		},
		spaceBetween: -1,
		enabled: false,
		breakpoints: {
			980: {
				enabled: true,
			}
		}
	}
</script>

<style lang="less" scoped>
	// homepage speciallist
	.homepage-speciallist{
		position: relative; display: block; margin: 0 0 55px;
		@media (max-width: @t){margin: 0 0 44px;}
		@media (max-width: @m){margin: 0 0 30px;}
	}
	.speciallist-wrapper{position: relative;}
	.speciallist-items-container{
		position: relative; display: block;
		@media (max-width: @m){
			margin-left: -16px; width: calc(~"100% + 32px");
			:deep(.swiper-wrapper){ position: relative; display: flex; overflow-x: auto; padding: 0 16px; box-sizing: inherit; /*scroll-snap-type: x proximity;*/}
			:deep(.swiper-slide){width: calc(~"50% - 16px")!important; flex-grow: 0; flex-shrink: 0; /*scroll-snap-align: center;*/}
		}
	}
	:deep(.speciallist-header){
		position: relative; display: block; margin-bottom: 25px;
		@media (max-width: @t){margin-bottom: 22px;}
		@media (max-width: @m){margin-bottom: 17px;}
	}
	:deep(.speciallist-title){
		display: block; font-size: var(--fontSizeH2); line-height: 1.2; font-weight: bold; margin-bottom: 8px;
		@media (max-width: @t){font-size: var(--fontSizeH1); margin-bottom: 4px;}
		@media (max-width: @m){font-size: var(--fontSizeH3); margin-bottom: 3px;}
	}
	:deep(.speciallist-cnt){
		position: relative; display: block;
		p{padding-bottom: 0;}
		a{
			position: relative; font-weight: bold; text-decoration-color: transparent; padding-right: 25px;
			&:after{.pseudo(20px,20px); .icon-arrow3(); font: 20px/20px var(--fonti); color: var(--colorBaseBlue); top: 0; right: 0;}
			@media (min-width: @h){
				&:hover{text-decoration-color: var(--colorBaseBlue);}
			}
		}
		@media (max-width: @t){
			font-size: var(--fontSizeSmall);
			a:after{top: -1px;}
		}
	}
	.speciallist-items-slider{
		@media (max-width: @m){
			display: flex; width: 100%; visibility: visible!important;
		}
	}
	:deep(.swiper-navigation){
		display: flex; position: absolute; top: 160px; left: -80px; right: -80px; height: 56px;
		@media (max-width: 1500px){left: 16px; right: 16px; height: 48px;}
		@media (max-width: @t){left: -35px; right: -35px; height: 32px; top: calc(~"50% - 16px");}
		@media (max-width: @m){display: none!important;}
	}
	:deep(.swiper-button-prev), :deep(.swiper-button-next){
		z-index: 1; background: var(--colorLightBlueBackground); width: 56px; height: 56px; .transition(background); box-shadow: 0 8px 18px rgba(0,12,26,0.12); border-radius: 50%; top: 0;
		&:before{.icon-arrow2(); font: 18px/1 var(--fonti); color: var(--colorBase); position: absolute; z-index: 1; margin-left: -3px; .transition(color);}
		&.swiper-button-disabled{
			opacity: 0.4; pointer-events: none;
			@media (max-width: @t){
				opacity: 1; background: var(--colorLightBlueBackground);
				&:before{color: var(--colorBase);}
			}
		}
		&.swiper-button-lock{display: none!important;}
		@media (min-width: @h){
			&:hover{
				background: var(--colorBaseBlue);
				&:before{color: var(--colorWhite);}
			}
		}
		@media (max-width: 1500px){
			width: 48px; height: 48px;
			&:before{font-size: 16px;}
		}
		@media (max-width: @t){
			width: 32px; height: 32px; box-shadow: none; background: var(--colorBaseBlue);
			&:before{color: var(--colorWhite); font-size: 12px;}
		}
	}
	:deep(.swiper-button-prev){left: 0;}
	:deep(.swiper-button-next){left: auto; right: 0;}
	:deep(.speciallist-items){
		padding-left: 1px!important; padding-bottom: 20px;
		@media (max-width: @t){padding-bottom: 0;}
		.swiper-slide{display: flex; height: auto;}
		.cp{border-radius: 0;}
		:deep(.swiper-slide){
			padding-bottom: 20px;
			@media (max-width: @t){padding-bottom: 0;}
		}
		:deep(.splide__arrow){top: calc(~"50% - 38px");}
		.cp{
			width: 100%; margin: 0; height: 100%;
			@media (min-width: @h){
				&:hover{z-index: 1; box-shadow: 0 8px 37px -23px rgba(0,12,26,0.22);}
			}
		}
		@media (max-width: @m){
			overflow: inherit; width: 100%;
			:deep(.swiper-slide){
				width: calc(~"50% - 9px"); margin-left: -1px; flex-grow: 0; flex-shrink: 0;
				&:first-child{
					margin-left: 0;
					.cp{border-radius: var(--inputBorderRadius) 0 0 var(--inputBorderRadius);}
				}
				&:last-child{
					.cp{border-radius: 0 var(--inputBorderRadius) var(--inputBorderRadius) 0;}
				}
			}
			.cp{border-radius: 0;}
		}
	}
</style>
