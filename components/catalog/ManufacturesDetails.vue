<template>
	<template v-if="category">
		<div class="manufacturer-image" v-if="category.main_image_2_upload_path && !category.content">
			<BaseUiImage :src="category.main_image_2_upload_path" default="/images/no-image-1920.jpg" loading="lazy" :picture="[{maxWidth: '980px', src: category.main_image_3_upload_path, default: '/images/no-image-980.jpg'}]" />
		</div>

		<div class="manufacturer-logo" v-if="category.main_image_upload_path && !category.main_image_2_upload_path && !category.content"><BaseUiImage :src="category.main_image_upload_path" default="/images/no-image-1920.jpg" loading="lazy" /></div>

		<div class="manufacturer-video" v-html="category.content" v-if="category.content"></div>

		<h1 class="manufacturer-title" v-show="!category.main_image_upload_path">{{ category.title }}</h1>

		<div class="manufacturer-content-container" v-if="category.short_description" :class="{'active': active === true}">
			<div class="manufacturer-content" ref="elementToWatch" v-html="category.short_description" v-interpolation></div>
			<div class="manufacturer-read-more" v-if="category.short_description?.split(' ').length > 34" @click="active = !active, showMoreButton = !showMoreButton"><BaseCmsLabel code="manufacturer_show_all" /></div>
		</div>

		<ClientOnly>
			<CatalogManufacturerFilters />
		</ClientOnly>
	</template>
</template>

<script setup>
	const props = defineProps(['category']);
	let category = props.category;

	const active = ref(0);
	const elementToWatch = ref(null);
	const showMoreButton = ref(false);

	const checkHeight = () => {
		if (elementToWatch.value) {
			const height = elementToWatch.value.clientHeight;
			showMoreButton.value = height > 80;
		}
	};

	onMounted(() => {
		setTimeout(function(){
			checkHeight();
		}, 5000);
	});
</script>

<style lang="less" scoped>
	.manufacturer-image{
		display: block; width: 100%; height: auto; margin-bottom: 40px;
		picture{display: block; width: 100%; height: auto; line-height: 0;}
		img{display: block; width: auto; height: auto; max-width: 100%;}
		@media (max-width: @m){width: calc(~"100% - -32px"); margin-left: -16px; margin-bottom: 16px;}
	}
	.manufacturer-content-container{
		position: relative; display: flex; flex-flow: column; align-items: flex-start; margin-bottom: 24px;
		@media (max-width: @m){
			display: flex; flex-flow: column; align-items: flex-start; margin-bottom: 0;
			&.active{
				.manufacturer-content{text-overflow: inherit; -webkit-line-clamp: inherit; max-height: inherit;}
				.manufacturer-read-more{display: none;}
			}

		}
	}
	.manufacturer-content{
		:deep(p:last-child){padding-bottom: 0;}
		@media (max-width: @m){font-size: 13px; line-height: 20px; max-height: 82px; display: -webkit-box; overflow: hidden; text-overflow: ellipsis; -webkit-line-clamp: 4; -webkit-box-orient: vertical;}
	}
	.manufacturer-read-more{
		position: relative; display: none; font-size: 13px; line-height: 22px; color: var(--colorBaseBlue); text-decoration: underline; text-underline-offset: 3px; padding-right: 16px;
		&:before{.pseudo(8px,8px); .icon-arrow2(); font: 8px/8px var(--fonti); color: var(--colorBaseBlue); top: 7px; right: 0; .rotate(-90deg);}
		@media (max-width: @ms){display: block;}
	}
	@width: var(--pageWidth);
	.manufacturer-video{
		display: block; width: 100%; height: auto; line-height: 0; margin-bottom: 40px;
		:deep(p){padding-bottom: 0;}
		:deep(.embeddedContent){line-height: 0;}
		:deep(iframe){width: 100%; height: calc(@width ~" / 1.77");}
		:deep(.ytp-show-cards-title){display: none;}
		@media (max-width: @t){
			:deep(iframe){height: 622px;}
		}
		@media (max-width: @m){
			width: calc(~"100% - -32px"); margin-left: -16px; margin-bottom: 16px;
			:deep(iframe){height: 490px;}
		}
		@media (max-width: @ms){
			:deep(iframe){height: 375px;}
		}
	}
	.manufacturer-title{
		font-size: var(--fontSizeH4); padding-bottom: 6px; text-transform: uppercase;
		@media (max-width: @m){font-size: 16px; line-height: 21px; padding-bottom: 9px;}
	}
	.manufacturer-logo{
		display: block; margin-bottom: 23px;
		img{display: block; width: auto; height: auto; max-height: 85px; max-width: 310px;}
		@media (max-width: @m){margin-bottom: 9px;}
	}
</style>
