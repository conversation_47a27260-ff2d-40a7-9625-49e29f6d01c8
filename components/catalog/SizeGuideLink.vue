<template>
	<button class="basic link size-guide" @click="modal.open('quick', {seo_h1: labels.get('size_guide_title'), content: props.guide})">
		<BaseCmsLabel code="size_guide" />
	</button>
</template>

<script setup>
	const modal = useModal();
	const labels = useLabels();
	const props = defineProps({
		guide: String,
	});
</script>

<style scoped lang="less">
	.size-guide{
		margin-left: 15px; font-weight: normal; padding-left: 26px; position: relative; background: none;
		&:before{.pseudo(20px,10px); background: url(assets/images/custom-icons/ruler.svg) center no-repeat; background-size: cover; top: 5px; left: 0;}
	}
</style>
