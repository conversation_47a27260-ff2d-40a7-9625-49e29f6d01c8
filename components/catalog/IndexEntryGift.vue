<template>
	<div class="gift-card-item" v-for="product in productsSort" :key="product.id">
		<div class="gift-card-image">
			<NuxtLink :to="product.url_without_domain" @click="gtmTrack('selectItem', {items: product})"><img src="/images/gift-card-icon.svg" alt="" /></NuxtLink>
		</div>
		<div class="gift-card-value">
			<NuxtLink :to="product.url_without_domain" @click="gtmTrack('selectItem', {items: product})"> <BaseUtilsFormatCurrency :price="product.price_custom" /></NuxtLink>
		</div>
		<NuxtLink class="btn btn-green cp-gift-btn-add" :to="product.url_without_domain" @click="gtmTrack('selectItem', {items: product})">
			<span><BaseCmsLabel code="gift_card_add" /></span>
		</NuxtLink>
	</div>
</template>

<script setup>
	const {gtmTrack} = useGtm();
	const props = defineProps(['products']);

	const productsSort = computed(() => {
		if(props.products?.length){
			return props.products.slice().sort((a, b) => a.price_custom.localeCompare(b.price_custom));
		}
	});
</script>

<style lang="less" scoped>
	.gift-card-item{display: flex; align-items: center; width: 100%;}
	.gift-card-image{
		display: block; width: 32px; height: 32px; margin-right: 16px; flex-grow: 0; flex-shrink: 0;
		img{display: block; width: auto; height: auto; max-width: 100%;}
		@media (max-width: @ms){width: 24px; height: 24px; margin-right: 8px;}
	}
	.gift-card-value{
		position: relative; display: block; flex-grow: 1; flex-shrink: 1;
		&:before{.pseudo(100%,1px); background: var(--colorBorderLightForms); left: 0; top: calc(~"50% - 0.5px");}
		a{position: relative; display: inline-block; z-index: 1; background: var(--colorWhite); padding-right: 24px; text-transform: uppercase; font-weight: bold; color: var(--colorBase); text-decoration: none;}
		@media (max-width: @ms){
			&:before{display: none;}
			a{background: transparent; padding-right: 0;}
		}
	}
	.cp-gift-btn-add{
		margin-left: 24px; flex-grow: 0; flex-shrink: 0; cursor: pointer;
		@media (max-width: @ms){margin-left: 8px; padding: 10px 21px;}
	}
</style>
