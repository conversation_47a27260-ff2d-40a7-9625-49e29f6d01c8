<template>
	<BaseCatalogBoughtTogether :items="props.items" :meta="props.meta" v-slot="{total, saving, addToCartData, onResetSelectedVariation}" v-model="checkedItems">
		<div class="cd-bougth-together" v-if="items?.length">
			<BaseCmsLabel class="cd-bought-together-header" code="bought_together_title" tag="div" />
			<div class="cd-bt-items">
				<div class="cd-bt-item" v-for="(item, index) in items" :key="item.id">
					<div class="cd-bt-item-image">
						<NuxtLink :to="item.url_without_domain">
							<BaseUiImage loading="lazy" :data="item.main_image_thumbs?.['width400-height400']" default="/images/no-image-210.jpg" :title="item.main_image_title" :alt="item.main_image_description ? item.main_image_description : item.title" />
						</NuxtLink>
					</div>
					<div class="cd-bt-item-right">
						<div class="cd-bt-item-content">
							<NuxtLink :to="item.manufacturer_url_without_domain" class="cd-bt-item-manufacturer" v-if="item.manufacturer_code">{{item.manufacturer_title}}</NuxtLink>
							<NuxtLink :to="item.url_without_domain" class="cd-bt-item-title">{{item.title}}</NuxtLink>
							<div class="cd-bt-item-code"><BaseCmsLabel code="product_code" /> {{item.code}}</div>
							<div class="cd-bt-item-price mobile">
								<div class="cp-price">
									<template v-if="(item.discount_percent_custom > 0 || item.price_custom < item.basic_price_custom)">
										<div class="cd-bt-item-old-price"><BaseUtilsFormatCurrency :price="item.basic_price_custom" /></div>
										<div class="cd-bt-item-current-price discount"><BaseUtilsFormatCurrency :price="item.price_custom" /></div>
									</template>
									<template v-else>
										<BaseCmsLabel class="cp-price-label" code="price" tag="div" />
										<div class="cd-bt-item-current-price"><BaseUtilsFormatCurrency :price="item.price_custom" /></div>
									</template>
								</div>
							</div>
							<template v-if="item.variation_request == 1">
								<div class="cd-bt-item-variations">
									<div class="cd-bt-item-variations-title"><BaseCmsLabel code="select_size" /> <CatalogSizeGuideLink v-if="item.size_guide" :guide="item.size_guide" /></div>
									<div class="cd-bt-item-variation-items">
										<template v-for="variation in item.variation_quickdata" :key="variation.id">
											<div
												class="cd-bt-item-variation-item"
												:class="{'selected': item.selectedVariation == variation.shopping_cart_code}"
												v-if="variation.is_available"
												@click="onResetSelectedVariation(item.variation_quickdata), item.selectedVariation = variation.shopping_cart_code">
												{{attributeName(item.variation_attributes_all,variation.attributes_ids) || variation.title }}
											</div>
										</template>
									</div>
								</div>
							</template>
							<ClientOnly>
								<template v-if="item.variation_request == '1'">
									<template v-for="variation in item.variation_quickdata" :key="variation.id">
										<template v-if="item.selectedVariation == variation.shopping_cart_code">
											<div class="cd-bt-item-variation-add-container" v-if="variation.is_available">
												<div class="cd-bt-item-qty" v-if="variation.available_qty > 1">
													<BaseThemeWebshopQty :quantity="1" :limit="variation.available_qty" v-model="variation.quantity" />
												</div>
												<div class="cd-bt-item-add">
													<input type="checkbox" :name="variation.shopping_cart_code" :value="variation" :id="variation.shopping_cart_code" v-model="checkedItems" />
													<label :for="variation.shopping_cart_code"><BaseCmsLabel code="want_this" /></label>
												</div>
											</div>
										</template>
									</template>
								</template>
								<template v-else>
									<template v-if="item.is_available">
										<div class="cd-bt-item-qty" v-if="item.available_qty > 1">
											<BaseThemeWebshopQty :quantity="1" :limit="item.available_qty" v-model="item.quantity" />
										</div>
										<div class="cd-bt-item-add">
											<input type="checkbox" :name="item.shopping_cart_code" :value="item" :id="item.shopping_cart_code" v-model="checkedItems" :disabled="index == 0" />
											<label :for="item.shopping_cart_code"><BaseCmsLabel code="want_this" /></label>
										</div>
									</template>
								</template>
							</ClientOnly>
						</div>
						<div class="cd-bt-item-price">
							<div class="cp-price">
								<template v-if="item.variation_request == 1 && item.variation_quickdata && item.selectedVariation">
									<template v-for="variation in item.variation_quickdata" :key="variation.shopping_cart_code">
										<template v-if="variation.shopping_cart_code == item.selectedVariation">
											<template v-if="(variation.discount_percent_custom > 0 || variation.price_custom < variation.basic_price_custom)">
												<div class="cd-bt-item-old-price"><BaseUtilsFormatCurrency :price="variation.basic_price_custom" /></div>
												<div class="cd-bt-item-current-price discount"><BaseUtilsFormatCurrency :price="variation.price_custom" /></div>
											</template>
											<template v-else>
												<div class="cd-bt-item-current-price"><BaseUtilsFormatCurrency :price="variation.price_custom" /></div>
											</template>
										</template>
									</template>
								</template>
								<template v-else>
									<template v-if="(item.discount_percent_custom > 0 || item.price_custom < item.basic_price_custom)">
										<div class="cd-bt-item-old-price"><BaseUtilsFormatCurrency :price="item.basic_price_custom" /></div>
										<div class="cd-bt-item-current-price discount"><BaseUtilsFormatCurrency :price="item.price_custom" /></div>
									</template>
									<template v-else>
										<div class="cd-bt-item-current-price"><BaseUtilsFormatCurrency :price="item.price_custom" /></div>
									</template>
								</template>
							</div>
						</div>
					</div>
				</div>
			</div>
			<ClientOnly>
				<div class="cd-bt-footer" v-if="checkedItems?.length">
					<div class="cd-bt-chosen" :class="{'saving': saving}">
						<div>
							<BaseCmsLabel code="choosen" /> <span>{{checkedItems.length}}/{{items.length}}</span>
						</div>
					</div>
					<div class="cd-bt-chosen-price">
						<div class="cd-bt-total-price"><BaseUtilsFormatCurrency :price="total" /></div>
						<div class="cd-bt-total-saving" v-if="saving">Štedite: <BaseUtilsFormatCurrency :price="saving" /></div>
					</div>
					<BaseWebshopAddToCart v-slot="{onAddToCart, loading}" :data="addToCartData">
						<div class="btn btn-green cd-bt-chosen-add" :class="{'loading': loading}" @click="onAddToCart"><UiLoader v-if="loading" /><BaseCmsLabel code="add_choosen_to_shopping_cart" /></div>
					</BaseWebshopAddToCart>
				</div>
			</ClientOnly>
		</div>
	</BaseCatalogBoughtTogether>
</template>

<script setup>
	const props = defineProps(['items', 'meta']);
	const checkedItems = ref([]);

	// extract attribute name from attributes
	function attributeName(itemAttributes,varAttributeIds){
		if(!itemAttributes || !varAttributeIds) return;

		const attArr = (varAttributeIds.slice(1, -1)).split(',');
		if(!attArr.length) return;

		const attr = Object.values(itemAttributes).find(att => att.id == attArr[0]).title;
		if(attr) return attr;
	}
</script>

<style lang="less" scoped>
	.cd-bougth-together{
		position: relative; display: flex; flex-flow: column; width: 100%; margin-top: 80px;
		@media (max-width: @m){margin-top: 48px;}
	}
	.cd-bought-together-header{
		position: relative; display: block; font-size: 20px; line-height: 1.4; font-weight: bold; margin-bottom: 18px;
		@media (max-width: @t){margin-bottom: 16px;}
		@media (max-width: @m){font-size: 19px; line-height: 22px;}
	}
	.cd-bt-items{
		display: flex; flex-flow: column; width: 100%;
		@media (max-width: @m){width: calc(~"100% - -30px"); margin-left: -15px;}
	}
	.cd-bt-item{
		position: relative; display: flex; border: 1px solid var(--colorBorderLightForms); margin-top: -1px; padding: 24px; gap: 24px;
		&:first-child{margin-top: 0; border-radius: var(--inputBorderRadius) var(--inputBorderRadius) 0 0;}
		&:last-child{border-radius: 0 0 var(--inputBorderRadius) var(--inputBorderRadius);}
		@media (max-width: @m){
			padding: 21px 15px; border-left: 0; border-right: 0; border-radius: 0; gap: 16px;
			&:first-child{border-radius: 0;}
			&:last-child{border-radius: 0;}
		}
	}
	.cd-bt-item-image{
		display: flex; align-items: center; justify-content: center; width: 128px; height: 128px; flex-grow: 0; flex-shrink: 0;
		img{display: block; width: auto; height: auto; max-width: 100%;}
		@media (max-width: @t){width: 112px; height: 112px;}
		@media (max-width: @m){width: 104px; height: 104px;}
	}
	.cd-bt-item-right{
		display: flex; gap: 24px; width: 100%;
		@media (max-width: @m){gap: 16px;}
	}
	.cd-bt-item-content{display: flex; flex-wrap: wrap; width: 100%; flex-grow: 1; flex-shrink: 1;}
	.cd-bt-item-manufacturer{
		display: block; width: 100%; font-size: var(--fontSizeLabel); font-weight: bold; text-transform: uppercase; color: var(--colorBaseBlue); margin-bottom: 3px; text-decoration: none;
		@media (max-width: @m){font-size: 12px; line-height: 15px; margin: 0;}
	}
	.cd-bt-item-title{
		display: block; font-size: var(--fontSizeSmall); line-height: 17px; font-weight: bold; width: 100%; margin-bottom: 7px; color: var(--colorBase); text-decoration: none; .transition(color);
		@media (min-width: @h){
			&:hover{color: var(--colorBaseBlue);}
		}
		@media (max-width: @m){font-size: 13px; line-height: 17px; margin: 0;}
	}
	.cd-bt-item-code{
		display: block; width: 100%; font-size: 13px; line-height: 17px; margin-bottom: 12px;
		@media (max-width: @t){margin-bottom: 10px;}
		@media (max-width: @m){font-size: 12px; line-height: 16px; margin: 0 0 12px;}
	}
	.cd-bt-item-qty{
		position: relative; display: block; width: 85px; height: 40px; margin-right: 10px; flex-grow: 0; flex-shrink: 0;
		input{width: 100%; height: 100%;}
		:deep(.qty-input-container){height: 100%;}
		:deep(.qty-btn){flex: 0 0 23px;}
	}
	.cd-bt-item-add{
		position: relative; display: block;
		input[type=checkbox]+label{
			position: relative; display: block; width: 100%; height: 40px; border: 1px solid var(--colorBorderLightForms); border-radius: var(--inputBorderRadius); font-size: 13px; line-height: 17px; font-weight: bold; padding: 12px 14px 11px 39px;
			&:before{width: 18px; height: 18px; top: 10px; left: 12px; font-size: 11px; line-height: 19px;}
			@media (max-width: @t){padding: 12px 14px 10px 39px;}
		}
		input[type=checkbox]:checked+label{background: var(--colorLightBlueBackground); color: var(--colorBaseBlue);}
		input[type=checkbox]:disabled+label{
			background: var(--colorLightBlueBackground); color: #BFCBD7;
			&:before{background: #BFCBD7; border-color: #BFCBD7;}
		}
	}
	.cd-bt-item-price{
		display: flex; flex-flow: column; align-items: flex-end; width: 106px; flex-grow: 0; flex-shrink: 0; padding-top: 21px;
		&.mobile{display: none;}
		@media (max-width: @t){
			align-items: flex-start; width: 100%; margin-bottom: 10px; display: none; padding-top: 0;
			&.mobile{display: flex;}
		}
		@media (max-width: @m){margin-bottom: 12px;}
	}
	.cd-bt-item-old-price{text-decoration: line-through; font-size: var(--fontSizeLabel);}
	.cp-price-label{font-size: var(--fontSizeLabel);}
	.cd-bt-item-current-price{
		display: block; font-size: var(--fontSizeSmall); line-height: 17px; font-weight: bold;
		&.discount{color: var(--colorRed);}
		@media (max-width: @m){font-size: 13px;}
	}
	.cd-bt-item-variations{position: relative; display: flex; flex-flow: wrap; width: 100%;}
	.cd-bt-item-variations-title{display: flex; width: 100%; margin: 0 0 7px; font-size: 13px; font-weight: bold;}
	.cd-bt-item-variation-add-container{padding-top: 15px; display: flex;}
	.cd-bt-item-variation-items{display: flex; width: 100%; flex-wrap: wrap;}
	.cd-bt-item-variation-item{
		display: flex; align-items: center; justify-content: center; border: 1px solid var(--colorBorderLightForms); margin-left: -1px; margin-top: -1px; padding: 7px 11px; font-size: 12px; line-height: 16px; min-width: 32px; cursor: pointer; transition: border-color 0.3s, background 0.3s, color 0.3s;
		&:first-child{border-radius: var(--inputBorderRadius) 0 0 var(--inputBorderRadius);}
		&:last-child{border-radius: 0 var(--inputBorderRadius) var(--inputBorderRadius) 0;}
		&:hover, &.selected{background: var(--colorBaseBlue); border-color: var(--colorBaseBlue); color: var(--colorWhite);}
	}
	.cd-bt-footer{
		position: relative; display: flex; width: 100%; margin-top: 16px; gap: 24px;
		@media (max-width: @t){margin-top: 15px;}
		@media (max-width: @t){flex-wrap: wrap; gap: 16px;}
	}
	.cd-bt-chosen{
		display: flex; align-items: center; font-size: var(--fontSizeSmall); line-height: 17px; font-weight: bold; flex-grow: 1; flex-shrink: 1;
		&.saving{align-items: start; margin-top: 7px;}
		span{color: var(--colorBaseBlue);}
	}
	.cd-bt-chosen-price{
		position: relative; display: flex; flex-flow: column; align-items: flex-end; flex-grow: 0; flex-shrink: 0; align-self: center;
	}
	.cd-bt-chosen-add{
		flex-grow: 0; flex-shrink: 0; cursor: pointer;
		.m{display: none;}
		@media (max-width: @t){
			width: 100%;
			.d{display: none;}
			.m{display: block;}
		}
	}
	.cd-bt-total-price{display: block; font-size: 17px; line-height: 24px; font-weight: bold;}
	.cd-bt-total-saving{display: block; font-size: var(--fontSizeSmall); line-height: 17px; font-weight: bold; color: var(--colorGreen);}
</style>
