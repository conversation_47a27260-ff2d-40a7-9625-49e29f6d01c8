<template>
	<template v-if="props.item.discount_percent > 0 || props.item.priority_details?.title || specialBadge">
		<div class="cd-badges" v-if="item.status != 4">
			<template v-if="item.discount_percent > 0">
				<div class="cd-badge cd-badge-discount">-{{item.discount_percent}}%</div>
			</template>
			<template v-else-if="item.priority_details?.title">
				<div class="cd-badge cd-badge-new">{{item.priority_details.title}}</div>
			</template>
			<div class="cd-badge cd-badge-special-attribute" v-if="specialBadge">{{specialBadge}}</div>
		</div>
	</template>
</template>

<script setup>
	const props = defineProps({
		item: {
			type: Object,
			required: true
		}
	});

	const specialBadge = computed(() => {
		if(!props.item.attributes?.length) return false;
		const badge = props.item.attributes.find(x => x.attribute_code == 'posebne_oznake');
		return (badge ? badge.title : false);
	});
</script>

<style scoped lang="less">
	.cd-badges{
		position: absolute; z-index: 1; top: 0; left: 16px; right: 16px; display: flex; justify-content: space-between; z-index: 2;
		@media (max-width: @m){top: 0; left: 0; right: 0;}
	}
	.cd-badge{
		position: relative; display: block; font-size: 14px; line-height: 15px; background: var(--colorBaseBlue); color: var(--colorWhite); font-weight: bold; padding: 6px 10px; border-radius: var(--inputBorderRadius);
		@media (max-width: @t){padding: 7px 8px;}
		@media (max-width: @m){font-size: var(--fontSizeLabel); line-height: 15px; padding: 4px 8px;}
	}
	.cd-badge-special-attribute{color: var(--colorBase); background: var(--colorYellow);}
	.cd-badge-discount{background: var(--colorRed);}
</style>
