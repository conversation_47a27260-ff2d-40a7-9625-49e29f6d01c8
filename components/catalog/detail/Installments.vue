<template>
	<div class="cd-installment-payment" @click="modal.open('custom-modal')">
		<div class="cd-installment-payment-title"><BaseCmsLabel code="installment_payment_title" /></div>
		<div class="cd-installment-payment-description" v-html="labels.get('installments_min_price').replace('%PRICE%', formatCurrency((selectedVariation ? selectedVariation.price_custom : item.price_custom) / 12))"></div>
	</div>
	<ClientOnly>
		<Teleport to="body">
			<BaseUiModal name="custom-modal" v-slot="{onClose, active}">
				<template v-if="active">
					<div class="installments-modal-container">
						<div class="installments-modal-cnt">
							<div class="installments-modal-header">
								<div class="installments-modal-title">
									<BaseCmsLabel code="installments_title" />
								</div>
								<div class="installments-modal-close" @click="onClose"></div>
							</div>

							<div class="installments-modal-body">
								<BaseCmsLabel tag="div" class="title" code="installments_calc_title" />
								<div class="calculated-installments" v-if="selectedVariation || item">
									<ul>
										<template v-for="installment in 12" :key="installment">
											<li v-if="installment > 1"><BaseCmsLabel code="installment_calc_row" /> {{ installment }} x {{ formatCurrency((selectedVariation ? selectedVariation.price_custom : item.price_custom) / installment) }}</li>
										</template>
									</ul>
								</div>

								<div class="cd-installments-calculate-desc" v-interpolation v-html="labels.get('installments_info')"></div>
								<div class="installments-more-info" v-interpolation v-html="labels.get('installments_more_info')"></div>
							</div>
						</div>
					</div>
					<div class="modal-bg-close" @click="onClose"></div>
				</template>
			</BaseUiModal>
		</Teleport>
	</ClientOnly>
</template>

<script setup>
	const labels = useLabels();
	const modal = useModal();
	const {formatCurrency} = useCurrency();
	const props = defineProps(['item', 'selectedVariation']);
</script>

<style scoped lang="less">
	.cd-installment-payment{
		background: var(--colorDarkBlue); padding: 13px 66px 13px 17px; color: var(--colorWhite); margin-left: auto; display: block; border-radius: var(--borderRadius); cursor: pointer; flex-shrink: 0; position: relative;
		&:after{.pseudo(38px,38px); background: var(--colorYellow); border-radius: 200px; right: 16px; top: 15px; display: flex; align-items: center; justify-content: center; .icon-calculator(); font: 22px/1 var(--fonti); color: #000;}
		@media (max-width: @t){
			width: 100%; margin-left: unset; margin-top: 14px; padding: 14px 92px 15px 19px; position: relative;
			&:before{width: 44px; height: 44px; right: 18px; top: 14px; font-size: 24px;}
		}
		@media (max-width: @m){order: 4;}
	}
	.cd-installment-payment-title{
		position: relative; display: block; font-size: 13px; line-height: 22px; font-weight: bold; color: var(--colorWhite); padding-left: 30px;
		&:before{.pseudo(22px,18px); .icon-cards(); font: 22px/22px var(--fonti); color: var(--colorYellow); top: 0; left: 0;}
	}
	.cd-installment-payment-description{
		font-size: 13px; line-height: 16px; color: var(--colorWhite); padding-top: 4px;
		:deep(strong){color: var(--colorYellow);}
		@media (max-width: @m){padding-left: 30px;}
	}

	//Modal
	.installments-modal-container{
		position: fixed; inset: 0; z-index: 1150; background: var(--colorWhite); left: unset; right: 0; width: 400px;
		@media(max-width: @m){width: 100%; left: 0; right: 0; top: 16px; border-radius: 4px 4px 0 0;}
	}
	.installments-modal-cnt{
		overflow: auto; width: calc(~"100% - 10px"); height: 100%; padding: 32px 14px 32px 24px;
		&::-webkit-scrollbar{-webkit-appearance: none; width: 3px; background-color: var(--colorBorderLightForms); cursor: pointer; border: 0; border-radius: 6px;}
		&::-webkit-scrollbar-track-piece{margin: 32px 0px;}
		&::-webkit-scrollbar-track{border-top: 32px solid white; border-bottom: 32px solid white; width: 3px; border-radius: 6px;}
		&::-webkit-scrollbar-thumb{background-color: var(--colorBaseBlue); cursor: pointer; width: 3px; border-radius: 6px;}
		@media(max-width: @m){
			padding: 21px 10px 18px 16px;
			&::-webkit-scrollbar-track-piece{margin: 18px 0px;}
			&::-webkit-scrollbar-track{border-top: 18px solid white; border-bottom: 18px solid white;}
		}
	}
	.installments-modal-header{
		display: flex; align-items: center; justify-content: space-between; background: var(--colorWhite); margin-bottom: 16px;
		@media (max-width: @m){margin-bottom: 13px;}
	}
	.installments-modal-close{
		font-size: 0; width: 36px; height: 36px; background: var(--colorBaseBlue); border-radius: 200px; cursor: pointer; display: flex; justify-content: center; align-items: center;
		&:before{.icon-close(); font: 11px/1 var(--fonti); color: var(--colorWhite);}
		@media (max-width: @m){width: 34px; height: 34px;}
	}
	.installments-modal-title{
		font-size: 20px; line-height: 1.4; font-weight: bold;
		@media (max-width: @m){font-size: 18px; line-height: 1.5;}
	}
	.modal-bg-close{content: ""; position: fixed; display: block; width: auto; height: auto; top: 0; right: 0; bottom: 0; left: 0; background: rgba(0, 12, 26, 0.6); z-index: 1149;}

	:deep(.title){
		font-size: 16px; line-height: 1.5; color: var(--colorBase); font-weight: bold; padding-bottom: 8px;
		span{font-weight: normal; font-size: 13px;}
	}
	.calculated-installments{
		ul{
			list-style: none; margin: 0 0 16px; padding: 0; font-size: 14px; line-height: 1.2;
			li{
				padding: 8px 14px; background: var(--colorWhite); border-radius: var(--borderRadius);
				&:nth-child(odd){background: var(--colorLightBlueBackground);}
			}
			@media (max-width: @m){margin-bottom: 20px;}
		}
	}
	.cd-installments-calculate-desc{
		display: block;

		:deep(ul){
			list-style: none; padding: 0; margin: 0 0 16px 0; line-height: 1.4; font-size: 14px;
			li{
				position: relative; padding: 0 0 3px 16px;
				&:before{.pseudo(5px, 5px); border: 2px solid var(--colorBaseBlue); top: 6px; left: 0; border-radius: 100%;}
			}
		}
		:deep(.extra){margin: 14px 0; padding: 9px 16px; font-size: 11px; line-height: 1.5; border-radius: var(--borderRadius);}
		:deep(.extra-instalment){
			background: #03172E; border-radius: var(--borderRadius); color: var(--colorWhite)!important; padding: 14px 16px 16px; margin: 14px 0;
			.title{color: var(--colorWhite);}
			strong{margin-bottom: 6px; display: block; font-size: 13px;}
			ul{font-size: 13px; margin-bottom: 8px;}
		}
		@media (max-width: @m){
			:deep(.title){padding-bottom: 6px;}
			:deep(ul){margin: 0 0 8px 0;}
			:deep(.extra){padding: 12px 16px 14px; font-size: 13px;}
			:deep(.extra-instalment){
				padding: 14px 16px 10px;
				strong{margin-bottom: 5px;}
			}
		}
	}
	.installments-more-info{
		font-size: 14px; line-height: 1.5; margin-top: 16px;
		:deep(a){
			text-decoration: underline; font-weight: bold; color: var(--colorBaseBlue); text-underline-offset: 3px; .transition(text-decoration-color);
			&:hover{text-decoration-color: transparent;}
		}
		@media (max-width: @m){margin-top: 20px;}
	}

	/* animations */
	.fade-enter-active,
	.fade-leave-active {
		transition: opacity 0.2s ease;
	}

	.fade-enter-from,
	.fade-leave-to {
		opacity: 0;
	}
</style>
