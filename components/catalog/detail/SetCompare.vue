<template>
	<div class="cd-compare-container">
		<BaseCatalogSetCompare :item="item" v-slot="{onToggleCompare, active, message, loading, compareUrl}">
			<div class="cp-compare cp-btn compare cd-compare cd-btn-compare" :class="{'loading': loading, 'compare_active': active && !loading, 'cd-compare cd-btn-comapre cd-btn-compare-list': mode == 'catalogDetail'}" @click="onToggleCompare">
				<span class="l1"><BaseCmsLabel code="add_to_compare" /></span>
				<span class="l2"><BaseCmsLabel code="remove_from_compare" /></span>
				<span class="l1-m"><BaseCmsLabel code="add_to_compare2" /></span>
				<span class="l2-m"><BaseCmsLabel code="remove_from_compare2" /></span>
			</div>
			<span :class="['cp-compare-info', 'cd-compare-info', 'compare_message', message]" v-show="message">
				<span v-if="message == 'compare_error_limit'" v-html="labels.get('compare_error_limit').replace('%view_compare_url%', compareUrl)" />
				<template v-else>
					<span v-if="active" v-html="labels.get('compare_ok_added').replace('%view_compare_url%', compareUrl)" />
					<span v-if="!active" v-html="labels.get('compare_ok_removed').replace('%view_compare_url%', compareUrl)" />
				</template>
			</span>
		</BaseCatalogSetCompare>
	</div>
</template>

<script setup>
	const labels = useLabels();
	const props = defineProps(['item', 'mode']);
</script>

<style lang="less" scoped>
	// catalog detail compare
	.cd-compare-container{
		position: absolute; top: 0; right: 0; width: 130px;
		@media (max-width: @t){position: relative; width: 100%; margin-top: 15px;}
	}
	.cd-btn-compare{
		position: relative; display: flex; align-items: center; justify-content: center; height: 48px; padding: 0 24px; font-weight: bold; font-size: 16px; line-height: 24px; text-decoration: underline; text-decoration-color: var(--colorBaseBlue); text-underline-offset: 5px; cursor: pointer; transition: text-decoration-color 0.3s;
		.l1, .l2, .l1-m, .l2-m{
			position: relative; display: inline-block; padding-left: 24px;
			&:before{.pseudo(19px,19px); .icon-compare(); font: 19px/19px var(--fonti); top: 2px; left: 0; color: var(--colorBaseBlue);}
		}
		&.loading{
			span:before{background: url(assets/images/loader.svg) no-repeat center; background-size: 22px auto; content:"";}
		}
		.l1-m, .l2-m{display: none;}
		.l2, .l2-m{display: none;}
		&.compare_active{
			.l1{display: none;}
			.l2{display: inline-block;}
		}
		@media (min-width: @h){
			&:hover{text-decoration-color: transparent;}
		}
		@media (max-width: @t){
			width: 100%; border: 1px solid var(--colorBorderLightForms); border-radius: var(--inputBorderRadius); flex-grow: 0; flex-shrink: 0; background: var(--colorWhite); text-decoration: none; font-size: 14px;
			.l1, .l2, .l1-m, .l2-m{
				display: none;
				&:before{width: 17px; height: 17px; font-size: 17px; line-height: 17px; top: 3px;}
			}
			.l1-m{display: inline-block;}
			&.compare_active{
				.l1-m, .l2{display: none;}
				.l2-m{display: inline-block;}
			}
		}
	}
	.cp-compare-info{
		background: var(--colorWhite); border-radius: var(--borderRadius); white-space: nowrap; box-shadow: 0 2px 7px rgba(0, 0, 0, 0.2); font-size: 12px; line-height: 1.4; position: absolute; top: calc(~'100% - -10px'); right: -10px; padding: 7px 12px; z-index: 11;
		&:after{.pseudo(8px,8px); background: var(--colorWhite); .rotate(45deg); top: -4px; right: 26px; }
		:deep(a){color: var(--textColor); text-decoration-color: var(--colorBaseBlue); text-underline-offset: 3px;}
		@media (max-width: @t){right: auto; left: auto;}
		@media (max-width: @m){
			font-size: 12px; padding: 8px; top: calc(~'100% - -7px');
			&:after{top: -3px; right: auto; left: calc(~"50% - 3px"); width: 6px; height: 6px;}
		}
	}
	@media (max-width: @t){
		.cd-compare-info{left: 50%; .translate(-50%, 0);}
	}
	.cp-compare-info:after{right: 68px;}
	.compare_error_limit{
		width: 230px; white-space: normal;
		@media (max-width: @m){width: 260px; padding: 10px 20px; text-align: center;}
	}
</style>
