<template>
	<div class="add-to-cart-bar" v-if="item.price_custom > 0">
		<div class="wrapper add-to-cart-bar-wrapper">
			<ClientOnly>
				<div class="add-to-cart-bar-image">
					<BaseUiImage :src="item.main_image_upload_path" width="72" height="72" loading="lazy" default="/images/no-image-80.jpg" />
				</div>
				<div class="add-to-cart-bar-title">{{item.title}}</div>
			</ClientOnly>
			<div id="add-to-cart-bar-container" class="add-to-cart-bar-container"></div>
		</div>
	</div>
</template>

<script setup>
	const props = defineProps(['item']);
</script>

<style lang="less" scoped>
	.add-to-cart-bar{
		position: fixed; bottom: 0; left: 0; right: 0; height: auto; background: var(--colorWhite); box-shadow: 0 -5px 20px rgba(0,0,0,0.15); z-index: 10; padding: 17px 0; .transition(bottom);
		@media (max-width: @t){padding: 10px 0;}
	}
	.add-to-cart-bar-wrapper{position: relative; display: flex; align-items: center;}
	.add-to-cart-bar-image{
		position: relative; display: flex; margin-right: 16px; width: 72px; height: auto; flex-grow: 0; flex-shrink: 0;
		img{display: block; width: auto; height: auto; max-width: 100%;}
		@media (max-width: @m){display: none;}
	}
	.add-to-cart-bar-title{
		position: relative; display: block; font-weight: bold; max-width: 450px; line-height: 1.4; margin-right: auto; font-size: 16px;
		@media (max-width: @t){font-size: 14px; padding-right: 20px;}
		@media (max-width: @m){display: none;}
	}
	.add-to-cart-bar-container{
		display: flex; align-items: center; flex-shrink: 0;
		@media (max-width: @m){width: 100%;}
	}
</style>
