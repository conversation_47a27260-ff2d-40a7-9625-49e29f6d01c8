<template>
	<BaseFaqQuestions :fetch="{catalogproduct_id: item.id, catalogcategory_id: item.category_id, category_code: 'faq_product'}" mode="product" v-slot="{items:faqItems}">
		<BaseCatalogProductsWidget :fetch="{related_code: 'related_rules', related_item_id: item.id, related_widget_data: item.related_widget_data, always_to_limit: true, limit: 15, id_exclude: item.id, only_with_image: true, only_available: true}" v-slot="{items: similarProducts}">
			<BaseCatalogProductsWidget :fetch="{related_code: 'related', related_item_id: item.id, limit: 15, id_exclude: item.id, only_with_image: true, only_available: true}" v-slot="{items: relatedProducts}">
				<BasePublishPostsWidget :fetch="{related_product_id: item.id, limit: 3}" v-slot="{items: publishItems}">
					<div class="cd-tabs-container" v-if="item.content || item.documents?.length || item.element_description || itemsAttributes?.length || faqItems?.length || similarProducts?.length || relatedProducts?.length || hasLastViewProducts || publishItems?.length">
						<ClientOnly>
							<div class="cd-tabs" v-if="item.content || item.documents?.length || item.element_description || itemsAttributes?.length || faqItems?.length || similarProducts?.length || relatedProducts?.length || publishItems?.length">
								<div class="wrapper cd-tab-wrapper">
									<div v-if="item.content || item.element_description" @click="scrollTo('#tab-desc', {offset: 170})" class="cd-tab-title" :class="{'active': activeTab === 'tab-desc'}">
										<span><BaseCmsLabel code="product_description" /></span>
									</div>
									<div @click="scrollTo('#tab-docs', {offset: 170})" class="cd-tab-title" :class="{'active': activeTab === 'tab-docs'}" v-if="item.documents?.length">
										<span><BaseCmsLabel code="product_download_docs" /></span>
									</div>
									<div @click="scrollTo('#tab-specs', {offset: 170})" class="cd-tab-title" :class="{'active': activeTab === 'tab-specs'}" v-if="itemsAttributes?.length">
										<span><BaseCmsLabel code="product_specifications" /></span>
									</div>
									<div @click="scrollTo('#tab-faq', {offset: 170})" class="cd-tab-title" :class="{'active': activeTab === 'tab-faq'}" v-if="faqItems?.length">
										<span><BaseCmsLabel code="tabs_product_faq" /></span>
									</div>
									<div @click="scrollTo('#tab-similar', {offset: 170})" class="cd-tab-title" :class="{'active': activeTab === 'tab-similar'}" v-if="similarProducts?.length">
										<span><BaseCmsLabel code="similar_products" /></span>
									</div>
									<div @click="scrollTo('#tab-related', {offset: 170})" class="cd-tab-title" :class="{'active': activeTab === 'tab-related'}" v-if="relatedProducts?.length">
										<span><BaseCmsLabel code="related_products" /></span>
									</div>
									<div @click="scrollTo('#tab-posts', {offset: 170})" class="cd-tab-title" :class="{'active': activeTab === 'tab-posts'}" v-if="publishItems?.length">
										<span><BaseCmsLabel code="related_posts" /></span>
									</div>
								</div>
							</div>
							<div class="cd-tabs-empty" v-else />
						</ClientOnly>

						<div class="cd-tabs-content" :class="{'no-tabs': !item.content && !item.documents?.length && !item.element_description && !itemsAttributes?.length && !faqItems?.length && !similarProducts?.length && !relatedProducts?.length && !publishItems?.length}">
							<div v-if="item.content || item.element_description" class="cd-tab-body cd-tab-body-content" id="tab-desc" ref="tabDesc" data-observer-id="tab-desc">
								<div class="wrapper cd-tabs-content-wrapper">
									<div class="cd-tab-body-title"><BaseCmsLabel code="product_description" /></div>
									<div id="itemContent" class="cd-tab-content cms-content cd-content-desc" v-interpolation>
										<div v-html="item.content" ref="content"></div>
										<div v-if="item.element_description" :class="{'extra-content-spacing': item.content}" v-html="item.element_description" />
									</div>
								</div>
							</div>

							<div class="cd-tab-body cd-tab-body-docs" v-if="item.documents?.length" id="tab-docs" data-observer-id="tab-docs" ref="tabDocs">
								<div class="wrapper cd-tabs-content-wrapper">
									<div class="cd-tab-body-title"><BaseCmsLabel code="product_docs" /></div>
									<div class="cd-tab-content">
										<ul class="cd-documents">
											<li v-for="document in item.documents" :key="document.id">
												<a :href="document.url" target="_blank">{{document.title ? document.title : document.file}}</a>
											</li>
										</ul>
									</div>
								</div>
							</div>

							<div class="cd-tab-body cd-tab-body-specifications" v-if="itemsAttributes?.length" id="tab-specs" data-observer-id="tab-specs" ref="tabSpecs">
								<div class="wrapper cd-tabs-content-wrapper">
									<div class="cd-tab-body-title"><BaseCmsLabel code="product_specifications" /></div>
									<div id="itemContent" class="cd-tab-content specs-content">
										<div class="spec-items">
											<div class="spec-item" v-for="itemAttribute in itemsAttributes" :key="itemAttribute.id">
												<div class="spec-item-title">{{itemAttribute.attribute_title}}</div>
												<div class="spec-item-content">
													{{ itemAttribute.title }}
													<template v-if="itemAttribute.unit"> {{ itemAttribute.unit }}</template>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>

							<div class="cd-tab-body cd-tab-body-faq" :class="{'overflow': faqItems?.length > 4, 'active': activeFaq}" id="tab-faq" data-observer-id="tab-faq" ref="tabFaq" v-if="faqItems?.length">
								<div class="wrapper cd-tabs-content-wrapper">
									<div class="cd-tab-body-title"><BaseCmsLabel code="tabs_product_faq" /></div>
									<div class="cd-tab-content faq-content" :class="{'active': activeFaq}" ref="catalog-faq-content">
										<CmsFaqItem v-for="(faq_item, index) in faqItems" :key="faq_item.id" :item="faq_item" :index="index" />
									</div>
									<ClientOnly>
										<div class="cdtab-btn-more" @click="activeFaq = !activeFaq" id="faq-content">
											<span class="i"><BaseCmsLabel code="tabs_product_faq_button" /></span>
										</div>
										<div class="faq-more-info" :class="{'hide': faqItems?.length < 4}">
											<BaseCmsLabel code="faq_more_info_title" v-interpolation tag="div" class="faq-more-info-title" />
											<BaseCmsLabel code="faq_more_info_contact" v-interpolation tag="div" class="faq-more-info-contact" />
										</div>
									</ClientOnly>
								</div>
							</div>

							<ClientOnly>
								<div class="wrapper cd-tabs-content-wrapper cd-disclaimer">
									<div class="cd-disclaimer-cnt"><BaseCmsLabel code="disclaimer" /></div>
								</div>

								<div class="cd-share-container">
									<div class="wrapper cd-tabs-content-wrapper">
										<CmsShare />
									</div>
								</div>
							</ClientOnly>

							<div data-observer-id="tab-similar" ref="tabSimilar">
								<CatalogSpeciallists :items="similarProducts" class="cd-tab-body cd-related-products first" id="tab-similar" list="similar" v-if="similarProducts?.length">
									<template #header>
										<div class="cd-tab-body-title cd-related-title"><BaseCmsLabel code="similar_products" /></div>
									</template>
								</CatalogSpeciallists>
							</div>

							<div data-observer-id="tab-posts" ref="tabPosts">
								<PublishSpecial :items="publishItems" class="cd-tab-body cd-pw-special-cnt" id="tab-posts" :with-category="true" v-if="publishItems?.length">
									<template #header>
										<div class="cd-tab-body-title cd-related-title"><BaseCmsLabel code="read_another" /></div>
									</template>
								</PublishSpecial>
							</div>

							<div data-observer-id="tab-related" ref="tabRelated">
								<CatalogSpeciallists :items="relatedProducts" class="cd-tab-body cd-related-products second" id="tab-related" list="related" v-if="relatedProducts?.length">
									<template #header>
										<div class="cd-tab-body-title cd-related-title"><BaseCmsLabel code="related_products" /></div>
									</template>
								</CatalogSpeciallists>
							</div>

							<ClientOnly>
								<BaseCatalogProductsWidget :fetch="{sort: 'last_view', limit: 15, viewed: true, only_with_image: true, only_available: true}" :cache="false" v-slot="{items: lastViewProducts}" @loadProductsWidget="onLastViewLoad">
									<CatalogSpeciallists :items="lastViewProducts" class="cd-tab-body cd-related-products" id="tab-last-view" list="last-view" v-if="lastViewProducts?.length">
										<template #header>
											<div class="cd-tab-body-title cd-related-title"><BaseCmsLabel code="last_view_products" /></div>
										</template>
									</CatalogSpeciallists>
								</BaseCatalogProductsWidget>
							</ClientOnly>
						</div>
					</div>
				</BasePublishPostsWidget>
			</BaseCatalogProductsWidget>
		</BaseCatalogProductsWidget>
	</BaseFaqQuestions>
</template>

<script setup>
	const {scrollTo, onElementVisibility} = useDom();
	const props = defineProps(['item']);

	// attributes
	const itemsAttributes = computed(() => {
		if(!props.item.attributes) return [];
		return props.item.attributes.filter(item => !['posebne_oznake', 'linija'].includes(item.attribute_code));
	});

	const activeContent = ref(false);
	const activeSpecs = ref(false);
	const activeFaq = ref(false);

	const hasLastViewProducts = ref(false);
	function onLastViewLoad(data) {
		if(data?.items?.length) hasLastViewProducts.value = true;
	}

	const activeTab = ref(null);
	const tabDesc = ref(null);
	const tabDocs = ref(null);
	const tabSpecs = ref(null);
	const tabFaq = ref(null);
	const tabSimilar = ref(null);
	const tabPosts = ref(null);
	const tabRelated = ref(null);
	onElementVisibility({
		target: [tabDesc, tabDocs, tabSpecs, tabFaq, tabSimilar, tabPosts, tabRelated],
		options: {rootMargin: "-200px 0px 0px 0px", treshold: 0},
		callback: ({visibleElements}) => {
			activeTab.value = visibleElements?.length ? visibleElements[0] : null;
		},
	});
</script>

<style lang="less" scoped>
	.cd-share-container{position: relative; display: block; padding-bottom: 80px; border-bottom: 1px solid var(--colorBorderLightForms); margin-bottom: 80px;
		@media (max-width: @t){margin-bottom: 50px; padding-bottom: 50px;}
		@media (max-width: @m){border: 0; padding: 0; margin-bottom: 40px;}
	}
	.cd-tabs-container{
		position: relative; display: block; width: 100%; z-index: 0;
		@media (max-width: @m){margin-top: 48px;}
	}
	.cd-tabs{
		position: sticky; top: 55px; display: block; background: var(--colorWhite); z-index: 10; width: 100%; border-top: 1px solid #E6E8EC; border-bottom: 1px solid #E6E8EC; box-shadow: 0 5px 20px rgba(0,0,0,0.15); height: 81px;
		@media (max-width: @t){height: 72px;}
		@media (max-width: @m){
			height: 54px;
			&:before{.pseudo(52px,100%); top: 0; right: 0; background: linear-gradient(270deg, #FFFFFF 0%, rgba(255,255,255,0.98) 25.71%, rgba(255,255,255,0) 100%); z-index: 1;}
		}
	}
	.cd-tabs-empty{
		border-bottom: 1px solid var(--colorBorderLightForms); height: 1px;
		@media (max-width: @m){display: none;}
	}
	.cd-tab-wrapper{
		display: flex; align-items: center; justify-content: center; height: 100%; gap: 40px;
		@media (max-width: @t){gap: 35px;}
		@media (max-width: @m){
			gap: 24px; justify-content: flex-start; overflow-x: auto; position: relative;
			&::-webkit-scrollbar{display: none;}
			&::-webkit-scrollbar-thumb{display: none;}
		}
	}
	.cd-tab-title{
		position: relative; height: 100%; display: flex; align-items: center; justify-content: center; flex-grow: 0; flex-shrink: 0; font-size: 19px; line-height: 1.4; font-weight: bold; .transition(color); cursor: pointer;
		@media (max-width: @t){font-size: 16px;}
		&:after{.pseudo(100%,2px); background: var(--colorBaseBlue); bottom: 0; left: 0; opacity: 0; .transition(opacity);}
		&.active{
			color: var(--colorBaseBlue);
			&:after{opacity: 1;}
		}
		@media (min-width: @h){
			&:hover{color: var(--colorBaseBlue);}
		}
		@media(max-width: @m){font-size: 15px; line-height: 17px;}
	}
	.cd-tabs-content{
		position: relative; display: block; padding: 65px 0 0;
		@media (max-width: @t){padding-top: 55px;}
		@media (max-width: @m){
			padding: 30px 15px 0; overflow: hidden;
			&.no-tabs{
				padding-top: 0;
				.cd-disclaimer{padding-top: 0;}
			}
		}
	}
	.cd-tabs-content-wrapper{
		max-width: 775px;
		@media (max-width: @t){max-width: 770px; padding: 0;}
		@media (max-width: @m){width: 100%;}
	}
	.cdtab-btn-more{
		position: relative; display: none;
		@media (max-width: @m){
			display: none; font-size: 13px; line-height: 22px; text-decoration: underline; color: var(--colorBaseBlue);
			&.show{display: block;}
			span{
				position: relative; display: inline-block; padding-right: 13px; text-decoration: underline; text-underline-offset: 4px;
				&:before{.pseudo(8px,8px); .icon-arrow2(); font: 8px/8px var(--fonti); color: var(--colorBaseBlue); top: 8px; right: 0; display: flex; align-items: center; justify-content: center; .rotate(-90deg);}
				&.a{
					&:before{.rotate(90deg);}
				}
			}
			span.a{display: none;}
			&.active{
				span.i{display: none;}
				span.a{display: inline-block;}
			}
		}
	}
	.cd-tab-body{
		margin-bottom: 65px;
		@media (max-width: @m){
			margin-bottom: 45px;
			:deep(.wrapper){padding: 0;}
		}
	}
	.cd-tab-body-faq {
		margin-bottom: 20px;
		.cdtab-btn-more{margin-top: 10px;}
		@media (max-width: @m){
			&.overflow{
				.cd-tab-content{
					max-height: 100%; overflow: visible;
					&:before{display: none;}
				}
			}
		}
	}
	.cd-documents{
		display: flex; flex-flow: column; gap: 17px; list-style: none; padding: 0; margin: 0;
		@media (max-width: @m){gap: 10px; padding-top: 5px;}
		a{
			display: flex; align-items: center; position: relative; text-decoration: none; gap: 10px; color: var(--textColor); .transition(color); word-break: break-all;
			&:before{
				.pseudo(23px,23px); background: url('/assets/images/icons/pdf2.svg') center no-repeat; background-size: contain; position: relative;
				@media (max-width: @m){width: 20px; height: 20px;}
			}
			&:hover{color: var(--colorBaseBlue);}
		}
	}
	.cms-content{
		:deep(p):last-of-type{padding-bottom: 0;}
	}
	.extra-content-spacing{padding-top: 30px;}
	@media (max-width: @m){
		.overflow{
			.cd-tab-content{
				max-height: 120px; overflow: hidden; position: relative;
				&:before{.pseudo(100%,66px); background: linear-gradient(0deg, #FFFFFF 0%, rgba(255,255,255,0.98) 19.41%, rgba(255,255,255,0) 100%); bottom: 0; left: 0; z-index: 1;}
			}
			.specs-content{
				max-height: 275px;
			}
			.cdtab-btn-more{display: block;}
			&.active{
				.cd-tab-content, .specs-content, .faq-content{
					max-height: none; overflow: visible;
					&:before{display: none;}
				}
				.cdtab-btn-more{display: none;}
			}
		}
	}
	.specs-content{
		@media (max-width: @m){
			width: calc(~"100% - -30px"); margin-left: -15px;
		}
	}
	.cd-tab-body-title{
		display: block; font-size: var(--fontSizeH3); line-height: 1.3; font-weight: bold; padding-bottom: 22px;
		@media (max-width: @t){font-size: 28px;}
		@media (max-width: @m){font-size: var(--fontSizeH1); font-size: 19px; padding-bottom: 8px; color: var(--colorBaseBlue);}
	}
	.spec-items{
		position: relative; display: flex; flex-flow: column;
		@media (max-width: @m){width: 100%;}
	}
	.spec-item{
		position: relative; display: block; padding: 18px 32px; border-radius: var(--inputBorderRadius);
		&:nth-child(odd){background: var(--colorLightBlueBackground);}
		@media (max-width: @m){padding: 14px 16px; border-radius: 0; font-size: 13px; line-height: 19px;}
	}
	.spec-item-title{
		display: block; font-size: var(--fontSizeSmall); line-height: 1.35; font-weight: bold; color: var(--colorBaseBlue); padding-bottom: 2px;
		@media (max-width: @m){font-size: 15px; line-height: 17px; padding-bottom: 6px;}
	}
	.cd-disclaimer{font-size: 12px; padding: 20px 0; font-style: italic; color: var(--colorTextLightGray);}
	.cd-disclaimer-cnt{max-width: 450px;}

	// faq
	.cd-tab-content.faq-content{
		.faq-item:first-child{border-top: 1px solid var(--colorBorderLightForms);}
		@media (max-width: @m){
			width: calc(~"100% - -30px"); margin-left: -15px;
		}
	}
	.faq-content.active{
		.faq-item.hide{
			@media (max-width: @m){display: block;}
		}
	}
	.faq-item.hide{
		@media (max-width: @m){display: none;}
	}
	.faq-more-info{
		position: relative; display: flex; align-items: center; gap: 40px; background: var(--colorBaseBlue); width: 100%; height: auto; border-radius: var(--inputBorderRadius); color: var(--colorWhite); padding: 40px; overflow: hidden; margin-top: 12px;
		&:before{.pseudo(182px,152px); background: url('/assets/images/bike.svg') center no-repeat; background-size: cover; top: -11px; right: -20px;}
		@media (max-width: @m){
			margin-top: 40px; border-radius: 0; width: calc(~"100% - -30px"); margin-left: -15px; padding: 22px 15px 30px; flex-flow: column; align-items: flex-start; gap: 8px;
			&:before{top: -2px; right: -30px;}
			&.hide{margin-top: 0;}
		}
	}
	.faq-more-info-title{
		display: block; font-size: 15px; line-height: 19px; flex-grow: 0; flex-shrink: 0; width: 245px;
		:deep(strong){font-size: 19px; line-height: 25px;}
		:deep(p){
			padding-bottom: 4px;
			&:last-child{padding-bottom: 0;}
		}
	}
	.faq-more-info-contact{
		position: relative; display: flex; align-items: center; width: 100%; gap: 40px;
		:deep(a){color: var(--colorWhite); font-size: 15px; line-height: 19px; font-weight: bold; text-underline-offset: 5px; text-decoration-thickness: 1px;}
		@media (max-width: @m){flex-flow: column; align-items: flex-start; gap: 10px;}
	}

	// related products
	.cd-related-products{
		position: relative; display: block; width: 100%;
		@media (max-width: @t){
			:deep(.speciallist-items .splide__slide){padding-bottom: 0;}
			:deep(.speciallist-items-slider){
				.splide__arrow{
					width: 32px; height: 32px; box-shadow: none; background: var(--colorBaseBlue);
					&:before{font-size: 12px; color: var(--colorWhite);}
				}
				.splide__arrow--next{right: -35px;}
				.splide__arrow--prev{left: -35px;}
			}
		}
		@media (max-width: @m){
		 	:deep(.speciallist-items-slider) .splide__arrow{display: none;}
		}
	}
	.cd-related-title{
		padding-bottom: 30px;
		@media (max-width: @t){padding: 0 24px 22px;}
		@media (max-width: @m){padding: 0 0 16px; color: var(--colorBase);}
	}

	// related posts
	.cd-pw-special-cnt{
		overflow: hidden; position: relative; padding-bottom: 80px;
		&:before{
			.pseudo(616px,528px); background: url('/assets/images/bike.svg') center no-repeat; background-size: cover; top: 0; right: -59px; opacity: .2;
			@media (max-width: @t){width: 380px; height: 380px; top: -6px; right: -45px;}
			@media (max-width: @m){display: none;}
		}
		.wrapper{position: relative; z-index: 1;}
		@media (max-width: @t){
			:deep(.pw-special-title){font-size: var(--fontSizeH1); padding: 0 24px 30px;}
		}
		@media (max-width: @m){padding-bottom: 20px; margin-inline: -15px; padding-inline: 15px;}
	}
</style>
