<template>
	<BaseCatalogFilters v-slot="{searchFields}" scroll-to-element=".c-main-items">
		<div class="cf-excluded-container" v-if="searchFields">
			<BaseCatalogFilterItem v-for="filter in searchFields" :key="filter.id" :item="filter" v-slot="{fields, onFilter}">
				<div class="cf-excluded-items" v-if="filter.code == 'categories'">
					<div class="cf-excluded-item" :class="{'not-available': field.total_available < 1}" v-for="field in fields" :key="field.id">
						<input type="checkbox" :name="filter.filter_url" :id="'m-'+field.unique_code" :value="field.filter_url" :checked="field.selected" @click="onFilter" />
						<label :for="'m-'+field.unique_code">{{field.title}}</label>
					</div>
				</div>
			</BaseCatalogFilterItem>
		</div>
	</BaseCatalogFilters>
</template>

<style lang="less" scoped>
	.cf-excluded-container{
		position: relative; margin: 24px 0 0; display: none;
		@media (max-width: @m){display: block;}
	}
	.cf-excluded-items{
		display: flex; flex-wrap: wrap; gap: 8px;
		@media (max-width: @m){flex-wrap: inherit; width: calc(~"100% - -30px"); margin-left: -15px; overflow-x: auto; padding: 0 15px;}
	}
	.cf-excluded-item{
		flex-grow: 0; flex-shrink: 0;
		input[type=checkbox] + label{
			padding: 15px 19px; font-size: 14px; line-height: 18px; background: var(--colorWhite); border: 1px solid var(--colorBorderLightForms); border-radius: var(--inputBorderRadius); transition: color 0.3s, border-color 0.3s;
			&:before{display: none;}
			@media (min-width: @h){
				&:hover{color: var(--colorBaseBlue); border-color: var(--colorBaseBlue);}
			}
			@media (max-width: @m){font-size: 13px; line-height: 18px; padding: 6px 14px;}
		}
		input[type=checkbox]:checked + label{
			color: var(--colorBaseBlue); border-color: var(--colorBaseBlue);
			@media (min-width: @h){
				&:hover{color: var(--colorBase); border-color: var(--colorBorderLightForms);}
			}
		}
	}
</style>
