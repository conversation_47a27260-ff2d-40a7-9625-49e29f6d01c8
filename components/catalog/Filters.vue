<template>
	<div class="cf" :class="['cf-' + contentType]" v-if="searchFields.length">
		<div class="cf-header">
			<div class="cf-title" @click="$emit('closeFilters');">
				<span><BaseCmsLabel code="filters" /></span>
			</div>
		</div>
		<BaseCatalogFilterItem v-for="filter in searchFields" :key="filter.id" :item="filter" v-slot="{fields, onFilter, onClear, selectedFilters, active, onToggle, onSearch}">
			<div class="cf-item" :class="['cf-item-' + filter.code, filter.layout ? 'cf-layout-' + filter.layout : '', {'active': active}]" v-if="filter.code !== 'categories' || filter.code === 'categories' && contentType !== 'category'">
				<div class="cf-item-title-cnt">
					<span class="cf-item-title" @click="onToggle">{{ filter.label }}</span>
					<span class="cf-filters-counter" v-show="selectedFilters?.length">{{selectedFilters?.length}}</span>
					<span v-show="selectedFilters?.length" class="cf-filters-remove" @click="onClear"><BaseCmsLabel code="clear" /></span>
					<span class="cf-item-toggle" @click="onToggle"></span>
				</div>
				<div class="cf-item-wrapper">
					<template v-if="filter.layout == 'sf'">
						<BaseCatalogFilterFieldSlider :item="filter" />
					</template>
					<template v-else>
						<div class="cf-search-cnt" v-if="filter.code == 'manufacturer'">
							<input :placeholder="labels.get('search_filters')" class="cf-search-input" type="text" @keyup="onSearch" />
						</div>
						<div class="cf-row" :class="[field.level && 'cf-row-level' + field.level, {'not-available': field.total_available < 1 && !field.selected}]" v-for="field in fields" :key="field.id">
							<input type="checkbox" :name="filter.filter_url" :id="field.unique_code" :value="field.filter_url" :checked="field.selected" @click="onFilter" />
							<label :for="field.unique_code">
								<div class="cf-color-img" v-if="filter.code == 'a_boja' && field.image_upload_path">
									<span><BaseUiImage :src="field.image_upload_path" width="22" height="22" alt="" default="/images/no-image-50.jpg" /></span>
								</div>
								<span>{{field.title}}</span>
								<span class="cf-items-counter">{{ field.total_available }}</span>
							</label>
						</div>
					</template>
				</div>
			</div>
		</BaseCatalogFilterItem>

		<BaseCatalogActiveFilters v-slot="{items, onRemove}">
			<div class="cf-btns">
				<div class="cf-btn-clear btn btn-border" :class="{'active-filters': items?.length}" @click="onRemove(), $emit('closeFilters');"><BaseCmsLabel code="clear_active_filters" /></div>
				<div class="cf-btn-confirm btn" @click="$emit('closeFilters');">{{labels.get('filters_confirm').replace('%VALUE%', totalProducts)}}</div>
			</div>
		</BaseCatalogActiveFilters>
	</div>
</template>

<script setup>
	const props = defineProps(['totalProducts', 'searchFields', 'contentType']);
	const emit = defineEmits(['closeFilters']);
	const labels = useLabels();
</script>

<style lang="less" scoped>
	.cf{
		@media (max-width: @m){padding-bottom: 66px;}
	}
	.cf-title{
		padding: 19px 23px; font-weight: bold; font-size: 16px; border: 1px solid var(--colorBorderLightForms);
		@media (max-width: @m){
			font-size: 15px; background: var(--colorBaseBlue); border: 0; padding: 17px 58px 16px 21px; color: #fff; position: relative;
			&:before, &:after{ .pseudo(18px,1px); top: 26px; right: 21px; background: var(--colorIconLightBlue); .rotate(45deg);}
			&:after{.rotate(-45deg);}
		}
		span{
			@media (max-width: @m){
				position: relative; padding-left: 37px;
				&:before{position: absolute; .icon-filters(); font: 11px/11px var(--fonti); color: var(--colorIconLightBlue); left: 0px; top: 5px;}
			}
		}
	}
	input[type=checkbox] + label{
		display: flex; align-items: center; padding: 2px 0 10px 34px; line-height: 1.4;
		@media (max-width: @t){font-size: 13px;}
		@media (max-width: @m){font-size: 15px;}
		&:before{
			@media (max-width: @m){width: 22px; height: 22px; line-height: 23px;}
		}
	}
	input[type=checkbox]:checked + label{
		color: #000;
		.cf-color-img span{border: 1px solid var(--colorBase);}
		.cf-color-img+span{color: var(--colorBaseBlue);}
	}
	.cf-color-img{
		position: absolute; left: -3px; top: 0; width: 30px; height: 30px; background: #fff; display: flex; align-items: center; justify-content: center;
		span{display: block; border-radius: 100%; padding: 3px;}
		:deep(img){display: block; border-radius: 100px;}
	}
	.cf-items-counter{position: absolute; right: 0; top: 5px; font-size: 12px; line-height: 1.2; color: var(--colorTextLightGray); font-weight: normal;}
	.cf-item{
		border: 1px solid var(--colorBorderLightForms); border-top: 0;
		@media (max-width: @m){border: 0; border-bottom: 1px solid var(--colorBorderLightForms);}
		&.active{
			.cf-item-toggle:after{display: none;}
			.cf-item-wrapper{display: block;}
		}
	}
	.cf-item-a_boja{
		input[type=checkbox] + label{
			padding-top: 5px;
			&:before{top: 4px;}
		}
	}
	.cf-item-wrapper{
		display: none; max-height: 352px; overflow: auto; padding: 0 13px 0 23px; margin: -4px 8px 0 0;
		@media (max-width: @m){max-height: 377px; padding: 0 9px 0 16px; margin-right: 6px;}
		.cf-row:last-child{margin-bottom: 12px;}
		&::-webkit-scrollbar {-webkit-appearance: none; width: 3px; background: var(--colorBorderLightForms); border-radius: 4px;}
		&::-webkit-scrollbar-thumb {background-color: var(--colorBaseBlue); border-radius: 4px;}
	}
	.cf-row.not-available label{
		pointer-events: none; color: #ccc;
		.cf-items-counter{display: none;}
	}
	.cf-item-title-cnt{display: flex; align-items: center; cursor: pointer;}
	.cf-item-title{
		display: block; font-weight: bold; font-size: 14px; line-height: 1.3; padding: 20px 0 18px 23px; position: relative;
		@media (max-width: @m){font-size: 15px; padding-left: 16px;}
	}
	.cf-filters-counter{
		width: 22px; height: 22px; flex: 0 0 22px; border-radius: 100%; background: var(--colorBaseBlue); color: #fff; font-size: 11px; font-weight: bold; display: flex; line-height: 22px; justify-content: center; margin: 0 0 0 9px;
		@media (max-width: @m){width: 20px; height: 20px; line-height: 20px; flex: 0 0 20px; margin: 0 0 0 8px;}
	}
	.cf-item-toggle{
		display: block; position: relative; min-width: 40px; flex: 1 1 10px; align-self: stretch;
		&:before{
			.pseudo(10px,2px); top: 50%; margin-top: -1px; right: 21px; background: var(--colorBaseBlue);
			@media (max-width: @m){right: 16px;}
		}
		&:after{
			.pseudo(2px,10px); top: 50%; margin-top: -5px; right: 25px; background: var(--colorBaseBlue);
			@media (max-width: @m){right: 20px;}
		}
	}
	.cf-search-cnt{
		margin: 0 0 12px; position: relative;
		&:before{position: absolute; .icon-search(); font: 16px/16px var(--fonti); color: var(--colorBaseBlue); right: 16px; top: 50%; margin-top: -8px;}
	}
	.cf-search-input{
		height: 40px; padding: 0 38px 0 15px; font-size: 14px; position: relative; z-index: 2; background: 0;
		@media (max-width: @m){height: 56px;}
	}
	.cf-filters-remove{
		font-size: 13px; font-weight: normal; margin: 1px 0 0 16px; padding: 0 0 0 20px; position: relative; display: none;
		&:before{position: absolute; .icon-clear(); font: 14px/14px var(--fonti); color: var(--colorBaseBlue); left: 0; top: 2px;}
		@media (max-width: @m){display: block;}
	}
	.cf-layout-sf{
		.cf-item-wrapper{max-height: none; overflow: visible;}
	}
	:deep(.cf-range) {display: flex; flex-flow: column; position: relative; width: 100%; height: auto; margin-bottom: 18px;}
	:deep(.cf-range-slider) {
		margin: 8px 8px 0; order: 1;
		--slider-height: 4px;
		--slider-radius: 0;
		--slider-bg: var(--colorBorderLightForms);
		--slider-connect-bg: var(--colorBaseBlue);
		--slider-handle-height: 20px;
		--slider-handle-width: 20px;
		--slider-handle-bg: #fff;
		.slider-handle{box-shadow: 0 0 8px 0 rgba(0,0,0,0.19);}
	}
	:deep(.cf-range-input) {
		display: flex; align-items: center; justify-content: space-between; order: 2; margin: 24px 0 10px;
		input {
			flex-grow: 1; width: 50%; height: 48px; font-size: 14px; text-align: center; padding: 0 12px;
			@media (max-width: @t){height: 40px; font-size: 13px; padding: 0 4px;}
			@media (max-width: @m){height: 48px; font-size: 15px;}
		}
	}
	:deep(.cf-range-separator) {
		flex: 0 0 10px; height: 2px; margin: 0 7px; background: var(--colorBaseBlue); font-size: 0; line-height: 48px;
		@media (max-width: @t){margin: 0 4px;}
		@media (max-width: @m){margin: 0 20px; flex-basis: 16px;}
	}
	:deep(.cf-range-button) {width: 100%; height: 44px; font-size: 14px; order: 3; box-shadow: none;}
	.cf-btns{
		display: none;
		@media (max-width: @m){display: flex; background: #fff; box-shadow: 0 -3px 10px 0 rgba(0,12,26,0.13); padding: 10px 16px; position: fixed; bottom: 0; left: 0; right: 0; z-index: 10;}
	}
	.cf-btn-clear{
		flex: 1 1 102px; margin-right: 10px; border-color: var(--colorRed); color: var(--colorBase); padding: 12px; text-align: center; pointer-events: none; opacity: 0.2;
		&.active-filters{pointer-events: all; opacity: 1;}
	}
	.cf-btn-confirm{flex: 1 1 215px; padding: 12px; text-align: center; box-shadow: none;}


	/* Manufacturers/lists */
	.cf-item-categories {
		.cf-row-level2{padding-left: 10px;}
		.cf-row-level3{padding-left: 20px;}
		.cf-row-level4{padding-left: 30px;}
	}
	.cf-manufacturer,.cf-list,.cf-search{
		.cf-item-categories{
			.cf-row-level1{
				input[type=checkbox] + label{padding-left: 0; font-weight: bold; color: var(--colorBaseBlue);cursor: default;}
				input[type=checkbox] + label:before,.cf-items-counter{display: none;}
			}
			.cf-row-level2{
				padding-left: 0;
				input[type=checkbox] + label{font-weight: bold;}
			}
			.cf-row-level3,.cf-row-level4{
				padding-left: 16px;
				input[type=checkbox] + label, input[type=radio] + label{
					font-size: 12px; line-height: 1.6;
					@media (max-width: @m){font-size: 13px;}
				}
			}
			.cf-row-level4{padding-left: 32px;}
		}
	}
</style>
