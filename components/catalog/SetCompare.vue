<template>
	<ClientOnly>
		<div class="cp-compare-container">
			<BaseCatalogSetCompare :item="item" v-slot="{onToggleCompare, active, message, loading, compareUrl}">
				<div class="cp-compare cp-btn compare" :class="{'loading': loading, 'compare_active': active && !loading}" @click="onToggleCompare">
					<span class="loader" v-show="loading" />
				</div>
				<span :class="['cp-compare-info', 'compare_message', message]" v-show="message">
					<span v-if="message == 'compare_error_limit'" v-html="labels.get('compare_error_limit').replace('%view_compare_url%', compareUrl)" />
					<template v-else>
						<span v-if="active" v-html="labels.get('compare_ok_added').replace('%view_compare_url%', compareUrl)" />
						<span v-if="!active" v-html="labels.get('compare_ok_removed').replace('%view_compare_url%', compareUrl)" />
					</template>
				</span>
			</BaseCatalogSetCompare>
		</div>
	</ClientOnly>
</template>

<script setup>
	const labels = useLabels();
	const props = defineProps(['item']);
</script>

<style lang="less" scoped>
	.cp-compare-container{
		position: absolute; top: 16px; right: 24px; z-index: 1;
		@media (max-width: @t){top: 12px; right: 16px;}
	}
	.cp-compare {
		display: flex; cursor: pointer; align-items: center; justify-content: center; flex-shrink: 0; width: 40px; height: 40px; font-size: 0; line-height: 0; border-radius: 200px; background: var(--colorLightBlueBackground); text-decoration: none; .transition(background);
		&:after {.icon-compare(); font: 18px/1 var(--fonti); color: #000; .transition(color);}
		&.compare_active {
			background: var(--colorBaseBlue);
			&:after { color: #fff;}
		}
		&.loading{
			pointer-events: none;
			&:after{opacity: 0;}
		}
		@media (min-width: @h){
			&:hover{
				background: var(--colorBaseBlue);
				&:after{color: var(--colorWhite);}
				&.compare_active {
					background: #004ba4;
				}
			}
		}
		@media (max-width: @t){
			width: 32px; height: 32px;
			&:after {font-size: 17px;}
		}
		@media (max-width: @m){width: 31px; height: 31px;}
	}
	.loader{position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: url(/assets/images/loader.svg) no-repeat center; background-size: 60% auto; z-index: 1;}
	.cp-compare-info{
		background: var(--colorWhite); white-space: nowrap; border-radius: var(--borderRadius); box-shadow: 0 2px 7px rgba(0, 0, 0, 0.2); font-size: 12px; line-height: 1.2; position: absolute; top: calc(~'100% - -10px'); right: -10px; padding: 8px 12px; z-index: 11;
		&:after{.pseudo(8px,8px); background: var(--colorWhite); .rotate(45deg); top: -4px; right: 26px; }
		:deep(a){color: var(--textColor); text-decoration-color: var(--colorBaseBlue); text-underline-offset: 3px;}
		@media (max-width: @m){
			font-size: 10px; padding: 8px; top: calc(~'100% - -7px'); right: 0; width: 150px; text-align: center;
			&:after{top: -3px; right: 12px; width: 6px; height: 6px;}
		}
	}
	.compare_error_limit{
		width: 230px; white-space: normal;
		@media (max-width: @m){width: 150px; text-align: left; right: -4px;}
	}
</style>
