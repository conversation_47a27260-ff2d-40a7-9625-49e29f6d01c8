<template>
	<div class="manufacturers-widget">
		<div class="wrapper manufacturers-widget-wrapper">
			<slot name="header" />
			<div class="manufacturers-widget-items">
				<BaseCmsLabel code="nav_brends_showall" class="manufacturers-widget-item bold show_all" tag="div" v-interpolation v-show="mode == 'nav-brands'" />
				<div class="manufacturers-widget-item" v-for="item in items" :key="item.id">
					<NuxtLink :to="item.url_without_domain" v-show="mode == 'nav-brands'">
						<span class="manufacturers-widget-item-title">{{item.title}}</span>
					</NuxtLink>
					<NuxtLink :to="item.url_without_domain" :class="{'nav-brands': mode == 'nav-brands'}">
						<figure class="manufacturers-widget-item-image" v-if="item.main_image_upload_path">
							<BaseUiImage :src="item.main_image_upload_path" default="/images/no-image-112.png" width="100" height="100" loading="lazy" />
						</figure>
						<span class="manufacturers-widget-item-title" v-else>{{item.title}}</span>
					</NuxtLink>
				</div>
			</div>
			<slot name="footer" />
		</div>
	</div>
</template>

<script setup>
	const props = defineProps(['items', 'mode']);
</script>

<style lang="less" scoped>
	// manufacturers widget
	.manufacturers-widget{
		position: relative; display: block; background: var(--colorLightBlueBackground); padding: 80px 0;
		@media (max-width: @t){padding: 64px 0;}
		@media (max-width: @m){padding: 30px 0;}
	}
	:deep(.manufacturers-widget-header){position: relative; display: block; margin: 0 0 17px;}
	:deep(.manufacturers-widget-title){
		display: block; font-size: var(--fontSizeH2); line-height: 1.2; font-weight: bold; margin-bottom: 8px;
		@media (max-width: @t){font-size: var(--fontSizeH1); margin-bottom: 3px;}
		@media (max-width: @m){font-size: 19px; margin-bottom: 3px;}
	}
	:deep(.manufacturers-widget-subtitle){
		p{padding-bottom: 0;}
		@media (max-width: @t){font-size: var(--fontSizeSmall);}
	}
	.manufacturers-widget-items{
		position: relative; display: flex; flex-wrap: wrap; width: 100%;
		@media (max-width: @m){
			flex-wrap: inherit; width: calc(~"100% - -32px"); margin-left: -16px; padding: 0 16px; overflow-y: auto;
			&::-webkit-scrollbar{-webkit-appearance: none; height: 0; background: transparent; z-index: 0;}
			&::-webkit-scrollbar-thumb{background-color: transparent;}
		}
	}
	.manufacturers-widget-item{
		position: relative; display: flex; width: calc(~"12.5% - -1px"); height: 104px; margin: -1px 0 0 -1px;
		&:first-child{
			a{border-radius: var(--inputBorderRadius) 0 0 0;}
		}
		&:nth-child(8){
			a{border-radius: 0 var(--inputBorderRadius) 0 0;}
		}
		&:nth-child(9){
			a{border-radius: 0 0 var(--inputBorderRadius) 0;}
		}
		&:last-child{
			a{border-radius: 0 0 0 var(--inputBorderRadius);}
		}
		a{
			position: relative; display: flex; align-items: center; justify-content: center; width: 100%; height: 100%; border: 1px solid var(--colorLightBlueBackground); background: var(--colorWhite); text-decoration: none; color: var(--colorBase); text-transform: uppercase; font-weight: bold; padding: 15px; .transition(box-shadow);
			@media (min-width: @h){
				&:hover{z-index: 1; box-shadow: 0 8px 20px rgba(0,89,194,0.1);}
			}
		}
		:deep(img){display: block;}
		@media (max-width: @t){
			height: 75px;
			a{padding: 27px;}
		}
		@media (max-width: @m){
			height: 96px; width: calc(~"50% - 9px"); flex-grow: 0; flex-shrink: 0; margin-left: 0;
			&:nth-child(8){
				a{border-radius: 0;}
			}
			&:nth-child(9){
				a{border-radius: 0;}
			}
			&:last-child{
				a{border-right: none; border-radius: 0 var(--inputBorderRadius) var(--inputBorderRadius) 0;}
			}
			a{padding: 27px; border: none; border-right: 1px solid var(--colorLightBlueBackground);}
		}
	}
	:deep(.manufacturers-widget-items-btn){
		position: relative; display: block; margin-top: 25px;
		p{padding-bottom: 0;}
		a{box-shadow: 0 10px 16px rgba(0,89,194,0.2);}
		@media (max-width: @t){
			margin-top: 16px;
			a{font-size: var(--fontSizeSmall);}
		}
		@media (max-width: @m){
			margin-top: 24px;
			a{font-size: 15px;}
		}
	}

	// manufacturers index
	.manufactures-index{
		padding: 0; margin-top: 30px;
		.manufacturers-widget-header, .manufacturers-widget-items-btn{display: none;}
		@media (max-width: @m){
			margin-top: 0;
			.wrapper{padding: 0;}
			.manufacturers-widget-items{flex-wrap: wrap; border-bottom: 1px solid var(--colorBorderLightForms); width: 100%; margin: 0; padding: 0;}
			.manufacturers-widget-item{
				width: 50%; border-right: 1px solid var(--colorBorderLightForms); border-top: 1px solid var(--colorBorderLightForms); border-left: 0;
				//&:nth-child(2n){border-right: 0;}
				a{border: none;}
			}
		}
	}

	.nav-submenu-col-brands{
		@media (min-width: @m){
			background: white;
			.wrapper{padding: 0;}
			:deep(.manufacturers-widget-header){margin: 0;}
			:deep(.manufacturers-widget-title){font-size: 16px; line-height: 1.5; margin-bottom: 5px;}
			:deep(.manufacturers-widget-subtitle){display: none;}
			:deep(.manufacturers-widget-items-btn){display: none;}
			.manufacturers-widget-items{flex-wrap: nowrap; flex-flow: column;}
			:deep(.manufacturers-widget-item){
				width: 100%; height: auto; margin: 4px 0;
				a{border: none; border-radius: 0!important; display: block; font-weight: normal; padding: 0;}
				.nav-brands{display: none;}
				&.show_all {
					p{padding-bottom: 0;}
					a{
						color: var(--colorBaseBlue); display: flex; align-content: center; text-decoration: none;
						&:after{
							.icon-arrow3(); font: 20px/1 var(--fonti); color: var(--colorBaseBlue); margin-left: 6px;
							@media (max-width: @t){font-size: 16px; margin-left: 5px;}
						}
					}
				}
				&.bold a{ font-weight: bold;}
			}
		}
		@media (min-width: @h){
			.manufacturers-widget-item{
				a:hover{z-index: 1; box-shadow: none; color: var(--colorBaseBlue);}
			}
		}
		@media (max-width: @t){
			:deep(.manufacturers-widget-title){font-size: 12px; line-height: 1.5;}
			.manufacturers-widget-items{padding: 6px 0;}
		}
	}
</style>
