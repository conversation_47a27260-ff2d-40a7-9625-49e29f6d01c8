<template>
	<div class="landing-category-main">
		<h1 style="display: none" v-if="category?.title">{{ category.title }}</h1>

		<BaseCmsRotator :fetch="{code: 'c_lvl_one_intro', limit: 4, catalogcategory_id: category.id, response_fields: ['id','link','url_without_domain','title','title2','content','image_upload_path','image_2_upload_path','image_thumbs','image_2_thumbs','catalogcategory_ids']}" v-slot="{items}">
			<CmsIntroSlider v-if="items?.length" :items="items" />
		</BaseCmsRotator>

		<CmsBenefits extclass="special" />

		<ClientOnly>
			<BaseCmsNav code="main_categories" :start-position="category.position_h" v-slot="{ items }">
				<CmsHomepageCategories :items="items" v-if="items?.length">
					<template #header>
						<BaseCmsLabel code="view_categories" tag="div" class="homepage-categories-title" />
					</template>
				</CmsHomepageCategories>
			</BaseCmsNav>
		</ClientOnly>

		<BaseCmsRotator v-if="!mobileBreakpoint" :fetch="{code: 'popular_categories', limit: 7, catalogcategory_id: category.id, response_fields: ['id','element_class','link','url_without_domain','image_upload_path','image_thumbs','title','catalogcategory_ids']}" v-slot="{items}">
			<CmsCategories v-if="items?.length" :items="items" />
		</BaseCmsRotator>

		<BaseCmsRotator
			:fetch="{code: 'c_lvl_one_promo_first', limit: 3, catalogcategory_id: category.id, response_fields: ['id','template','link','url_without_domain','title2','title','content','image_upload_path','image_2_upload_path','image_thumbs','image_2_thumbs','catalogcategory_ids']}"
			v-slot="{items}">
			<CmsPromo class="homepage-promo-widget homepage-promo-widget1" :items="items" v-if="items?.length" />
		</BaseCmsRotator>

		<BaseCatalogLists :fetch="{code: ['new_'+category.code], limit: 1, response_fields: ['code','title','content']}" v-slot="{items: list}">
			<BaseCatalogProductsWidget :fetch="{list_code: list[0].code, sort: 'list_position', only_available: true, limit: 15, category_code: category.code, only_with_image: true}" v-slot="{items}" v-if="list?.length">
				<CatalogSpeciallists class="homepage-speciallist homepage-speciallist1" :items="items" list="new" v-if="items?.length">
					<template #header>
						<div class="speciallist-header">
							<div v-if="list[0].title" class="speciallist-title">{{ list[0].title }}</div>
							<div v-if="list[0].content" class="speciallist-cnt" v-html="list[0].content" v-interpolation></div>
						</div>
					</template>
				</CatalogSpeciallists>
			</BaseCatalogProductsWidget>
		</BaseCatalogLists>

		<BaseCmsRotator
			:fetch="{code: 'c_lvl_one_promo_second', limit: 2, catalogcategory_id: category.id, response_fields: ['id','template','link','url_without_domain','title2','title','content','image_upload_path','image_2_upload_path','image_thumbs','image_2_thumbs','catalogcategory_ids']}"
			v-slot="{items}">
			<CmsPromo class="homepage-promo-widget homepage-promo-widget2" :items="items" v-if="items?.length" />
		</BaseCmsRotator>

		<BaseCatalogLists :fetch="{code: ['sale_'+category.code], limit: 1, response_fields: ['code','title','content']}" v-slot="{items: list}">
			<BaseCatalogProductsWidget :fetch="{list_code: list[0].code, sort: 'list_position', only_available: true, limit: 15, category_code: category.code, only_with_image: true}" v-slot="{items}" v-if="list?.length">
				<CatalogSpeciallists class="homepage-speciallist homepage-speciallist2" :items="items" v-if="items?.length" list="sale">
					<template #header>
						<div class="speciallist-header">
							<div v-if="list[0].title" class="speciallist-title">{{ list[0].title }}</div>
							<div v-if="list[0].content" class="speciallist-cnt" v-html="list[0].content" v-interpolation></div>
						</div>
					</template>
				</CatalogSpeciallists>
			</BaseCatalogProductsWidget>
		</BaseCatalogLists>

		<BaseCmsRotator
			:fetch="{code: 'c_lvl_one_promo_third', limit: 1, catalogcategory_id: category.id, response_fields: ['id','template','link','url_without_domain','title2','title','content','image_upload_path','image_2_upload_path','image_thumbs','image_2_thumbs','catalogcategory_ids']}"
			v-slot="{items}">
			<CmsPromo class="homepage-promo-widget homepage-promo-widget3" :items="items" v-if="items?.length" />
		</BaseCmsRotator>

		<BaseCatalogLists :fetch="{code: ['bestsellers_'+category.code], limit: 1}" v-slot="{items: list}">
			<BaseCatalogProductsWidget :fetch="{list_code: list[0].code, sort: 'list_position', only_available: true, limit: 15, only_with_image: true}" v-slot="{items}" v-if="list?.length">
				<CatalogSpeciallists class="homepage-speciallist homepage-speciallist3" :items="items" v-if="items?.length" list="bestsellers">
					<template #header>
						<div class="speciallist-header">
							<div v-if="list[0].title" class="speciallist-title">{{ list[0].title }}</div>
							<div v-if="list[0].content" class="speciallist-cnt" v-html="list[0].content" v-interpolation></div>
						</div>
					</template>
				</CatalogSpeciallists>
			</BaseCatalogProductsWidget>
		</BaseCatalogLists>

		<BaseCmsRotator :fetch="{code: 'service_widget', limit: 1, catalogcategory_id: category.id, response_fields: ['id','link','image_upload_path','image_2_upload_path','image_thumbs','image_2_thumbs','title','title2','content','catalogcategory_ids']}" v-slot="{items}">
			<CmsService :items="items" v-if="items?.length" />
		</BaseCmsRotator>

		<BaseCatalogManufacturers :fetch="{id:category.related_manufacturers_ids, special: 1, limit: 16, sort: 'position_h', response_fields: ['id','url_without_domain','title','main_image_upload_path']}" v-slot="{items}">
			<CatalogManufacturers class="homepage-manufacturers-widget" :items="items" v-if="items?.length && category.related_manufacturers_ids">
				<template #header>
					<div class="manufacturers-widget-header">
						<BaseCmsLabel code="manufacturers_widget_title" class="manufacturers-widget-title" tag="div" v-interpolation />
						<BaseCmsLabel code="manufacturers_widget_subtitle" class="manufacturers-widget-subtitle" tag="div" v-interpolation />
					</div>
				</template>
				<template #footer>
					<div class="manufacturers-widget-items-btn">
						<NuxtLink :href="getAppUrl('manufacturer')" class="btn"><BaseCmsLabel code="manufacturers_widget_button" /></NuxtLink>
					</div>
				</template>
			</CatalogManufacturers>
		</BaseCatalogManufacturers>
	</div>
</template>

<script setup>
	const {getAppUrl} = useApiRoutes();
	const {onMediaQuery} = useDom();
	const props = defineProps({
		category: {
			type: Object,
			required: true,
		},
	});

	const {matches: mobileBreakpoint} = onMediaQuery({
		query: '(max-width: 980px)',
	});
</script>
