<template>
	<div class="cd-warehouse" :class="{'cd-warehouse-disabled': disabled}" v-if="warehouses">
		<div class="cd-warehouse-title">
			<span><BaseCmsLabel :code="disabled ? 'availability_select' : 'availability'" /></span>
		</div>
		<ul class="cd-warehouse-list">
			<li v-for="item in warehouses" :key="item.id" @mouseover="toggleItem(item, 'hover')" @mouseout="toggleItem(item, 'hover')">
				<div class="cd-warehouse-item">
					<span class="cd-warehouse-item-title" @click="toggleItem(item, 'click')">
						<span class="icon-availability" :class="{'unavailable': +item.available_qty <= 0 || disabled}" />
						{{item.title}} <template v-if="user?.staff || user?.superuser || user?.developer">({{item.available_qty}})</template>
					</span>
					<div class="cd-warehouse-tooltip" v-if="item.address" v-show="activeItem == item.id">
						<span class="close" @click="toggleItem(item, 'click')">x</span>
						<div class="cd-warehouse-tooltip-address" v-if="item.address" v-html="item.address" />
						<div v-if="item.contact" v-html="item.contact" />
						<div v-if="item.business_hour" v-html="item.business_hour" />
					</div>
				</div>
			</li>
		</ul>
	</div>
</template>

<script setup>
	const {user} = useAuth();
	const props = defineProps(['item', 'selectedVariation']);
	const {onMediaQuery} = useDom();
	const {matches: mobileBreakpoint} = onMediaQuery({
		query: '(max-width: 980px)',
	});
	const activeItem = ref();

	const warehouses = computed(() => {
		let warehouses = props.item.warehouses;

		if(+props.item.variation_request) {
			if(props.selectedVariation && props.selectedVariation.warehouses) warehouses = props.selectedVariation.warehouses;
			if(!props.selectedVariation && props.item.variations) {
				const variations = Object.values(props.item.variations);
				if(variations?.length) warehouses = variations[0].warehouses;
			}
		}

		return warehouses;
	});

	const disabled = computed(() => {
		if(+props.item.variation_request && !props.selectedVariation) return true;
		return false;
	})

	function toggleItem(item, event) {
		if(event == 'hover' && mobileBreakpoint.value) return;
		if(event == 'click' && !mobileBreakpoint.value) return;
		activeItem.value = (activeItem.value == item.id) ? null : item.id;
	}
</script>

<style scoped lang="less">
	.cd-warehouse{
		background: #fff; padding: 22px 25px; margin-top: 30px; border-radius: var(--borderRadius); font-size: 14px; line-height: 1.3; position: relative;
		@media (max-width: @m){border: 1px solid var(--colorBorderLightForms); margin-top: 10px; padding: 15px 20px;}
	}
	.cd-warehouse-disabled{
		&:before{.pseudo(auto, auto); top: 0; right: 0; bottom: 0; left: 0; background: #fff; z-index: 10; opacity: 0.6;}
	}
	.cd-warehouse-title{
		font-size: 14px; font-weight: bold; position: relative; display: flex; align-items: center; padding-bottom: 10px;
		&:before{.icon-store; width: 25px; font: 25px/1 var(--fonti); margin-right: 10px; color: var(--colorBaseBlue);}
	}
	.cd-warehouse-list{
		list-style: none; padding: 0 0 0 35px; margin: 0; display: flex; flex-direction: column;
		&>li{
			align-self: flex-start;
			&:hover{align-self: auto;}
		}
	}
	.cd-warehouse-item{
		display: inline-block; margin-bottom: 8px; position: relative;
		@media (max-width: @m){display: block;}
		&:hover .cd-warehouse-item-title{color: var(--colorBaseBlue);}
	}
	.cd-warehouse-item-title{text-decoration: underline; text-decoration-color: var(--colorBaseBlue); text-decoration-thickness: 1px; text-underline-offset: 3px; display: inline-flex; align-items: center; cursor: default; .transition(color);}
	.icon-availability{display: block; width: 10px; height: 10px; margin-right: 8px; background: var(--colorGreen); border-radius: 100px;}
	.unavailable{background: var(--colorRed);}
	.cd-warehouse-tooltip{
		position: absolute; top: -17px; left: 110%; background: #fff; z-index: 20; box-shadow: 0 0 25px 0 rgba(0,0,0,0.14); border-radius: var(--borderRadius); width: 305px; padding: 20px 25px 15px; font-size: 13px; line-height: 1.5;
		@media (max-width: @m){top: calc(100% + 10px); left: -56px; width: calc(100vw - 45px);}
		&:before{
			.pseudo(8px,8px); background: #fff; .rotate(45deg); top: 24px; left: -4px;
			@media (max-width: @m){left: 120px; top: -4px}
		}
		:deep(strong){font-weight: normal;}
		:deep(p){padding: 0 0 10px;}
		:deep(a){color: var(--colorBase); text-decoration: underline; text-decoration-color: var(--colorBaseBlue); text-underline-offset: 3px; .transition(color);}
		:deep(a:hover){color: var(--colorBaseBlue);}
	}
	.cd-warehouse-tooltip-address{
		font-weight: bold;
		:deep(strong){font-weight: bold;}
	}
	.close{
		position: absolute; top: 19px; right: 20px; font-size: 0; width: 20px; height: 20px; align-items: center; justify-content: center; cursor: pointer; display: none;
		@media (max-width: @m){display: flex;}
		&:before{.icon-close; font: 12px/1 var(--fonti); color: var(--colorRed);}
	}
</style>
