<template>
	<div class="ci-categories" v-if="props.subcategories?.length">
		<div class="ci-categories-list" :class="{'active': categoryOpened}">
			<div class="ci-categories-list-title" @click="categoryOpened = !categoryOpened">{{ category.title }}</div>
			<ul class="ci-categories-list-items">
				<template v-if="props.subcategories?.length">
					<li v-for="item in subcategories" :key="item.id" :class="['ci-categories-list-lvl', 'ci-categories-list-lvl' + item.level]">
						<BaseUiLink :href="item.url_without_domain">{{ item.title }}</BaseUiLink>
					</li>
				</template>
				<li v-if="category.total_discount > 0">
					<BaseUiLink :href="category.url_without_domain+'?discount=1'" class="sale"><BaseCmsLabel code="sale_title" /></BaseUiLink>
				</li>
				<li v-if="category.total_new > 0">
					<BaseUiLink :href="category.url_without_domain+'?new=1'" class="new"><BaseCmsLabel code="new_title" /></BaseUiLink>
				</li>
			</ul>
		</div>
	</div>
</template>

<script setup>
	const props = defineProps(['category', 'subcategories']);
	let categoryOpened = ref(true);
</script>

<style lang="less">
	.page-catalog-lvl1 {
		.ci-categories-list-items {
			.ci-categories-list-lvl2{font-weight: bold;}
			.ci-categories-list-lvl3{padding-left: 15px;}
		}
	}
</style>
<style lang="less" scoped>
	.ci-categories{
		border: 1px solid var(--colorBorderLightForms); margin-bottom: -1px; border-radius: 4px 4px 0 0;
		@media (max-width: @m){display: none;}
	}
	.ci-categories-list.active{
		.ci-categories-list-title{
			padding-bottom: 10px;
			&:after{display: none;}
		}
		.ci-categories-list-items{display: block;}
	}
	.ci-categories-list-title{
		font-size: 16px; font-weight: bold; line-height: 1.4; padding: 20px 38px 17px 23px; position: relative; cursor: pointer;
		&:before{.pseudo(10px,2px); top: 30px; right: 23px; background: var(--colorBaseBlue);}
		&:after{.pseudo(2px,10px); top: 26px; right: 27px; background: var(--colorBaseBlue);}
	}
	.ci-categories-list-items{
		list-style: none; font-size: 14px; line-height: 1.3; display: none; padding: 0 24px 17px;
		@media (max-width: @t){font-size: 13px;}
		a{
			text-decoration: none; display: inline-block; vertical-align: top; padding: 2px 0 3px; color: var(--colorBase); .transition(color);
			@media (min-width: @h){
				&:hover:not(.sale,.new){color: var(--colorBaseBlue);}
			}
		}
		.router-link-active{color: var(--colorBaseBlue);}
		.sale{color: var(--colorRed); font-weight: bold;}
		.new{color: var(--colorBase); font-weight: bold;}
	}
</style>
