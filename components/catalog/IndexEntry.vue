<template>
	<article class="cp" :class="[{'cp-unavailable': !item.is_available && !item.variation_available}, mode]">
		<div class="cp-top">
			<ClientOnly>
				<div class="cp-badge-wrapper">
					<div v-if="item?.priority_details?.code == 'new'" class="cp-badge cp-badge-new">
						<BaseCmsLabel code="new" tag="span" />
					</div>
					<div v-if="item.variation_request === '1' && item.variation_discount_percent_max > 0" class="cp-badge cp-badge-discount">
						<span> -{{ item.variation_discount_percent_max }}% </span>
					</div>
					<div v-else-if="item.variation_request !== '1' && item.discount_percent_custom > 0" class="cp-badge cp-badge-discount">
						<span> -{{ item.discount_percent_custom }}% </span>
					</div>
				</div>
			</ClientOnly>

			<CatalogSetCompare :item="item" />

			<figure class="cp-image">
				<NuxtLink :to="item.url_without_domain" @click="gtmTrack('selectItem', {items: item})">
					<BaseUiImage loading="lazy" :data="item.main_image_thumbs?.['width400-height400']" default="/images/no-image-210.jpg" width="400" height="400" :title="item.main_image_title" :alt="item.main_image_description ? item.main_image_description : item.title" />
				</NuxtLink>
			</figure>

			<ClientOnly>
				<BaseCmsLabel class="cp-badge cp-badge-unavailable" v-if="!item.is_available && !item.variation_available" code="product_not_available" tag="div" />
				<div class="cp-badge cp-badge-special" v-else-if="item.attributes_special && item.attributes_special[0].attribute_code == 'posebne_oznake'">{{item.attributes_special[0].title}}</div>

				<div class="cp-sizes-cnt" v-if="options !== 'withAddToCart' && sizeAttributes?.length">
					<div class="cp-sizes-cnt-body">
						<div class="cp-sizes-title"><BaseCmsLabel code="available_sizes" /></div>
						<div class="cp-sizes">
							<NuxtLink v-for="attr in sizeAttributes" :key="attr.id" :href="`${item.url_without_domain}#variationattribute${attr.id}`" class="cp-size" @click="gtmTrack('selectItem', {items: item})">{{attr.title}}</NuxtLink>
						</div>
					</div>
				</div>
			</ClientOnly>
		</div>
		<div class="cp-cnt-container">
			<div class="cp-cnt">
				<div class="cp-manufacturer">
					<NuxtLink v-if="item.manufacturer_code" :to="item.manufacturer_url_without_domain">{{item.manufacturer_title}}</NuxtLink>
				</div>
				<div class="cp-title">
					<NuxtLink :to="item.url_without_domain" @click="gtmTrack('selectItem', {items: item})">{{item.title}}</NuxtLink>
				</div>
			</div>
			<ClientOnly>
				<div class="cp-price" v-if="item.price_custom > 0">
					<template v-if="item.variation_request === '1' && item.variation_price_same !== '1' && item.variation_price_min > 0">
						<BaseCmsLabel class="cp-price-label" code="price_from" tag="div" />
						<div class="cp-current-price"><BaseUtilsFormatCurrency :price="item.variation_price_min" /></div>
					</template>
					<template v-else>
						<template v-if="(item.discount_percent_custom > 0 || item.price_custom < item.basic_price_custom)">
							<div class="cp-old-price"><BaseUtilsFormatCurrency :price="item.basic_price_custom" /></div>
							<div class="cp-current-price cp-discount-price"><BaseUtilsFormatCurrency :price="item.price_custom" /></div>
						</template>
						<template v-else>
							<BaseCmsLabel class="cp-price-label" code="price" tag="div" />
							<div class="cp-current-price"><BaseUtilsFormatCurrency :price="item.price_custom" /></div>
						</template>
					</template>
				</div>
				<div class="cp-add-to-cart-cnt" v-if="options === 'withAddToCart' && item.status == 1">
					<div v-if="item.variation_request === '1' && item.variation_quickdata && item.variation_attributes_all" class="cp-add-to-cart-var">
						<select @change="onSelect($event)" class="cp-add-to-cart-select">
							<option value="" disabled selected><BaseCmsLabel code="variation_select_text" /></option>
							<option v-for="variation in item.variation_quickdata" :key="variation.id" :value="variation.shopping_cart_code">{{attributeName(item.variation_attributes_all,variation.attributes_ids)}}</option>
						</select>
						<BaseWebshopAddToCart v-if="selectedVariation" v-slot="{onAddToCart, addInProgress}" :data="{modalData: item, shopping_cart_code: selectedVariation, quantity: 1}">
							<div class="btn btn-green cp-add-to-cart-btn" :class="addInProgress && 'disabled'" @click="onAddToCart"></div>
						</BaseWebshopAddToCart>
					</div>
					<BaseWebshopAddToCart v-else v-slot="{onAddToCart, addInProgress}" :data="{modalData: item, shopping_cart_code: item.shopping_cart_code, quantity: 1}">
						<div class="btn btn-green cp-add-to-cart-btn" :class="addInProgress && 'disabled'" @click="onAddToCart">
							<BaseCmsLabel code="add_to_shopping_cart" />
						</div>
					</BaseWebshopAddToCart>
				</div>
			</ClientOnly>
		</div>
	</article>

	<ClientOnly>
		<article v-if="promoItem" class="cp cp-promo">
			<NuxtLink :to="promoItem.url_without_domain">
				<BaseUiImage loading="lazy" :data="(mobileBreakpoint && promoItem.image2_thumbs?.['width600-height600-crop1']) ? promoItem.image2_thumbs['width600-height600-crop1'] : promoItem.image_thumbs?.['width260-height435-crop1']" default="/images/no-image-210.jpg" />
			</NuxtLink>
		</article>
	</ClientOnly>
</template>

<script setup>
	const {gtmTrack} = useGtm();
	const {onMediaQuery} = useDom();
	const props = defineProps({
		item: Object,
		mode: String,
		options: [Object,String],
		order: Number,
		promo: Array
	});

	// promo banners
	const {matches: mobileBreakpoint} = onMediaQuery({
		query: '(max-width: 980px)',
	});
	const promoItem = computed(() => {
		if(!props.promo?.length) return null;
		if(!mobileBreakpoint.value) {
			if(props.order == 7 && props.promo[0]) return props.promo[0];
			if(props.order == 11 && props.promo[1]) return props.promo[1];
			if(props.order == 21 && props.promo[2]) return props.promo[2];
		} else {
			if(props.order == 6 && props.promo[0]) return props.promo[0];
			if(props.order == 12 && props.promo[1]) return props.promo[1];
			if(props.order == 18 && props.promo[2]) return props.promo[2];
		}
	});

	// available size attributes
	const sizeAttributes = computed(() => {
		if (!props.item.variation_attributes) return [];
		return Object.values(props.item.variation_attributes).filter(item => {
			if (['velicina','dostupnavelicina'].includes(item.attribute_code)) {
				return item;
			}
		});
	});

	const selectedVariation = ref(null);

	function onSelect(e){
		selectedVariation.value = e.target.value ? e.target.value : null;
	}

	function attributeName(itemAttributes,varAttributeIds){
		const attArr = (varAttributeIds.slice(1, -1)).split(',');
		let title = '';
		attArr.forEach((att, index) => {
			title += itemAttributes[att].attribute_title + ': ' + itemAttributes[att].title + ((index+1 < attArr.length) ? ', ' : '');
		})

		return title;
	}
</script>

<style lang="less" scoped>
	.cp{
		position: relative; display: flex; flex-flow: column; border: 1px solid var(--colorBorderLightForms); background: var(--colorWhite); width: calc(~"25% - -1px"); height: auto; margin-left: -1px; margin-top: -1px; .transition(box-shadow);
		&:after{.pseudo(auto,20px); border-radius: 10%; right: 16px; left: 16px; bottom: 5px; z-index: -1; opacity: 0; box-shadow: 0 8px 18px 0 rgba(0, 12, 26, 0.12); .transition(opacity);}
		@media (min-width: @h){
			&:hover{
				z-index: 2;
				&:after{opacity: 1;}
				.cp-sizes-cnt{opacity: 1;}
			}
		}
		&:first-child{border-radius: var(--inputBorderRadius) 0 0 0;}
		&:nth-child(4){border-top-right-radius: var(--inputBorderRadius);}
		&:last-child{border-bottom-right-radius: var(--inputBorderRadius);}
		&:nth-last-child(3){border-bottom-left-radius: var(--inputBorderRadius);}
		&.cp-unavailable{
			.cp-image{opacity: 0.25;}
		}
		@media (max-width: @t){border-radius: 0!important;}
		@media (max-width: @m){width: calc(~"50% - -1px");}
	}
	.cp-top{
		position: relative; padding: 48px 24px 24px; border-radius: 4px 4px 0 0;
		@media (max-width: @t){padding: 40px 10px 25px;}
		@media (max-width: @m){padding-bottom: 24px;}
	}
	.cp-badge-wrapper{
		position: absolute; top: 24px; left: 24px; display: flex; flex-flow: column;
		@media (max-width: @t){top: 16px; left: 16px;}
	}
	.cp-badge{
		width: 47px; height: 24px; display: flex; align-items: center; justify-content: center; color: var(--colorWhite); background: var(--colorRed); border-radius: 3px; font-size: 12px; line-height: 1.1; font-weight: bold; position: relative; z-index: 1;
		@media (max-width: @m){height: 23px;}
	}
	.cp-badge-new{background: var(--colorBaseBlue); margin-bottom: 5px;}
	.cp-badge-special{
		width: auto; padding: 0 8px; background: var(--colorYellow); position: absolute; bottom: 17px; left: 24px; color: var(--colorBase);
		@media (max-width: @t){bottom: 14px; left: 16px;}
	}
	.cp-badge-unavailable{
		width: 100%; background: var(--colorWhite); color: var(--colorRed); box-shadow: 0 0 8px 0 rgba(0, 12, 26, 0.15); position: absolute; right: 24px; left: 24px; width: auto; bottom: 13px;
		@media (max-width: @t){text-align: center; height: auto; padding: 5px 0; left: 16px; right: 16px;}
		@media (max-width: @m){padding: 3px 0;}
	}
	.cp-image{
		width: 100%; display: flex; justify-content: center; align-items: center; position: relative;
		&>a{display: flex; justify-content: center; align-items: center; height: 209px;}
		:deep(img){width: auto; height: auto; max-width: 100%; max-height: 100%; display: block;}
		@media (max-width: @t){
			&>a{height: 128px;}
		}
		@media (max-width: @m){
			&>a{height: 160px;}
		}
	}
	.cp-sizes-cnt{
		position: absolute; bottom: 0; left: 0; right: 0; font-size: 11px; line-height: 1.1; font-weight: bold; color: var(--colorBase); opacity: 0; .transition(opacity); z-index: 1;
		&:after{.pseudo(auto,15px); content: ''; position: absolute; top: 0; left: 15px; right: 15px; box-shadow: 0 8px 18px 0 rgba(0, 0, 0, 0.5); z-index: -1;}
		@media (max-width: @t){display: none; opacity: 0;}
	}
	.cp-sizes-cnt-body{background: #fff; padding: 15px 23px 16px; position: relative; z-index: 1;}
	.cp-sizes{display: flex; flex-wrap: wrap; align-items: center; padding-top: 7px; position: relative;}
	.cp-size{
		display: flex; align-items: center; position: relative; z-index: 0; text-decoration: none; color: var(--colorBase); justify-content: center; padding: 0 8px; height: 28px; min-width: 28px; width: auto; font-weight: normal; font-size: 12px; line-height: 1.5; border: 1px solid var(--colorBorderLightForms); margin-left: -1px; margin-top: -1px;
		@media (min-width: @h){
			&:hover{border-color: var(--colorBaseBlue); color: var(--colorBaseBlue); z-index: 1;}
		}
	}

	//BOTTOM
	.cp-cnt-container{
		position: relative; display: flex; flex-flow: column; padding: 0 24px 22px; flex-grow: 1; flex-shrink: 1; background: var(--colorWhite); border-radius: 0 0 4px 4px;
		@media (max-width: @t){padding: 0 16px 16px;}
		@media (max-width: @m){padding-bottom: 15px;}
	}
	.cp-cnt{height: 100%;}
	.cp-manufacturer{
		font-size: 12px; line-height: 1.1; font-weight: bold; text-transform: uppercase; min-height: 14px;
		a{text-decoration: none;}
		@media (min-width: @h){
			a:hover{text-decoration: underline;}
		}
		@media (max-width: @t){font-size: 11px; line-height: 1.3;}
		@media (max-width: @m){font-size: 12px; line-height: 1.3; min-height: 15px;}
	}
	.cp-manufacturer + .cp-title{
		padding-top: 4px;
		@media (max-width: @t){padding-top: 5px;}
	}
	.cp-title{
		font-size: 14px; line-height: 1.3;
		a{color: var(--colorBase); text-decoration: none;}
		@media (max-width: @m){font-size: 12px; line-height: 1.5;}
		@media (max-width: @m){font-size: 13px; line-height: 1.3;}
	}
	.cp-price{
		flex-grow: 0; flex-shrink: 0; margin-top: 20px; font-size: 12px; line-height: 1.5; color: var(--colorBase);
		@media (max-width: @t){margin-top: 25px; line-height: 1.4;}
		@media (max-width: @m){margin-top: 16px;}
	}
	.cp-old-price{text-decoration: line-through;}
	.cp-current-price{
		font-weight: bold; font-size: 14px; line-height: 1.4;
		@media (max-width: @t){font-size: 13px; line-height: 1.3;}
	}
	.cp-discount-price{color: var(--colorRed);}

	.cp-add-to-cart-cnt{margin-top: 15px;}
	.cp-add-to-cart-var{
		display: flex;
		.cp-add-to-cart-btn{
			border-top-left-radius: 0; border-bottom-left-radius: 0; margin-left: -2px; flex: 0 0 48px; padding: 0;
			@media (max-width: @m){flex: 0 0 40px;}
			&:before{.icon-cart; font: 20px/20px var(--fonti); color: #fff;}
		}
	}
	.cp-add-to-cart-select{
		height: 48px; font-size: 14px; background-size: 10px; background-position: right 14px center; padding: 0 30px 0 16px;
		@media (max-width: @m){height: 40px; font-size: 12px; background-size: 8px; background-position: right 11px center; padding: 0 26px 0 11px;}
	}
	.cp-add-to-cart-btn{
		height: 48px; padding: 0 20px; display: flex; text-align: center; font-size: 14px; box-shadow: none;
		&.disabled{pointer-events: none;}
		@media (max-width: @m){height: 40px; font-size: 12px; padding: 0;}
	}

	//Speciallist
	.slider{
		@media (max-width: @t){
			.cp-title{font-size: 12px; line-height: 1.5;}
		}
		@media (max-width: @m){
			.cp-title{font-size: 13px; line-height: 1.3;}
			.cp-badge-unavailable{height: auto; text-align: center; padding: 5px 0; position: absolute; width: auto; left: 16px; right: 16px; bottom: 10px; font-size: 11px;}
		}
	}

	.cp.compare{
		width: 100%; border: 0; margin: 0; flex-grow: 1;
		&:after{display: none;}
	}
	.cp-promo{
		a{display: flex; height: 100%;}
		:deep(img){
			display: block; object-fit: cover;
			@media (max-width: @m){width: 100%;}
		}
		@media (max-width: @m){width: 100%; border: 0;}
	}
</style>
