<template>
	<BaseCmsNav code="main_categories" v-slot="{ items }">
		<ul v-if="items?.length" class="c-categories-cnt" :class="[{'c-categories-cnt-two': items.length > 9}]">
			<li v-for="item in items" :key="item.id" class="c-category" :class="['c-category-'+ item.code, item.style, item.code]">
				<NuxtLink :to="item.url_without_domain">
					<span>
						<span v-if="item.image" class="img-cat-main"><BaseUiImage :src="item.image_upload_path" width="68" height="68" :alt="item.title" /></span>
						<span>{{item.title}}</span>
					</span>
				</NuxtLink>
			</li>
		</ul>
	</BaseCmsNav>
</template>

<style scoped lang="less">
	.c-categories-cnt{
		list-style: none; position: relative; padding-top: 8px; display: grid; grid-template-columns: repeat(3, 1fr); flex-wrap: wrap; gap: 8px; max-width: 544px; margin: auto; width: 100%;
		li{
			position: relative; background: var(--colorLightBlueBackground); border-radius: var(--inputBorderRadius); transition: background .3s, box-shadow .3s;
			&>a{
				display: flex; width: 100%; height: 100%; aspect-ratio: 1; align-items: center; justify-content: center; font-weight: bold; font-size: 14px; line-height: 1.2; color: var(--colorBase); position: relative; text-decoration: none; .transition(color);
				@media (max-width: @ms){font-size: 15px;}
				&>span{display: flex; flex-direction: column; align-items: center;}
			}
			@media (min-width: @h){
				&:hover{
					background: var(--colorWhite); box-shadow: 0 8px 20px 0 rgba(0, 89, 194, 0.1); z-index: 1;
					a{color: var(--colorBaseBlue);}
				}
			}
			&.c-category-discount{
				a{color: var(--colorRed);}
			}
		}
		@media (max-width: @t){max-width: 484px;}
		@media (max-width: @m){max-width: 480px;}
	}
	.c-categories-cnt-two{
		grid-template-columns: repeat(5, 1fr); max-width: 800px;
		@media (max-width: @t){max-width: 700px;}
		@media (max-width: @m){grid-template-columns: repeat(2, 1fr);}
	}
	.img-cat-main{
		display: flex; align-items: center; justify-content: center; width: 65px; height: 65px; align-self: center; margin-bottom: 25px;
		@media (max-width: @ms){width: 45px; height: 35px; margin-bottom: 13px;}
		:deep(img){
			max-height: 55px; width: 100%;
			@media (max-width: @ms){max-height: 40px;}
		}
	}
</style>
