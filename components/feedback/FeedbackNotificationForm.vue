<template>
	<div class="cd-not-available" v-if="productId">
		<BaseCmsLabel code="product_not_available_header" class="cd-not-available-header" tag="div" v-if="status == 'not-available'" />
		<BaseCmsLabel code="product_on_request_header" class="cd-not-available-header cd-in-comming-header" tag="div" v-else-if="status == 'on-request'" />
		<BaseCmsLabel code="product_in_comming_header" class="cd-not-available-header cd-in-comming-header" tag="div" v-else />
		<div class="cd-notifyme-form">
			<BaseFeedbackNotificationForm class="form-notifyme cd-form-notifyme form-animated-label" v-slot="{fields, status, loading}">
				<template v-if="!status?.success">
					<BaseFormField v-for="field in fields" :key="field.name" :item="field" v-slot="{errorMessage, floatingLabel}">
						<p v-if="field.type != 'hidden'" :class="{'ffl-floated': floatingLabel}">
							<BaseFormInput :id="`notification-${field.name}`" />
							<label :for="`notification-${field.name}`"><BaseCmsLabel :code="'notify_'+field.name" /></label>
							<span class="error" v-show="errorMessage" v-html="errorMessage" />
						</p>
						<BaseFormInput v-else :id="`notification-${field.name}`" :value="productId" />
					</BaseFormField>

					<button :disabled="loading" class="btn btn-green btn-notifyme-form" type="submit"><BaseCmsLabel :code="props.status == 'on-request' ? 'send' : 'notifyme'" /></button>
				</template>
				<div class="notifyme-success" v-show="status?.success">
					<BaseCmsLabel code="notifyme_catalog_ty" class="notifyme-success-content" tag="div" />
				</div>
			</BaseFeedbackNotificationForm>
		</div>
	</div>
</template>

<script setup>
	const props = defineProps(['item', 'status']);
	const productId = computed(() => (props.item?.widget_code) ? props.item.widget_code.replace(/_/g, "-") : null);
</script>

<style lang="less" scoped>
	.cd-not-available{
		position: relative; display: block; flex-grow: 1;
		@media (max-width: @m){width: 100%;}
	}
	.cd-not-available-header{
		display: block; margin-bottom: 10px;
		:deep(h2){display: block; font-size: var(--fontSizeSpecial); color: var(--colorRed); padding: 0 0 8px;}
		:deep(p){padding-bottom: 5px;}
	}
	.cd-in-comming-header{
		margin-top: 10px;
		:deep(h2){color: var(--colorOrange);}
	}
	.cd-form-notifyme{
		position: relative; display: block;
		:deep(input){border: none; box-shadow: 0 5px 20px rgba(0,12,26,0.05);}
		:deep(.form-field){padding-bottom: 0;}
	}
	@media (max-width: @ms){
		.btn{width: 100%; margin: 10px 0 0; padding: 14px 24px;}
	}
	.error{position: relative;}
	.notifyme-success{font-weight: bold; color: var(--colorGreen);}
</style>
