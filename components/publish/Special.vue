<template>
	<div class="pw-special-cnt">
		<div class="wrapper">
			<div class="pw-special">
				<slot name="header">
					<h2 class="pw-special-title"><BaseCmsLabel code="read_another" /></h2>
				</slot>
				<div class="pw-special-items">
					<PublishIndexEntry v-for="item in items" :key="item.id" :item="item" :with-category="withCategory" />
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	const props = defineProps({
		items: {
			type: Array,
			required: true
		},
		withCategory: {
			type: Boolean,
			default: false
		}
	});
</script>

<style lang="less" scoped>
	.pw-special-cnt{
		background: var(--colorLightBlueBackground); padding: 80px 0 88px;
		@media (max-width: @t){padding: 56px 0 70px;}
		@media (max-width: @m){padding: 32px 0 24px;}
	}
	.pw-special-title{
		padding: 0 0 32px;
		@media (max-width: @m){font-size: 19px; padding-bottom: 17px;}
	}
	.pw-special-items{
		margin: 0 -20px; display: flex;
		@media (max-width: @m){display: block; margin: 0;}
	}

	@media (min-width: calc(@m + 1px)){
		.pp{margin-bottom: 0;}
	}
</style>
