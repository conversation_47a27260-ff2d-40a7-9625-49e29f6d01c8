<template>
	<div class="p-categories" v-if="rootCategory?.children">
		<NuxtLink v-for="subcategory in subcategories" :key="subcategory.id" :to="subcategory.url_without_domain" :class="'p-category p-category-'+subcategory.code">
			<span>{{ subcategory.title }}</span>
		</NuxtLink>
		<NuxtLink class="p-category p-category-all" :to="rootCategory?.url_without_domain" v-if="category && category.level > 1">
			<span><BaseCmsLabel code="all_articles" /></span>
		</NuxtLink>
	</div>
</template>
<script setup>
	const props = defineProps(['category', 'rootCategory']);
	const subcategories = computed(() => {
		if(props.category && props.rootCategory?.children?.length){
			return props.rootCategory.children.filter(el => {
				return el.url != props.category.url;
			});
		}
		return props.rootCategory?.children || [];
	});
</script>
<style lang="less" scoped>
	.p-categories{
		display: flex;
		@media (max-width: @m){margin: 0 -4px 24px;}
	}
	.p-category{
	    display: flex; align-items: center; color: var(--colorBase); background: #fff; font-size: 16px; font-weight: bold; text-decoration: none; padding: 0 24px; box-shadow: 0 8px 18px 0 rgba(0,89,194,0.12); height: 56px; margin-left: 8px; border-radius: var(--inputBorderRadius); .transition(color,0.3s);
	    @media (max-width: @t){padding: 0 16px; height: 40px; font-size: 13px;}
		@media (max-width: @m){flex: 1 1 33.333%; height: 88px; background: var(--colorLightBlueBackground); box-shadow: none; margin: 0 4px; padding: 0; justify-content: center;}
		span{
	           position: relative; padding-left: 38px;
	           @media (max-width: @t){padding-left: 25px;}
			   @media (max-width: @m){padding: 39px 0 0;}
			   &:before{
					.pseudo(28px,28px); top: -2px; left: 0px; background: url(assets/images/custom-icons/comments.svg) no-repeat; background-size: contain;
					@media (max-width: @t){width: 21px; height: 21px; top: -1px;}
					@media (max-width: @m){width: 31px; height: 30px; top: 0; left: 50%; margin-left: -15px;}
				}
	       }
	    &:hover{
	        @media (min-width: @h){color: var(--colorBaseBlue);}
	    }
	}
	.p-category-tips{
	    span{
	        &:before{background-image: url(assets/images/custom-icons/idea.svg);}
	    }
	}
	.p-category-news{
	    span{
	        &:before{background-image: url(assets/images/custom-icons/promotion.svg);}
	    }
	}
	.p-category-all{
	    span{
	        &:before{background-image: url(assets/images/custom-icons/all-articles.svg);}
	    }
	}
</style>
