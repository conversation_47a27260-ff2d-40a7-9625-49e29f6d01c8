<template>
	<div :class="['pw', homepage && 'pw-hp']" v-if="items?.length">
		<div class="wrapper pw-wrapper">
			<div class="pw-left">
				<div class="pw-left-inner">
					<h2 v-if="homepage" class="pw-title" v-html="rootCategory.seo_h1"></h2>
					<div v-if="homepage" class="pw-subtitle"><BaseCmsLabel code="blog_subtitle" /></div>

					<slot />

					<div class="m-p-categories">
						<ClientOnly>
							<PublishSubcategories :rootCategory="rootCategory" v-if="mBreakpoint" />
						</ClientOnly>
					</div>

					<PublishIndexEntry :item="items[0]" mode="big" withCategory="1" />
				</div>
			</div>
			<div class="pw-right">
				<div class="pw-right-inner">
					<PublishSubcategories :rootCategory="rootCategory" />

					<div class="pw-items">
						<PublishIndexEntry v-for="post in items.slice(1)" :key="post.id" :item="post" mode="small" withCategory="1" />
					</div>

					<NuxtLink v-if="homepage" :to="rootCategory.url_without_domain" class="btn pw-show-all"><BaseCmsLabel code="show_all_posts" /></NuxtLink>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	const props = defineProps(['items', 'rootCategory', 'homepage']);
	const {onMediaQuery} = useDom();
	const {matches: mBreakpoint} = onMediaQuery({
		query: '(max-width: 980px)',
	});
</script>

<style lang="less" scoped>
	@media (min-width: calc(@m + 1px)){
		.pw{
			background: var(--colorLightBlueBackground); border-bottom: 1px solid var(--colorBorderLightForms); margin-bottom: 80px;
			@media (max-width: @t){margin-bottom: 40px;}
		}
		.pw-hp{margin-bottom: 0; border-bottom: 0;}
		.pw-wrapper{display: flex;}
		.pw-left{
			flex: 1 1 calc(~'50% - -32px'); position: relative; padding: 56px 32px 62px 0;
			@media (max-width: @t){flex: 1 1 50%; padding: 40px 32px 42px 0;}
			&:before {.pseudo(auto,auto); top: 0; left: -50vw; right: 0; bottom: 0; background: #fff;}
		}
		.pw-left-inner{position: relative;}
		.pw-right{
			flex: 1 1 calc(~'50% - 32px'); padding: 96px 0 80px 32px;
			@media (max-width: @t){flex: 1 1 50%; padding: 80px 0 64px 32px;}
			@media (max-width: @m){
				:deep(.p-categories){display: none;}
			}
		}
		.pw-items{padding: 32px 0 0;}
		:deep(.p-category){
			box-shadow: none; .transition(~"color,box-shadow");
			@media (min-width: @h){
				&:hover{box-shadow: 0 8px 18px 0 rgba(0,89,194,0.12);}
			}
		}
	}
	@media (max-width: @m){
		.pw{padding: 24px 0 0;}
		.pw-right .p-categories{display: none;}
		.m-p-categories{
			min-height: 112px;
			.p-categories{display: flex;}
		}
	}
	.pw-title{
		padding: 29px 0 0; font-size: 39px;
		@media (max-width: @t){font-size: 28px;}
		@media (max-width: @m){font-size: 23px; padding: 0;}
		:deep(span){color: var(--colorBaseBlue);}
	}
	.pw-subtitle{
		margin-bottom: -22px;
		@media (max-width: @t){font-size: 14px;}
		@media (max-width: @m){margin: 0 0 16px;}
	}
	.pw-show-all{
		width: 100%; margin-top: 16px;
		@media (max-width: @t){font-size: 14px;}
		@media (max-width: @m){font-size: 15px; margin-top: 24px;}
	}
	.pw-hp{
		margin-bottom: 0;
		@media (max-width: @m){background: var(--colorLightBlueBackground); padding: 48px 0;}
		:deep(.p-category){background: #fff;}
	}
</style>
