<template>
	<NuxtLink :to="item.url_without_domain" :class="['pp', mode && 'pp-'+mode ]">
		<div class="pp-image">
			<BaseUiImage loading="lazy" :data="image" width="640" height="424" default="/images/no-image-640x424.jpg" :picture="[{maxWidth: '980px', src: picture, default: '/images/no-image-640x424.jpg'}]" />
		</div>
		<div class="pp-cnt">
			<div class="pp-title">{{item.title}}</div>
			<div v-if="withCategory && item.category_title" class="pp-category">{{item.category_title}}</div>
		</div>
	</NuxtLink>
</template>

<script setup>
	const props = defineProps(['item', 'mode', 'withCategory']);
	const image = computed(() => {
		if(props.mode == 'big') {
			return props.item?.main_image_thumbs?.['width640-height360-crop1'];
		}
		return props.item?.main_image_thumbs?.['width550-height325-crop1'];
	})
	const picture = computed(() => {
		if(props.item.main_image_thumbs?.['width400-height225-crop1']?.thumb) {
			return props.item.main_image_thumbs['width400-height225-crop1'].thumb;
		}
		if(props.item.main_image_thumbs?.['width640-height360-crop1']?.thumb) {
			return props.item.main_image_thumbs['width640-height360-crop1'].thumb;
		}
		return null;
	})
</script>

<style lang="less" scoped>
	.pp{
		display: block; margin: 0 20px 50px; width: calc(~'33.333% - 40px'); text-decoration: none; color: var(--colorBase);
		@media (max-width: @m){margin: 0 0 16px; width: auto; display: flex; align-items: center;}
		@media (min-width: @h){
			&:hover{
				.pp-title{color: var(--colorBaseBlue);}
			}
		}
	}
	.pp-image{
		:deep(img){display: block; border-radius: var(--inputBorderRadius);}
		@media (max-width: @m){flex: 1 1 44%;}
	}
	.pp-cnt{
		padding: 16px 32px 0;
		@media (max-width: @t){padding: 10px 16px 0;}
		@media (max-width: @m){flex: 1 1 56%; padding: 0 0 0 16px;}
	}
	.pp-title{
		font-size: 20px; font-weight: bold; line-height: 1.3; padding: 0; .transition(color,0.3s);
		@media (max-width: @t){font-size: 16px;}
		@media (max-width: @m){font-size: 15px; line-height: 1.4;}
	}
	.pp-category{
		color: var(--colorBaseBlue); font-size: 14px; font-weight: bold; padding: 3px 0 0;
		@media (max-width: @t){font-size: 12px;}
		@media (max-width: @m){font-size: 13px; padding: 0;}
	}
	@media (min-width: calc(@m + 1px)){
		.pp-big{
			width: auto; margin: 52px 0 0;
			.pp-cnt{
				padding: 26px 64px;
				@media (max-width: @t){padding: 16px 40px;}
			}
			.pp-title{
				font-size: 32px; line-height: 1.2;
				@media (max-width: @t){font-size: 24px;}
			}
			.pp-category{
				font-size: 16px; padding-top: 6px;
				@media (max-width: @t){font-size: 14px;}
			}
		}
		.pp-small{
			width: auto; margin: 0 0 8px; padding: 8px; display: flex; background: #fff; border-radius: var(--inputBorderRadius); align-items: center; .transition(box-shadow,0.3s);
			.pp-image{
				flex: 1 0 176px; max-width: 176px;
				@media (max-width: @t){flex: 1 1 47%;}
			}
			.pp-cnt{
				padding: 10px 20px 10px 32px; flex: 1 1 auto;
				@media (max-width: @t){padding: 10px 16px 10px 24px;}
			}
			.pp-title{
				@media (max-width: @t){font-size: 14px;}
			}
			.pp-category{
				@media (max-width: @t){font-size: 11px;}
			}
			@media (min-width: @h){
				&:hover{box-shadow: 0 8px 18px 0 rgba(0,89,194,0.12);}
			}
		}
	}
</style>
