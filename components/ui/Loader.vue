<template>
	<div class="lds-ring-cnt">
		<div class="lds-ring" :class="[{'medium': props.size == 'medium'}, {'large': props.size == 'large'}]">
			<div></div>
			<div></div>
			<div></div>
			<div></div>
		</div>
	</div>
</template>

<script setup>
	const props = defineProps({
		size: {
			type: String,
			default: 'small',
		},
	});
</script>

<style scoped lang="less">
	.lds-ring {
	  display: inline-block;
	  position: relative;
	  width: 20px;
	  height: 20px;
	  &.medium{width: 40px; height: 40px;}
	  &.large{width: 60px; height: 60px;}
	}
	.lds-ring div {
	  box-sizing: border-box;
	  display: block;
	  position: absolute;
	  width: 17px;
	  height: 17px;
	  margin: 2px;
	  border: 2px solid #fff;
	  border-radius: 50%;
	  animation: lds-ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
	  border-color: #fff transparent transparent transparent;
	  &.medium{width: 34px; height: 34px; margin: 4px; border-width: 4px;}
	  &.large{width: 51px; height: 51px; margin: 6px; border-width: 6px;}
	}
	.lds-ring div:nth-child(1) {
	  animation-delay: -0.45s;
	}
	.lds-ring div:nth-child(2) {
	  animation-delay: -0.3s;
	}
	.lds-ring div:nth-child(3) {
	  animation-delay: -0.15s;
	}
	@keyframes lds-ring {
	  0% {
	    transform: rotate(0deg);
	  }
	  100% {
	    transform: rotate(360deg);
	  }
	}
</style>
