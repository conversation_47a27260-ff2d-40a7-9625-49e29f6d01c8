<template>
	<BaseCmsPage v-slot="{page}">
		<AuthMainLayout>
			<template #authContent>
				<h1>{{ page?.seo_h1 }}</h1>
				<BaseCmsLabel tag="div" class="auth-change-password-subtitle" code="change_password_subtitle" />
				<BaseAuthChangePasswordForm v-slot="{fields, status, loading}" class="form-animated-label">
					<div v-if="status && status?.success" class="global-success"><BaseCmsLabel :code="status.data?.label_name" /></div>

					<template v-if="!status?.success">
						<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage, floatingLabel}">
							<p class="field" :class="['field-' + item.name, {'ffl-floated': floatingLabel}, {'err': errorMessage}]">
								<BaseFormInput />
								<BaseCmsLabel v-if="item.name == 'password'" tag="label" for="password" code="desired_password" />
								<BaseCmsLabel v-else tag="label" :for="item.name" :code="item.name" />
								<span class="error" v-show="errorMessage" v-html="errorMessage" />
							</p>
						</BaseFormField>
						<button type="submit" :class="{'loading': loading}"><UiLoader v-if="loading" /><BaseCmsLabel code="save_new_password" /></button>
					</template>
				</BaseAuthChangePasswordForm>
			</template>
		</AuthMainLayout>
	</BaseCmsPage>
</template>

<script setup></script>

<style lang="less" scoped>
	.auth-change-password-subtitle{font-weight: bold; padding-bottom: 10px;}
	@media(max-width: @m){
		button{width: 100%; margin-bottom: 8px;}
	}
</style>
