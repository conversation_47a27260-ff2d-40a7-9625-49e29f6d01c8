<template>
	<BaseCmsPage v-slot="{page}">
		<AuthMainLayout>
			<template #authContent>
				<h1>{{ page?.seo_h1 }}</h1>
				<div class="auth-edit-inner">
					<BaseAuthEditForm class="form-animated-label edit-form-animated-label" v-slot="{fields, loading, status}">
						<template v-if="status?.data?.errors?.length">
							<div class="global-error" v-for="error in status.data.errors" :key="error">{{ error.field }}: {{ error.error }}</div>
						</template>
						<div v-if="status?.success" class="global-success"><BaseCmsLabel :code="status.data.label_name" /></div>

						<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage, floatingLabel, isTouched, value}">
							<BaseCmsLabel v-if="item.name == 'first_name'" class="auth-personal-information" code="personal_information" tag="h2" />
							<BaseCmsLabel v-if="item.name == 'company_name'" class="auth-company-information" code="company_information" tag="h2" />
							<p class="field" :class="['field-' + item.name, {'ffl-floated': floatingLabel}, {'err': errorMessage}, {'success': !errorMessage && isTouched && value}]">
								<BaseFormInput :id="item.name" />
								<BaseCmsLabel tag="label" :for="item.name" :code="item.name" />
								<div v-if="item.name == 'location'" class="field-tooltip"><BaseCmsLabel code="location_tooltip" /></div>
								<span class="error" v-show="errorMessage" v-html="errorMessage" />
							</p>
						</BaseFormField>
						<button type="submit" :class="{'loading': loading}" :disabled="loading"><UiLoader v-if="loading" /><BaseCmsLabel code="save_information" /></button>
					</BaseAuthEditForm>
				</div>
			</template>
		</AuthMainLayout>
	</BaseCmsPage>
</template>

<style lang="less">
	.field-zipcode, .field-city{display: none!important;}
	.field-newsletter{
		margin-top: 6px; margin-bottom: 16px;
		@media(max-width: @m){margin-top: 0; margin-bottom: 15px;}
	}
	.auth-edit-inner{
		.edit-form-animated-label{
			display: block; column-count: 2; column-gap: 48px; padding-right: 32px;
			.field{width: 100%; display: inline-block;}
			.field-zipcode,.field-city{display: inline-block; width: 165px; margin-right: 8px; vertical-align: top;}
			.field-city{width: calc(~"100% - 173px"); margin-right: 0 !important;}
			.global-error,.global-success{column-span: all;}
		}
		@media(max-width: @t){
			.edit-form-animated-label{padding-right: 0; column-gap: 40px;}
		}
		@media(max-width: @m){
			padding: 0 16px;
			.edit-form-animated-label{
				column-gap: unset; display: block; column-count: unset;
				.field{display: block;}
				.field-zipcode{width: 43%; display: inline-block;}
				.field-city{width: calc(~"57% - 8px"); display: inline-block;}
			}
			button{width: 100%; margin-bottom: 8px;}
		}
	}
	.auth-personal-information,.auth-company-information{
		font-size: 16px; line-height: 24px; padding-top: 0; padding-bottom: 10px;
		@media(max-width: @m){font-size: 15px; line-height: 21px;}
	}
	.auth-company-information{
		@media(max-width: @m){padding-top: 14px;}
	}
</style>
