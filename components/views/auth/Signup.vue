<template>
	<BaseCmsPage v-slot="{page}" :fetch-slug-segments="2">
		<h1 style="display: none">{{ page?.seo_h1 }}</h1>
		<AuthLoginRegisterLayout>
			<template #col1>
				<AuthSocialLogin mode="register" />
				<BaseAuthSignupForm class="form-signup form-animated-label" v-slot="{fields, errors, apiErrors, status, contentType, loading}">
					<BaseCmsLabel v-if="contentType != 'confirmSignup'" code="signup_subtitle" tag="div" class="signup-subtitle" />
					<BaseAuthConfirmSignup v-if="contentType == 'confirmSignup'" v-slot="{status}" redirect="auth_login" :redirect-timeout="3000">
						<BaseCmsLabel :code="status.label_name" />
					</BaseAuthConfirmSignup>
					<template v-else>
						<template v-if="!status?.success">
							<template v-if="apiErrors?.length">
								<div class="global-error" v-for="apiError in apiErrors" :key="apiError">{{ apiError.field }}: {{ apiError.error }}</div>
							</template>
							<template v-if="Object.keys(errors)?.length">
								<BaseCmsLabel tag="div" class="global-error" code="form_validation_error" data-scroll-to-error />
							</template>
							<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage, floatingLabel, isTouched, value}">
								<p class="field" :class="['field-' + item.name, {'ffl-floated': floatingLabel}, {'err': errorMessage}, {'success': !errorMessage && isTouched && value}]" v-interpolation>
									<span v-if="item.name == 'accept_terms'">
										<AuthTermsModal />
									</span>
									<BaseFormInput :id="'signup-'+item.name" />
									<BaseCmsLabel tag="label" :for="'signup-'+item.name" :code="item.name" />
									<span class="error" v-show="errorMessage" v-html="errorMessage" />
								</p>
							</BaseFormField>
							<button class="confirm-registration" type="submit" :class="{'loading': loading}"><UiLoader v-if="loading" /><BaseCmsLabel code="form_login" /></button>
						</template>
						<div v-else>
							<BaseCmsLabel tag="div" class="global-success" :code="status.data.label_name" />
						</div>
					</template>
				</BaseAuthSignupForm>
			</template>
			<template #col2>
				<div v-if="page?.content" class="cms-content" v-html="page?.content" v-interpolation></div>
				<AuthLoginRegisterBenefits mode="register" />
				<div class="checkin-have-account">
					<BaseCmsLabel code="have_account_already" tag="h2" />
					<div class="auth-buttons auth-checkin-submit-container">
						<BaseAuthUser v-slot="{urls}">
							<NuxtLink :to="urls.auth_login" class="btn btn-blue">
								<BaseCmsLabel code="login" />
							</NuxtLink>
							<NuxtLink :to="urls.auth_forgotten_password" class="link-forgotten-password" data-auth_login="forgotten_password" data-siteform_response="show_hide" data-siteform_response_hide="-1">
								<BaseCmsLabel code="forgotten_password_userbox" />
							</NuxtLink>
						</BaseAuthUser>
					</div>
				</div>
			</template>
		</AuthLoginRegisterLayout>
	</BaseCmsPage>
</template>

<script setup>
	const {getAppUrl} = useApiRoutes();
</script>

<style scoped lang="less">
	//.form-animated-label input[type=checkbox] + label{padding-left: 34px;}
	.checkin-have-account{
		padding-top: 48px;
		h2{padding-bottom: 16px;}
		@media(max-width: @m){
			padding-top: 40px;
			h2{padding-bottom: 8px;}
			.btn{width: 40%; min-width: 136px;}
		}
	}
	.form-signup{
		.confirm-registration{margin-top: 6px;}
		@media(max-width: @m){
			.confirm-registration{width: 100%; margin-top: 5px;}
		}
	}
	.auth-checkin-submit-container{display: flex; align-items: center;}
	.signup-subtitle{font-weight: bold; padding-bottom: 8px; font-size: 16px; line-height: 24px;}
	.field-accept_terms{
		input[type=checkbox] + label{padding-left: 34px;}
		:deep(a){
			color: var(--colorBase); text-decoration-color: var(--colorBaseBlue); text-underline-offset: 3px;
			&:hover{text-decoration-color: transparent;}
		}
		.error{padding-left: 34px;}
	}
</style>
