<template>
	<BaseCmsPage v-slot="{page}">
		<div class="forgotten-password-page">
			<div class="wrapper">
				<h1>{{ page?.seo_h1 }}</h1>
				<ClientOnly>
					<BaseAuthForgottenPasswordForm class="form-animated-label" v-slot="{fields, loading, status, urls}">
						<div v-if="status && status?.success" class="global-success"><BaseCmsLabel :code="status.data?.label_name" /></div>

						<template v-if="!status || (status && !status.success)">
							<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage, floatingLabel}">
								<p class="field" :class="['field-' + item.name, {'ffl-floated': floatingLabel}, {'err': errorMessage}]">
									<BaseFormInput />
									<BaseCmsLabel tag="label" :for="item.name" :code="item.name" />
									<span class="error" v-show="errorMessage" v-html="errorMessage" />
								</p>
							</BaseFormField>
							<div class="form-login-forgotten-cnt">
								<button type="submit" class="forgotten-pw-btn" :class="{'loading': loading}"><UiLoader v-if="loading" /><BaseCmsLabel code="send" /></button>
								<div class="auth-links">
									<NuxtLink :to="urls.auth_login"><BaseCmsLabel code="back_to_login" /></NuxtLink>
									<NuxtLink :to="urls.auth_signup"><BaseCmsLabel code="add_new_account" /></NuxtLink>
								</div>
							</div>
						</template>
					</BaseAuthForgottenPasswordForm>
					<template #fallback>
						<BaseThemeUiLoading />
					</template>
				</ClientOnly>
			</div>
		</div>
	</BaseCmsPage>
</template>

<style scoped lang="less">
	.forgotten-password-page{
		padding: 56px 0 88px; background: var(--colorLightBlueBackground);
		h1{text-align: center;}
		.wrapper{max-width: 500px;}
		@media(max-width: @m){
			padding: 24px 0 48px;
			h1{font-size: 24px; line-height: 30px;}
			.wrapper{max-width: unset;}
		}
	}
	.forgotten-pw-btn{
		@media(max-width: @m){width: 40%; min-width: 136px;}
	}
	.auth-links{
		display: flex; flex-flow: column; align-items: center; flex-grow: 1; font-size: var(--fontSizeSmall); line-height: var(--lineHeight);
		@media(max-width: @m){font-size: 13px; line-height: 20px;}
	}
</style>
