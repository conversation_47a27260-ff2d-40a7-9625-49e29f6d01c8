<template>
	<BaseCmsPage v-slot="{page}" :fetch-slug-segments="2">
		<CmsPageLayout>
			<h1>{{ page?.seo_h1 }}</h1>
			<ClientOnly>
				<BaseAuthNewPasswordForm class="form-animated-label" v-slot="{fields, submitting, onSubmit, status, loading}">
					<div v-if="status?.success == false" class="global-error"><BaseCmsLabel :code="status.data?.label_name" /></div>
					<div v-if="status?.success" class="global-success"><BaseCmsLabel :code="status.data?.label_name" /></div>

					<div v-if="!status || status?.success == false" :class="{'submitting': submitting}" @submit="onSubmit">
						<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage, floatingLabel}">
							<p class="field" :class="['field-' + item.name, {'ffl-floated': floatingLabel}, {'err': errorMessage}]">
								<BaseFormInput />
								<BaseCmsLabel tag="label" :for="item.name" :code="item.name" />
								<span class="error" v-show="errorMessage" v-html="errorMessage" />
							</p>
						</BaseFormField>
						<button type="submit" :class="{'loading': loading}"><UiLoader v-if="loading" /><BaseCmsLabel code="send" /></button>
					</div>
				</BaseAuthNewPasswordForm>
				<template #fallback>
					<BaseThemeUiLoading />
				</template>
			</ClientOnly>
		</CmsPageLayout>
	</BaseCmsPage>
</template>
