<template>
	<BaseCmsPage v-slot="{page}">
		<h1 style="display: none;">{{ page?.seo_h1 }}</h1>
		<AuthLoginRegisterLayout>
			<template #col1>
				<AuthSocialLogin mode="login" />
				<AuthLoginForm mode="login" />
			</template>
			<template #col2>
				<div v-if="page?.content" class="cms-content" v-html="page.content" ref="content" v-interpolation />
				<BaseAuthUser v-slot="{urls}">
					<NuxtLink :to="urls.auth_signup" class="btn btn-blue btn-signup-on-login">
						<BaseCmsLabel code="create_account" />
					</NuxtLink>
				</BaseAuthUser>
				<AuthLoginRegisterBenefits mode="login" />
			</template>
		</AuthLoginRegisterLayout>
	</BaseCmsPage>
</template>
