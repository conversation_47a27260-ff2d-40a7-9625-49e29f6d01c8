<template>
	<BaseCmsPage v-slot="{page}">
		<AuthMainLayout>
			<template #authContent>
				<h1>{{ page?.seo_h1 }}</h1>
				<ClientOnly>
					<BaseAuthCoupons v-slot="{items}">
						<div class="auth-coupons-list-header">
							<div class="auth-coupons-form-cnt">
								<WebshopCouponForm mode="auth" />
								<BaseCmsLabel code="auth_coupons_text" tag="div" class="auth-coupons-text" />
							</div>
						</div>
						<div v-if="items?.length" class="auth-coupons-list">
							<div class="auth-coupons-table">
								<div v-for="item in items" :key="item.id" class="auth-coupons-table-row">
									<div class="col-value-description">
										<div class="col-value">
											<strong v-if="item.type == 'f'"><BaseUtilsFormatCurrency :price="item.coupon_price" /></strong>
											<strong v-else>-{{ item.coupon_percent * 100 }}% popusta</strong>
										</div>

										<template v-if="item.description">
											<div class="col-description">{{ item.description }}</div>
										</template>
									</div>

									<div class="col-valid">
										<template v-if="+item.max_used > 0 && item.current_used >= item.max_used">
											<BaseCmsLabel code="coupon_used" />
										</template>
										<template v-else-if="item.datetime_expire">
											<BaseCmsLabel v-if="item.datetime_expire > Math.floor(Date.now() / 1000)" code="coupon_valid_until" tag="div" class="col-valid-label" />
											<template v-else><div class="col-valid-label col-valid-label-expired">istekao</div></template>
											<div class="col-valid-value" :class="{ 'col-valid-value-expired' : item.datetime_expire < Math.floor(Date.now() / 1000) }">
												<BaseUtilsFormatDate :date="item.datetime_expire" />
											</div>
										</template>
										<template v-else>-</template>
									</div>

									<div class="col-code">
										<BaseCmsLabel tag="div" class="col-code-label" code="coupon_code2" />
										<div class="col-code-value">{{ item.code }}</div>
									</div>
								</div>
							</div>
						</div>
						<BaseCmsLabel v-else code="auth_no_coupons" tag="div" class="auth-no-content" />
					</BaseAuthCoupons>
					<template #fallback>
						<BaseThemeUiLoading />
					</template>
				</ClientOnly>
			</template>
		</AuthMainLayout>
	</BaseCmsPage>
</template>

<script setup>
	const labels = useLabels();
	const fieldCoupon = ref({'id': 'coupon', 'type': 'text'})
</script>

<style scoped lang="less">
	.auth-coupons-label{
		display: none;
		@media(max-width: @m){display: block; font-weight: bold; padding-bottom: 12px; font-size: 17px; line-height: 24px;}
	}
	.auth-coupons-form{
		width: 415px; flex-shrink: 0; align-items: center;
		@media(max-width: @t){width: auto; flex-grow: 1;}
	}
	.auth-no-content{
		@media(max-width: @m){padding: 0 16px;}
	}
	.auth-coupons-form-cnt{
		padding: 32px 90px 32px 32px; background: var(--colorLightBlueBackground); margin-bottom: 16px; display: flex; align-items: center; border-radius: var(--inputBorderRadius);
		/*.ww-coupons-add{
			position: relative; background: white; border-radius: var(--inputBorderRadius);
			input{
				padding: 0 90px 0 20px; font-size: 16px; line-height: 24px;
				&::placeholder{opacity: .5;}
			}
		}*/
		@media(max-width: @t){padding: 14px 16px; margin-bottom: 24px;}
		@media(max-width: @m){display: block; margin: 0 16px 24px;}
	}
	/*.auth-coupons-add{
		position: relative; box-shadow: 0 0 40px 0 rgba(0,89,194,0.1);
		input{border: unset; padding-right: 100px;}
		@media(max-width: @m){
			box-shadow: 0 5px 20px 0 rgba(0,12,26,0.13);
			input{font-size: 15px; line-height: 21px; padding-left: 16px;}
		}
	}
	.auth-coupon-btn-add{
		min-height: 40px; padding: 0 18px; position: absolute; right: 8px; box-shadow: unset; top: 8px; background: white; color: var(--colorBaseBlue); border: 1px solid var(--colorBaseBlue); font-size: 15px; transition: color .3s, background .3s;
		&:hover{background: var(--colorBaseBlue); color: white;}
	}*/
	.auth-coupons-text{
		font-size: 12px; line-height: 17px; color: var(--colorTextLightGray); padding-left: 32px; margin-left: 48px; position: relative;
		&:before{.pseudo(auto,auto); font: 20px/20px var(--fonti); .icon-info3(); left: 0; top: 0;}
		@media(max-width: @t){max-width: 320px; flex-shrink: 0; margin-left: 30px;}
		@media(max-width: @m){
			margin-left: 0; margin-top: 10px; padding-left: 22px;
			&:before{font: 16px/16px var(--fonti);}
		}
	}
	.coupon_message{
		font-size: 11px; line-height: 14px; padding-top: 10px; padding-left: 20px; color: var(--colorRed);
		@media(max-width: @m){padding-top: 8px; padding-left: 16px;}
	}

	//coupons table
	.auth-coupons-table-row{
		margin-bottom: 8px; background: white; border-radius: 5px; box-shadow: 0 10px 40px 0 rgba(0,89,194,0.08); display: flex; border: 1px solid var(--colorBorderLightForms); min-height: 95px;
		&:last-child{margin-bottom: 0;}
		&>div{padding: 24px 20px 24px 32px; align-items: unset; justify-content: center; flex-flow: column; border-bottom: unset; display: flex; font-size: 14px; line-height: 18px;}
		@media(max-width: @t){
			min-height: 70px;
			&>div{padding: 14px 16px; font-size: 13px; line-height: 17px;}
		}
		@media(max-width: @m){
			display: block; margin: 0 16px 24px; min-height: unset; box-shadow: 0 8px 16px 0 rgba(127,133,140,0.2);
			&>div{border-bottom: 1px solid var(--colorBorderLightForms);}
		}

	}
	.col-value-description{
		flex-grow: 1;
		.col-value{
			padding-bottom: 2px;
			strong{font-size: 20px; line-height: 28px; display: block;}
		}
		@media(max-width: @t){
			.col-value{
				strong{font-size: 17px; line-height: 21px;}
			}
		}
	}
	.col-valid,.col-code{
		width: 210px; flex-shrink: 0; border-left: 1px solid var(--colorLightGray);
		@media(max-width: @m){width: 100%; flex-shrink: unset; border-left: 0; display: flex; flex-flow: row !important; justify-content: space-between !important;}
	}
	.col-code{
		@media(max-width: @m){border-bottom: unset !important;}
	}
	.col-valid-label,.col-code-label{
		padding-bottom: 6px; position: relative; padding-left: 27px;
		&:before{.pseudo(18px,18px); left: 0; top: 1px; background: url('/assets/images/custom-icons/calendar.svg') center no-repeat; background-size: contain;}
		@media(max-width: @m){
			width: 55%; padding-bottom: 0; padding-left: 32px;
			&:before{top: -1px;}
		}
	}
	.col-valid-value,.col-code-value{
		@media(max-width: @m){width: 45%; text-align: right;}
	}
	.col-code-label{
		padding-left: 29px;
		&:before{.pseudo(20px,13px); top: 3px; background: url('/assets/images/custom-icons/coupons.svg') center no-repeat; background-size: contain;}
		@media(max-width: @m){
			padding-left: 32px;
			&:before{top: 2px;}
		}
	}
	.col-valid-label-expired,.col-valid-value-expired{
		color: var(--colorRed); padding-left: 0;
		&:before{display: none;}
	}
	.col-code-value{color: var(--colorBaseBlue);}
</style>
