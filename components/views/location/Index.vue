<template>
	<BaseCmsPage v-slot="{page}">
		<div class="locations-header">
			<div class="wrapper locations-header-wrapper">
				<CmsBreadcrumbs v-if="page?.breadcrumbs" :items="page.breadcrumbs" />
				<h1 v-if="page?.title">{{ page.title }}</h1>
			</div>
		</div>

		<BaseLocationPoints v-slot="{places, items}">
			<div class="locations-city-list">
				<div class="wrapper locations-city-list-wrapper">
					<div class="locations-city-list-item" v-for="place in places" :key="place.id" @click="scrollTo(place.location_code)">{{place.title}}</div>
				</div>
			</div>

			<ClientOnly>
				<CmsMap :items="items" extraclass="map-locations" />
			</ClientOnly>

			<div class="locations-main">
				<div class="wrapper locations-main-wrapper">
					<div class="location-cities">
						<div class="location-city" v-for="place in places" :key="place.id" :id="place.location_code">
							<CmsLocationItem :place="place" />
						</div>
					</div>
				</div>
			</div>
		</BaseLocationPoints>
	</BaseCmsPage>
</template>

<script setup>
	function scrollTo(index){
		let el = '#' + index;
		let elCity = '#item-container-' + index;
		const winWith = window.innerWidth;
		document.querySelector(el).scrollIntoView({behavior: 'smooth'});
		if(winWith <= 980){
			document.querySelector(elCity).classList.add('active');
		}
	}
</script>

<style lang="less" scoped>
	// page header
	.locations-header{
		position: relative; display: block; padding: 56px 0 40px;
		h1{padding-bottom: 0;}
		@media (max-width: @t){padding: 40px 0 30px;}
		@media (max-width: @m){padding: 24px 0 16px;}
	}
	.locations-city-list{
		position: relative; display: block; background: var(--colorLightBlueBackground); padding: 28px 0;
		@media (max-width: @t){padding: 20px 0;}
		@media (max-width: @m){padding: 23px 0 6px;}
	}
	.locations-city-list-wrapper{display: flex; flex-wrap: wrap; align-items: center;}
	.locations-city-list-item{
		position: relative; display: block; font-weight: bold; padding-left: 25px; margin-right: 88px; cursor: pointer; text-decoration: underline 1px transparent; text-underline-offset: 3px; transition: text-decoration-color 0.3s, color 0.3s;
		&:last-child{margin-right: 0;}
		&:before{.pseudo(17px,24px); background: url(assets/images/custom-icons/pin.svg) center no-repeat; background-size: cover; top: 0; left: 0;}
		@media (min-width: @h){
			&:hover{text-decoration-color: var(--colorBaseBlue); color: var(--colorBaseBlue);}
		}
		@media (max-width: @m){
			font-size: 13px; line-height: 17px; margin: 0 25px 19px 0; padding-left: 20px;
			&:before{width: 13px; height: 18px; top: -1px;}
		}
	}

	// main
	.locations-main{
		position: relative; display: block; background: var(--colorLightBlueBackground); padding: 85px 0 88px;
		@media (max-width: @t){padding: 50px 0 60px;}
		@media (max-width: @m){/*padding: 30px 0;*/padding: 0; background: var(--colorWhite);}

		//@media (max-width: @ms){padding: 0; background: var(--colorWhite);}
	}
	.locations-main-wrapper{
		width: 1000px;
		@media (max-width: @m){width: 100%; padding: 0;}
		//@media (max-width: @ms){width: 100%; padding: 0;}
	}
	.location-city{
		position: relative; display: block; margin-bottom: 64px;
		&:last-child{margin-bottom: 0;}
		/*@media (max-width: @m){margin-bottom: 30px;}
		@media (max-width: @ms){
			margin: 0; border-bottom: 1px solid var(--colorBorderLightForms);
			&:last-child{border-bottom: 0;}
			&.active{background: var(--colorWhite);}
		}*/
		@media (max-width: @m){
			margin: 0; border-bottom: 1px solid var(--colorBorderLightForms);
			&:last-child{border-bottom: 0;}
			&.active{background: var(--colorWhite);}
		}
	}
</style>
