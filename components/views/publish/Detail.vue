<template>
	<BasePublishDetail :root-category="true" v-slot="{item}">
		<div class="wrapper pd-wrapper">
			<div class="pd-head">
				<CmsBreadcrumbs :items="item.breadcrumbs" />
				<h1 class="pd-title">{{ item.seo_h1 }}</h1>
			</div>
		</div>
		<div class="wrapper pd-hero-image" v-if="item.main_image_upload_path">
			<BaseUiImage :data="item.main_image_thumbs?.['width1280-height720-crop1']" :alt="item.main_image_description" :title="item.main_image_title" />
		</div>
		<div class="wrapper pd-wrapper pd-main">
			<div class="pd-info">
				<NuxtLink class="pd-btn-all" :to="item.root_category.url_without_domain" v-if="item.root_category"><BaseCmsLabel code="all_articles" /></NuxtLink>
				<div class="pd-date"><BaseUtilsFormatDate :date="item.datetime_published" /></div>
			</div>
			<div class="pd-content cms-content" v-if="item.content" v-html="item.content" v-interpolation ref="content"></div>
			<CmsShare />
		</div>

		<BaseCatalogProductsWidget :fetch="{related_publish_id: item.id, limit: 20, only_with_image: true, only_available: true}" v-slot="{items}">
			<CatalogSpeciallists :items="items" v-if="items?.length">
				<template #header>
					<h2 class="pd-related-title"><BaseCmsLabel code="related_products" /></h2>
				</template>
			</CatalogSpeciallists>
		</BaseCatalogProductsWidget>

		<BasePublishPostsWidget :fetch="{category_code: item.category_code, id_exclude: item.id, limit: 3}" v-slot="{items}">
			<PublishSpecial v-if="items?.length" :items="items" />
		</BasePublishPostsWidget>
	</BasePublishDetail>
</template>

<style lang="less" scoped>
	@media (min-width: calc(@m + 1px)){
		:deep(.bc-items){justify-content: center;}
	}
	h1{
		text-align: center; padding: 10px 0 32px;
		@media (max-width: @m){text-align: left; padding: 0 0 20px;}
	}
	.pd-wrapper{max-width: 776px;}
	.pd-head{
		padding: 56px 0 0;
		@media (max-width: @t){padding: 40px 0 0;}
		@media (max-width: @m){padding-top: 24px;}
	}
	.pd-hero-image{
		@media (max-width: @m){padding: 0;}
		:deep(img){
			display: block; border-radius: var(--inputBorderRadius); margin: auto;
			@media (max-width: @m){border-radius: 0}
		}
	}
	.pd-main{
		padding-bottom: 88px;
		@media (max-width: @m){padding-bottom: 48px;}
	}
	.pd-info{
		display: flex; justify-content: space-between; align-items: center; padding: 30px 0 24px;
		@media (max-width: @m){padding: 14px 0;}
	}
	.pd-btn-all{
		text-underline-offset: 3px; padding: 0 0 0 24px; font-weight: bold; font-size: 14px; position: relative;
		@media (max-width: @m){font-size: 12px;}
		&:before{
			position: absolute; .icon-arrow4(); font: 6px/6px var(--fonti); color: var(--colorBaseBlue); left: 0px; top: 8px;
			@media (max-width: @m){top: 6px;}
		}
	}
	.pd-date{
		font-size: 14px; font-weight: bold;
		@media (max-width: @m){font-size: 12px;}
	}
	.pd-related-title{
		padding: 0 0 32px;
		@media (max-width: @m){padding-bottom: 16px; font-size: 19px;}
	}
	.speciallist-widget{
		border-top: 1px solid var(--colorBorderLightForms); padding: 80px 0 52px;
		@media (max-width: @t){padding: 64px 0 36px;}
		@media (max-width: @m){border: 0; padding: 0 0 16px;}
	}
</style>
