<template>
	<BasePublishCategory v-slot="{item: category, rootCategory}" :root-category="true" :include-subcategories="true" :seo="true">
		<BasePublishPosts :featured-posts="category.level == 1 ? 5 : 0" v-slot="{items: posts, nextPage, loadMore, featuredPosts, loading}" :infinite-scroll="2">
			<PublishFeatured v-if="category.level == 1 && featuredPosts" :items="featuredPosts" :root-category="rootCategory">
				<CmsBreadcrumbs v-if="category?.breadcrumbs" :items="category.breadcrumbs" />
				<h1 v-html="category.seo_h1"></h1>
			</PublishFeatured>

			<div v-else class="p-header">
				<div class="wrapper p-header-wrapper">
					<div class="p-header-left">
						<CmsBreadcrumbs v-if="category?.breadcrumbs" :items="category.breadcrumbs" />
						<h1 v-html="category.seo_h1"></h1>
					</div>
					<div class="p-header-right">
						<PublishSubcategories :category="category" :root-category="rootCategory" />
					</div>
				</div>
			</div>
			<div class="wrapper p-wrapper">
				<div class="p-items" v-if="posts?.length">
					<PublishIndexEntry v-for="post in posts" :key="post.id" :item="post" :with-category="category.level == 1 && true" />
				</div>
				<div v-else class="p-empty"><BaseCmsLabel code="no_publish" /></div>
				<ClientOnly>
					<div class="load-more-container" v-if="nextPage" data-posts-scroll-trigger>
						<button type="button" class="btn load-more btn-load-more" :class="{'loading': loading}" @click="loadMore"><UiLoader v-if="loading" /><BaseCmsLabel code="load_more_articles" /></button>
					</div>
				</ClientOnly>
				<BaseUiPagination class="pagination" />
			</div>
		</BasePublishPosts>
	</BasePublishCategory>
</template>

<style lang="less" scoped>
	h1{
		padding-bottom: 0;
		@media (max-width: @m){padding-bottom: 22px;}
		:deep(span){color: var(--colorBaseBlue);}
	}
	.p-header{
		padding: 56px 0 32px;
		@media (max-width: @t){padding-top: 40px;}
		@media (max-width: @m){padding: 24px 0 0;}
	}
	.p-header-wrapper{
		display: flex; align-items: flex-end; justify-content: space-between;
		@media (max-width: @m){display: block;}
	}
	.p-wrapper{
		padding-bottom: 88px;
		@media (max-width: @m){padding-bottom: 40px;}
	}
	.p-items{
		display: flex; flex-wrap: wrap; margin: 0 -20px 0;
		@media (max-width: @m){display: block; margin: 0;}
	}
	.load-more-container{
		display: flex; justify-content: center;
		@media (max-width: @ms){display: block; padding-top: 16px;}
	}
	.p-empty{
		@media (max-width: @m){text-align: center;}
	}
	@media (max-width: @m){
		.btn-load-more{width: 100%;}
	}
</style>
