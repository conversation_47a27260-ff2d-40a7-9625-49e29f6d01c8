<template>
	<BaseCmsPage v-slot="{page}">
		<ClientOnly>
			<BaseWebshopCart v-slot="{counter, parcels, urls, onFinishShopping}" :gtm-tracking="true">
				<div class="w-cnt">
					<div class="wrapper w-row" v-if="parcels?.length">
						<div class="w-col1" :class="{'no-free-shipping-suggestions': !freeShippingProducts?.length}">
							<div class="w-col1-inner">
								<h1 v-if="page?.seo_h1">
									{{ page.seo_h1 }} <span class="counter" v-if="counter">{{ counter }}</span>
								</h1>

								<BaseWebshopCartErrors v-slot="{errorsItems, warningsItems}">
									<template v-if="errorsItems?.length">
										<div class="global-error" v-for="error in errorsItems" :key="error"><BaseCmsLabel :code="error.label_name" /></div>
									</template>
									<template v-if="warningsItems?.length">
										<div class="global-warning" v-for="warning in warningsItems" :key="warning"><BaseCmsLabel :code="warning.label_name" /></div>
									</template>
								</BaseWebshopCartErrors>

								<div class="w-cart-cnt" v-if="parcels[0]?.items?.length">
									<BaseWebshopFreeShipping v-slot="{item: toFree}">
										<WebshopFreeShipping v-if="toFree?.amount > 0" :item="toFree" />

										<div class="w-cart-items">
											<WebshopCartItem v-for="item in parcels[0].items" :data="item" :key="item.shopping_cart_code" />
										</div>

										<BaseCatalogProductsWidget v-model="freeShippingProducts" v-if="toFree?.amount > 0" :fetch="{list_code: 'free_shipping', sort: 'list_position', only_available: true, limit: 15, only_with_image: true}" v-slot="{items}">
											<CatalogSpeciallists class="w-fsp-cnt" :items="items" :per-page="3" v-if="items.length" options="withAddToCart">
												<template #header>
													<h4 class="w-fsp-title" v-html="labels.get('free_shipping_missing_headline').replace('%VALUE%', formatCurrency(toFree.amount))"></h4>
												</template>
											</CatalogSpeciallists>
										</BaseCatalogProductsWidget>
									</BaseWebshopFreeShipping>

									<BaseFaqQuestions :fetch="{category_position: '05'}" v-slot="{items}">
										<div v-if="items?.length" class="w-faq">
											<h4 class="w-faq-title"><BaseCmsLabel code="faq_title" /></h4>
											<div class="w-faq-items">
												<CmsFaqItem v-for="faq_item in items" :key="faq_item.id" :item="faq_item" />
											</div>
											<CmsFaqInfo />
										</div>
									</BaseFaqQuestions>
								</div>
								<div class="w-empty-cart" v-else>
									<BaseCmsLabel code="empty_shopping_cart" v-interpolation tag="div" />
								</div>
							</div>
						</div>

						<div class="w-col2">
							<div class="w-col2-inner" v-if="parcels[0].items?.length">
								<h4 class="w-coupons-title"><BaseCmsLabel code="cart_coupons_title" /></h4>
								<WebshopCouponForm mode="webshop" />

								<h4 class="w-total-title"><BaseCmsLabel code="total_in_cart" /></h4>

								<WebshopTotal />
								<BaseWebshopCartErrors v-slot="{errors, warnings}">
									<NuxtLink @click="onFinishShopping" v-if="urls.webshop_customer && !errors && !warnings" :to="urls.webshop_customer" class="btn btn-green w-finish-shopping"><BaseCmsLabel code="finish_shopping" /></NuxtLink>
								</BaseWebshopCartErrors>
							</div>
						</div>
					</div>
				</div>
				<div class="w-mobile-fixed" v-if="parcels[0]?.items?.length">
					<BaseWebshopTotal v-slot="{items}">
						<div class="w-total-fixed" v-if="items?.total">
							<div class="w-total-fixed-label"><BaseCmsLabel code="total_to_pay" />:</div>
							<div class="w-total-fixed-value"><BaseUtilsFormatCurrency :price="items.total" /></div>
						</div>
						<BaseWebshopCartErrors v-slot="{errors, warnings}">
							<NuxtLink v-if="urls.webshop_customer && !errors && !warnings" :to="urls.webshop_customer" class="btn btn-green w-finish-shopping w-finish-shopping-fixed"><BaseCmsLabel code="finish_shopping" /></NuxtLink>
						</BaseWebshopCartErrors>
					</BaseWebshopTotal>
				</div>
			</BaseWebshopCart>
			<template #fallback>
				<BaseThemeUiLoading />
			</template>
		</ClientOnly>
	</BaseCmsPage>
</template>

<script setup>
	const labels = useLabels();
	const {formatCurrency} = useCurrency();
	const {insertAfter, appendTo, onMediaQuery} = useDom();
	const freeShippingProducts = ref(null);

	onMediaQuery({
		query: '(max-width: 980px)',
		enter: () => {
			insertAfter('.w-faq', '.w-col2');
		},
	});
</script>

<style lang="less" scoped>
	.w-cnt{
		background: var(--colorLightBlueBackground);
		@media (max-width: @m){background: #fff;}
	}
	.w-row{
		display: flex;
		@media (max-width: @m){display: block; padding: 0;}
	}
	h1{
		font-size: var(--fontSizeH2);
		@media (max-width: @t){font-size: 28px; padding-bottom: 24px;}
		@media (max-width: @m){font-size: 26px; padding: 0 16px 15px;}
		.counter{
			color: var(--colorBaseBlue); font-size: 20px;
			@media (max-width: @m){font-size: 15px;}
		}
	}
	.w-col1{
	    flex-grow: 1; padding: 56px 167px 80px 0; position: relative;
		@media (max-width: @t){padding: 40px 114px 80px 0;}
		@media (max-width: @m){
			padding: 24px 0 48px;
			&.no-free-shipping-suggestions{padding-bottom: 0;}
		}
	    &:before {
			.pseudo(auto,auto); top: 0; left: -50vw; right: 0; bottom: 0; background: #fff;
			@media (max-width: @m){display: none;}
		}
	}
	.w-col1-inner{position: relative;}
	.w-col2{
		width: 380px; flex-grow: 0; flex-shrink: 0; padding-bottom: 50px;
		@media (max-width: @t){width: 290px;}
		@media (max-width: @m){width: 100%; background: var(--colorLightBlueBackground); padding-bottom: 0;}
	}
	.w-col2-inner{
		background: #fff; border-radius: var(--inputBorderRadius); box-shadow: 0 0 40px 0 rgba(0,89,194,0.1); padding: 40px; position: relative; margin: 118px 0 0 -40px;
		@media (max-width: @t){padding: 32px; margin: 98px 0 0 -66px;}
		@media (max-width: @m){margin: 0; box-shadow: none; background: 0; padding: 32px 16px 40px;}
	}
	.w-total-title{
		font-size: 24px; padding: 0 0 10px;
		@media (max-width: @m){font-size: 19px;}
	}
	.w-coupons-title{
		font-size: 17px; padding: 0 0 12px;
		@media (min-width: @m){display: none;}
	}
	.w-fsp-cnt{
		max-width: 730px;
		@media (max-width: @t){max-width: 540px;}
	}
	.w-fsp-title{
		font-size: 24px; line-height: 1.3; padding: 64px 0 24px;
		:deep(strong){color: var(--colorBaseBlue);}
		@media (max-width: @t){max-width: 450px;}
		@media (max-width: @m){font-size: 17px; padding: 48px 0 16px; max-width: none;}
	}
	:deep(.speciallist-wrapper){
		padding: 0;
		@media (max-width: @m){padding: 0 16px;}
	}
	.w-faq-title{
		padding: 44px 0 16px; border-bottom: 1px solid var(--colorBorderLightForms);
		@media (max-width: @t){font-size: 24px; padding-top: 64px;}
		@media (max-width: @m){padding: 40px 16px 10px; font-size: 19px; color: var(--colorBaseBlue); }
	}
	.faq-info{
		@media (max-width: @t){
			display: grid; grid-template-columns: repeat(2, 1fr); grid-template-rows: repeat(2, 1fr); padding: 30px 29px;
			:deep(span){ grid-area: 1 / 1 / 3 / 2; margin-right: 0;}
			:deep(a){text-align: right; margin-right: 0;}
		}
		@media (max-width: @m){display: none;}
	}
	.w-finish-shopping{width: 100%; margin: 14px 0 0;}
	.w-empty-cart{
		@media (max-width: @m){padding: 0 16px 16px;}
	}
	.w-mobile-fixed{
		display: none; box-shadow: 0 -3px 10px 0 rgba(0,12,26,0.13); position: fixed; bottom: 0; left: 0; right: 0; z-index: 100; background: #fff; border-radius: 4px 4px 0 0; padding: 12px 16px; align-items: center; justify-content: space-between; z-index: 200;
		@media (max-width: @m){display: flex;}
	}
	.w-total-fixed{font-size: 15px; line-height: 1.3; flex: 1 0 auto;}
	.w-total-fixed-value{font-weight: bold;}
	.w-finish-shopping-fixed{margin: 0; padding: 10px 0 9px; flex: 0 0 176px; box-shadow: none;}
</style>

<style lang="less">
	.page-webshop-shopping_cart .to-top{
		@media (max-width: @m){display: none;}
	}
</style>
