<template>
	<BaseCmsPage>
		<WebshopCheckoutLayout>
			<template #header>
				<WebshopCheckoutHeader :step="2" />
			</template>
			<template #wcCol2>
				<div class="step-form-cnt step-form1-cnt">
					<WebshopStep :step="1" :title="true" />
					<BaseWebshopCustomerForm class="form step1-form form-animated-label" v-slot="{loading, fields, errors}" data-autofocus>
						<div class="step1-form-inner">
							<template v-if="Object.keys(errors)?.length">
								<BaseCmsLabel tag="div" class="global-error" code="form_validation_error" data-scroll-to-error />
							</template>
							<BaseFormField v-for="field in fields" :key="field.name" :item="field" v-slot="{errorMessage, floatingLabel, isTouched, value}">
								<p class="field" :class="['field-' + field.name, {'ffl-floated': floatingLabel}, {'err': errorMessage}, {'success': !errorMessage && isTouched && value}]">
									<BaseFormInput :readonly="loading" />
									<label :for="field.name"><BaseCmsLabel :code="field.name" /></label>
									<div v-if="['location', 'b_location'].includes(field.name)" class="field-tooltip"><BaseCmsLabel code="location_tooltip" /></div>
									<span class="error" v-show="errorMessage" v-html="errorMessage" />
								</p>
							</BaseFormField>
						</div>

						<button class="btn btn-green" type="submit" :class="{'loading': loading}" :disabled="loading"><UiLoader v-if="loading" /><BaseCmsLabel code="next_step" /></button>
					</BaseWebshopCustomerForm>
				</div>

				<WebshopStep :step="2" />
				<WebshopStep :step="3" />
				<WebshopStep :step="4" />
			</template>
		</WebshopCheckoutLayout>
	</BaseCmsPage>
</template>

<style scoped lang="less">
	.step-form-cnt{
		padding-bottom: 40px;
		@media(max-width: @m){padding-bottom: 32px;}
	}
	.field-zipcode,.field-city,.field-b_zipcode,.field-b_city{display: none!important;}
	.field-group.active{
		.field-b_zipcode,.field-b_city{display: inline-block;}
	}
	.field-group-shipping,.field-group-r1{
		@media(max-width: @m){
			input[type=checkbox] + label, input[type=radio] + label{
				font-size: 13px; line-height: 19px;
				&:before{width: 22px; height: 22px; line-height: 22px;}
			}
		}
	}
	.btn-green{
		margin-top: 5px;
		@media(max-width: @m){width: 100%;}
	}
	.wc-col2-cnt{padding-bottom: 0 !important;}
</style>
