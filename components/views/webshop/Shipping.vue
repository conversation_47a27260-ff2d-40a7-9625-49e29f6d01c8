<template>
	<BaseCmsPage>
		<WebshopCheckoutLayout>
			<template #header>
				<WebshopCheckoutHeader :step="3" />
			</template>
			<template #wcCol2>
				<BaseWebshopCheckout>
					<WebshopStep :step="1" :completed="true" />
					<div class="step-form-cnt step-form2-cnt">
						<WebshopStep :step="2" :title="true" />
						<BaseWebshopShippingForm class="form step2-form form-animated-label" v-slot="{loading, fields, onShippingUpdate, status, activeShipping}">
							<div class="step2-form-inner">
								<div v-if="status?.success && status?.data?.label">
									<div class="global-success"><BaseCmsLabel :code="status.data.label" /></div>
								</div>
								<div v-if="status?.data?.errors">
									<div class="global-error" v-for="error in status.data.errors" :key="error">{{ error.error }}</div>
								</div>
								<p v-for="field in fields" :key="field.value">
									<BaseFormField :item="field" v-slot="{errorMessage}">
										<BaseFormInput :checked="field.selected" :id="field.code" @click="onShippingUpdate(field)" />
										<label :for="field.code">
											{{ field.title }}
											<div v-show="field.description" class="shipping-info">{{ field.description }}</div>
										</label>
										<span class="error" v-show="errorMessage" v-html="errorMessage" />
										<!-- Pickup locations select menu -->
										<BaseWebshopPickupLocations v-slot="{fields: locationFields, selectedLocation, onSelectLocation}" v-if="fields?.length && field.widget == 'location' && activeShipping?.id == field.id">
											<div class="personal-pickup">
												<BaseFormField :item="locationFields[0]" v-slot="{errorMessage}">
													<BaseFormInput type="select" @change="onSelectLocation($event), onShippingUpdate(field)" :value="selectedLocation?.id ? selectedLocation.id : ''">
														<option v-for="field in locationFields" :key="field.name" :value="field.value">{{ field.title }}</option>
													</BaseFormInput>
													<span class="error" v-show="errorMessage" v-html="errorMessage" />
												</BaseFormField>
												<div class="personal-pickup-content" v-if="selectedLocation">
													<div class="title">{{selectedLocation.title}}</div>
													<div class="title" v-if="selectedLocation.address" v-html="selectedLocation.address" />
													<div class="title" v-if="selectedLocation.contact" v-html="selectedLocation.contact" />
													<div class="title" v-if="selectedLocation.business_hour" v-html="selectedLocation.business_hour" />
												</div>
												<BaseCmsLabel tag="div" code="personal_pickup_label" class="personal-pickup-label" />
											</div>
										</BaseWebshopPickupLocations>
									</BaseFormField>
								</p>
							</div>
							<button class="btn btn-green" type="submit" :class="{'loading': loading}" :disabled="loading"><UiLoader v-if="loading" /><BaseCmsLabel code="next_step" /></button>
						</BaseWebshopShippingForm>
					</div>

					<WebshopStep :step="3" />
					<WebshopStep :step="4" />
				</BaseWebshopCheckout>
			</template>
		</WebshopCheckoutLayout>
	</BaseCmsPage>
</template>

<style scoped lang="less">
	.step-form-cnt{
		padding: 17px 0 40px;
		@media(max-width: @m){padding: 20px 0 32px;}
	}
	:deep(.step2-form-inner){
		padding-left: 44px; padding-bottom: 6px;
		@media(max-width: @m){padding-left: 0;}
	}
	.btn-green{
		@media(max-width: @m){width: 100%;}
	}
	input[type=radio]:checked + label{
		font-weight: normal;
		.shipping-info{display: block;}
	}
	:deep(.global-success,.global-error){display: none;}
	.shipping-info{padding: 8px 0 0 0; font-size: 14px; line-height: 22px; display: none;}
	.personal-pickup{
		padding: 8px 25px 0 32px;
		@media(max-width: @m){
			padding: 6px 0 0 32px;
			:deep(select){height: 48px; font-size: 15px; background-size: 10px; background-position: right 16px center;}
		}
	}
	.personal-pickup-content{
		font-size: 14px; line-height: 22px; padding-left: 17px; padding-top: 8px;
		@media(max-width: @m){font-size: 13px; line-height: 1.5;}
		:deep(a){color: var(--colorBase); text-decoration-color: var(--colorBaseBlue);}
	}
	.personal-pickup-label{
		font-size: 14px; line-height: 1.5; padding: 13px 20px 11px; background: var(--colorLightBlueBackground); border-radius: var(--borderRadius); margin-top: 8px; margin-bottom: 24px;
		@media (max-width: @m){font-size: 13px; padding: 10px 17px; margin-bottom: 8px;}
	}
</style>
