<template>
	<BaseCmsPage>
		<WebshopCheckoutLayout>
			<template #header>
				<WebshopCheckoutHeader :step="5" />
			</template>
			<template #wcCol2>
				<BaseWebshopCheckout v-slot="{urls}">
					<WebshopStep :step="1" :completed="true" />
					<WebshopStep :step="2" :completed="true" />
					<WebshopStep :step="3" :completed="true" />
					<div class="step-form-cnt step-form4-cnt">
						<WebshopStep :step="4" :title="true" />
						<BaseWebshopReviewOrderForm v-slot="{fields, customer, total, shipping, payment, meta, loading, orderErrors}">
							<div class="customer-details">
								<div class="customer-detail customer-detail-info" v-if="customer">
									<h2>
										<BaseCmsLabel code="step1" /> <NuxtLink :to="urls.webshop_customer"><BaseCmsLabel code="edit2" tag="span" class="edit" /></NuxtLink>
									</h2>
									<div>
										<p>{{ customer.first_name }} {{ customer.last_name }}</p>
										<p>
											{{ customer.address.street }}, {{ customer.address.zipcode }} {{ customer.address.city }}<span v-if="customer?.address?.country_name">, {{ customer.address.country_name }}</span>
										</p>
										<p v-if="customer?.phone">{{ customer.phone }}</p>
										<p v-if="customer?.email">{{ customer.email }}</p>
										<div class="global-error" v-if="orderErrors?.customer && !loading"><BaseCmsLabel :code="orderErrors.customer" /></div>
									</div>
								</div>

								<div class="customer-detail customer-detail-shipping" v-if="customer">
									<h2>
										<BaseCmsLabel code="shipping_details" /> <NuxtLink :to="urls.webshop_customer"><BaseCmsLabel code="edit2" tag="span" class="edit" /></NuxtLink>
									</h2>
									<div v-if="customer?.b_first_name">
										<p>{{ customer.b_first_name }} {{ customer.b_last_name }}</p>
										<p>
											{{ customer.b_address.b_street }}, {{ customer.b_address.b_zipcode }} {{ customer.b_address.b_city }}<span v-if="customer?.b_address?.b_country_name">, {{ customer.b_address.b_country_name }}</span>
										</p>
										<p v-if="customer?.b_phone">{{ customer.b_phone }}</p>
									</div>
									<div v-else>
										<BaseCmsLabel code="same_as_shipping" tag="p" />
									</div>
								</div>

								<div class="customer-detail customer-detail-b" v-if="customer">
									<h2 v-if="customer?.b_company_oib">
										<BaseCmsLabel code="b_r1" /> <NuxtLink :to="urls.webshop_customer"><BaseCmsLabel code="edit2" tag="span" class="edit" /></NuxtLink>
									</h2>
									<div v-if="customer?.b_company_oib">
										<p>{{ customer.b_company_name }}</p>
										<p>{{ customer.b_company_address }}</p>
										<p>{{ customer.b_company_oib }}</p>
									</div>
								</div>

								<div class="customer-detail customer-detail-delivery">
									<h2>
										<BaseCmsLabel code="shipping" /> <NuxtLink :to="urls.webshop_shipping"><BaseCmsLabel code="edit2" tag="span" class="edit" /></NuxtLink>
									</h2>
									<p>
										{{ shipping?.title }}
										<span v-if="shipping?.pickup_location"> ({{shipping.pickup_location.title}})</span>
									</p>
									<div class="global-error" v-if="orderErrors?.shipping && !loading"><BaseCmsLabel :code="orderErrors.shipping" /></div>
								</div>

								<div class="customer-detail customer-detail-payment">
									<h2>
										<BaseCmsLabel code="payment" /><NuxtLink :to="urls.webshop_payment"><BaseCmsLabel code="edit2" tag="span" class="edit" /></NuxtLink>
									</h2>
									<p>{{ payment?.title }}</p>
									<div class="global-error" v-if="orderErrors?.payment && !loading"><BaseCmsLabel :code="orderErrors.payment" /></div>
								</div>

								<div class="customer-detail customer-detail-total" v-if="total">
									<BaseCmsLabel code="total_to_pay3" tag="h2" />
									<p><BaseUtilsFormatCurrency :price="total.total" /></p>
								</div>
							</div>

							<template v-if="fields">
								<div class="alert" v-show="acceptTermsAlert"><BaseCmsLabel code="webshop_alert_terms" /></div>
								<div class="accept-terms-container">
									<p v-for="field in fields" :key="field.name" v-interpolation>
										<BaseFormField :item="field" v-slot="{errorMessage}">
											<BaseFormInput @click="checkAlert" />
											<template v-if="field.name == 'accept_terms'">
												<BaseCmsLabel tag="label" class="accept-terms2" :for="field.name" code="accept_terms_webshop" />
											</template>
											<template v-else>
												<BaseCmsLabel tag="label" :for="field.name" :code="field.name" />
											</template>
											<span class="error" v-show="errorMessage" v-html="errorMessage" />
										</BaseFormField>
									</p>
								</div>
							</template>

							<button class="btn btn-green finish-order" :class="[{'accepted': !acceptTermsAlert},{'loading': loading}]" type="submit" :disabled="!meta?.valid || loading" v-if="!orderErrors">
								<UiLoader v-if="loading" />
								<BaseCmsLabel code="finish_shopping2" />
							</button>
						</BaseWebshopReviewOrderForm>
					</div>
				</BaseWebshopCheckout>
			</template>
		</WebshopCheckoutLayout>
	</BaseCmsPage>
</template>

<script setup>
	const acceptTerms = ref(false);
	const acceptTerms2 = ref(false);
	const finishOrderBtn = ref(false);
	function checkAlert(e) {
		if (e.target.name == 'accept_terms') acceptTerms.value = e.target.checked;
		if (e.target.name == 'accept_terms_2') acceptTerms2.value = e.target.checked;
	}

	// show/hide alert
	const acceptTermsAlert = computed(() => {
		return acceptTerms.value && acceptTerms2.value ? false : true;
	});

	// show/hide alert on page load
	onMounted(() => {
		const acceptTermsCheckbox = document.querySelector('[name="accept_terms"]');
		if (acceptTermsCheckbox) acceptTerms.value = acceptTermsCheckbox?.checked;

		const acceptTerms2Checkbox = document.querySelector('[name="accept_terms_2"]');
		if (acceptTerms2Checkbox) acceptTerms2.value = acceptTerms2Checkbox?.checked;
	});
</script>

<style scoped lang="less">
	.step-form4-cnt{
		padding: 16px 0 0 0;
		@media(max-width: @m){
			padding: 20px 0 32px; position: relative;
			&:after{.pseudo(auto,1px); left: -16px; right: -16px; background: var(--colorBorderLightForms); bottom: 0;}
			:deep(input[type=checkbox] + label, input[type=radio] + label){
				font-size: 13px;
				&:before{width: 22px; height: 22px; line-height: 22px; font-size: 12px;}
			}
		}
	}
	.customer-details{
		:deep(.customer-detail){
			margin-bottom: 10px;
			&:last-child{margin-bottom: 0;}
		}
		@media(max-width: @m){
			:deep(.customer-detail){margin-bottom: 12px;}
		}
	}
	.customer-detail{
		h2{padding-top: 0; padding-bottom: 2px; font-size: 14px; line-height: 1.5;}
		p{padding-bottom: 3px; font-size: 14px; line-height: 1.5;}
		@media(max-width: @m){
			h2{font-size: 15px;}
			p{font-size: 15px;}
		}
	}
	.edit{
		margin-left: 4px; font-weight: normal; text-decoration: underline; text-underline-offset: 3px; .transition(text-decoration-color);
		&:hover{text-decoration-color: transparent;}
		@media(max-width: @m){font-size: 13px;}
	}
	.alert{
		background: var(--colorYellow); border-radius: 3px; padding: 12px 15px; font-weight: bold; font-size: 12px; line-height: 15px; margin-top: 8px; position: relative;
		&:before{.pseudo(8px,8px); left: 8px; bottom: -3px; background: var(--colorYellow); transform: rotate(45deg);}
	}
	.accept-terms-container{
		padding-top: 18px; padding-bottom: 7px;
		:deep(p){padding-bottom: 15px;}
	}
	.accept-terms2{
		:deep(a){
			font-weight: bold; text-underline-offset: 4px; text-decoration-color: var(--colorBaseBlue); color: var(--colorBase); transition: color .3s, text-decoration-color .3s;
			&:hover{text-decoration-color: transparent; color: var(--colorBaseBlue);}
		}
	}
	.error{font-size: 11px; line-height: 15px; padding-left: 32px;}
	input[type=checkbox] + label, input[type=radio] + label{padding: 2px 0 4px 32px;}
	.finish-order{
		pointer-events: none; opacity: .5;
		&.accepted{opacity: 1; pointer-events: unset;}
		@media(max-width: @m){width: 100%;}
	}
</style>
