<template>
	<BaseCmsPage v-slot="{page}">
		<WebshopCheckoutLayout>
			<template #header>
				<WebshopCheckoutHeader :step="1" />
			</template>
			<template #wcCol2>
				<BaseWebshopCheckout>
					<BaseCmsLabel class="gc-title" code="guest_checkout_title" tag="h4" />
					<div class="wc-cnt" v-html="page?.content" />

					<BaseWebshopGuestForm class="form form-animated-label login-form guest-login" v-slot="{loading, fields}">
						<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage, floatingLabel}">
							<p class="field" :class="['field-' + item.name, {'ffl-floated': floatingLabel}]">
								<BaseFormInput id="guest-email" />
								<BaseCmsLabel tag="label" for="guest-email" :code="item.name" />
								<span class="error" v-show="errorMessage" v-html="errorMessage" />
							</p>
						</BaseFormField>
						<button type="submit" :class="{'loading': loading}"><UiLoader v-if="loading" /><BaseCmsLabel code="continue_without_signup" /></button>
					</BaseWebshopGuestForm>
				</BaseWebshopCheckout>

				<AuthSocialLogin mode="checkout" />
				<AuthLoginForm mode="checkout" />
			</template>
		</WebshopCheckoutLayout>
	</BaseCmsPage>
</template>

<style scoped lang="less">
	.gc-title{padding-top: 0; padding-bottom: 8px;}
	.guest-login{
		button{
			margin-top: 6px;
			@media (max-width: @m){width: 100%; margin-top: 0;}
		}
	}
</style>
