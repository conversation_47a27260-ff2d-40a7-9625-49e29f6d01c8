<template>
	<Body class="page-thankyou" />
	<BaseCmsPage v-slot="{page}">
		<ClientOnly>
			<div class="thankyou-page">
				<BaseWebshopCheckout v-slot="{user}">
					<BaseWebshopOrder v-slot="{order, payment, shipping, pickupLocation, termsPdf, invoicePdf, paymentTransferUrl}" @load="onLoad">
						<template v-if="order">
							<div class="thankyou-page-intro">
								<div class="wrapper">
									<h1>{{ order?.customer?.first_name }}, <BaseCmsLabel code="thank_you_for_ordering" /></h1>
									<div v-if="page?.content" v-html="page.content" />

									<!-- PDF predugovorne obavijesti -->
									<a :href="termsPdf" target="_blank" v-if="termsPdf" class="btn btn-termspdf">
										<BaseCmsLabel code="thank_you_download_terms_pdf" tag="span" />
									</a>

									<!-- Odabrana dostava -->
									<div class="thankyou-selected">
										<h3><BaseCmsLabel code="thank_you_selected" />:</h3>
										<p v-if="shipping">
											<span class="title"><BaseCmsLabel code="shipping" /></span>
											{{ shipping.title }}
											<template v-if="shipping.widget == 'location' && pickupLocation">
												<span>{{pickupLocation.title}}</span>
												<div class="thank-you-pickup">
													<div v-if="pickupLocation.address" v-html="pickupLocation.address" />
													<div v-if="pickupLocation.contact" v-html="pickupLocation.contact" />
													<div v-if="pickupLocation.business_hour" v-html="pickupLocation.business_hour" />
												</div>
											</template>
										</p>

										<!-- Odabrano plaćanje -->
										<p v-if="payment">
											<span class="title"><BaseCmsLabel code="payment" /></span>
											{{ payment.title }}
										</p>
									</div>

									<div class="thank-you-invoice" v-if="invoicePdf">
										<a :href="invoicePdf" target="_blank" class="btn btn-green"><BaseCmsLabel code="order_download_invoice" /></a>
									</div>
								</div>
							</div>

							<div class="thankyou-page-bill" v-if="order?.cart?.payment_transfer_data">
								<div class="wrapper">
									<BaseCmsLabel code="thankyou_bill_payment_info" tag="div" class="thankyou-page-bill-info" />
									<div class="table-payment-transfer">
										<BaseWebshopPaymentTransfer :data="order.cart.payment_transfer_data" />
									</div>
									<a :href="paymentTransferUrl+'&mode=pdf'" target="_blank" class="btn btn-print"><BaseCmsLabel code="print_payment" /></a>
								</div>
							</div>

							<div class="quick-signup" v-if="!user">
								<BaseAuthQuickSignupForm class="form-animated-label" v-slot="{fields, status, loading}">
									<div class="quick-signup-title"><BaseCmsLabel code="thank_you_quick_registration" /></div>
									<div class="quick-signup-desc"><BaseCmsLabel code="thank_you_quick_reg_info" tag="div" /></div>
									<template v-if="!status?.success">
										<div class="quick-signup-fields" v-interpolation>
											<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage, floatingLabel, isTouched, value}">
												<p class="field" :class="['field-' + item.name, {'ffl-floated': floatingLabel}, {'err': errorMessage}, {'success': !errorMessage && isTouched && value}]">
													<BaseFormInput :id="'quicksignup-' + item.name" />
													<BaseCmsLabel tag="label" :for="'quicksignup-' + item.name" :code="item.name" />
													<span class="error" v-show="errorMessage" v-html="errorMessage" />
												</p>
											</BaseFormField>
										</div>
										<button type="submit" class="btn-green quick-signup-submit" :class="{'loading': loading}" :disabled="loading"><UiLoader v-if="loading" /><BaseCmsLabel code="form_login" /></button>
									</template>

									<!-- Uspješna registracija -->
									<div v-if="status?.success && status?.data.label_name" class="global-success"><BaseCmsLabel :code="status.data.label_name" /></div>
								</BaseAuthQuickSignupForm>
							</div>
						</template>
					</BaseWebshopOrder>
				</BaseWebshopCheckout>
			</div>
			<template #fallback>
				<BaseThemeUiLoading />
			</template>
		</ClientOnly>
	</BaseCmsPage>
</template>

<script setup>
	const {waitForWindowProperty} = useMeta();
	function onLoad(data) {
		// Custom remarketing event
		waitForWindowProperty('gtag', () => {
			let items = [];

			if(data?.parcels[0]?.items) {
				items = data.parcels[0].items.map(item => {
					return {
						'id': item.id,
						'location_id': 'conversion',
						'google_business_vertical': 'custom'
					}
				})
			}

			gtag('event', 'page_view', {
				'send_to': 'AW-***********',
				'value': data?.total?.total || 0,
				'items': items
			});
		});
	}
</script>

<style lang="less" scoped>
	.page-thankyou{
		@media(max-width: @m){
			.header-categories{display: none;}
		}
	}
	.thankyou-page{
		h1{font-size: 24px; line-height: 1.5; padding-top: 0; padding-bottom: 8px;}
		.wrapper{max-width: 620px;}
		@media(max-width: @m){
			h1{font-size: 17px;}
		}
	}
	.thankyou-page-intro{
		background: var(--colorLightBlueBackground); padding: 48px 0 46px; text-align: center;
		:deep(p){padding-bottom: 10px;}
		.wrapper{padding: 0;}
		@media(max-width: @m){
			padding: 24px 0; background: unset; text-align: left;
			.wrapper{padding: 0 16px;}
		}
	}
	.thankyou-selected{
		padding-top: 24px;
		:deep(h3){padding-top: 24px; font-size: 20px; line-height: 1.5; padding-top: 0;}
		:deep(p){padding-bottom: 16px; font-size: 14px;}
		.title{display: block; font-size: 16px; font-weight: bold;}
		@media(max-width: @m){
			:deep(h3){font-size: 17px; padding-bottom: 5px;}
			.title{font-size: 15px; line-height: 1.5;}
			:deep(p){padding-bottom: 10px; font-size: 13px; line-height: 1.5;}
		}
	}
	.thank-you-invoice{padding: 10px 0 0;}
	:deep(.thank-you-pickup) p{padding: 0 0 5px;}
	.thankyou-page-bill{
		padding: 32px 0 64px;
		.wrapper{max-width: 580px; display: flex; flex-flow: column; align-items: center; padding: 0;}
		@media(max-width: @m){
			max-width: unset; padding: 0 0 32px;
			.wrapper{padding: 0 16px; max-width: none;}
		}

	}
	.thankyou-page-bill-info{
		font-size: 12px; line-height: 1.5; padding-left: 32px; position: relative; margin-bottom: 30px;
		&:before{.pseudo(24px,24px); background: url('/assets/images/custom-icons/info2.svg') center no-repeat; left: 0; top: -3px}
		@media(max-width: @m){
			padding-left: 22px; margin-bottom: 10px;
			&:before{width: 16px; height: 16px; background-size: 16px; top: 0;}
		}
	}
	.btn-termspdf{
		margin-top: 6px;
		:deep(span){
			position: relative; padding-left: 30px;
			&:before{.pseudo(auto,auto); .icon-pdf; font: 21px/21px var(--fonti); color: var(--colorWhite); left: 0;}
		}
	}
	.table-payment-transfer{
		margin: 0 0 35px;
		@media (max-width: @m){max-width: 100%; overflow: auto; margin: 0 0 20px;}
	}
	.mail-payment-transfer{
		@media(max-width: @m){width: 100%; overflow: auto;}
	}
	.quick-signup{
		max-width: 500px; margin: auto; padding: 40px 0 70px;
		@media (max-width: @m){padding: 0 15px 40px;}
	}
	.quick-signup-title{
		font-size: 24px; line-height: 1.4; font-weight: bold; padding: 0 0 10px; text-align: center;
		@media (max-width: @m){font-size: 17px; text-align: left; padding: 0 0 6px;}
	}
	.quick-signup-desc{
		padding: 0 0 10px; text-align: center;
		@media (max-width: @m){text-align: left; font-size: 14px;}
	}
	.quick-signup-submit{width: 100%;}
	.field-accept_terms .error{padding-left: 32px;}
</style>
