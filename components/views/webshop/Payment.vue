<template>
	<BaseCmsPage>
		<WebshopCheckoutLayout>
			<template #header>
				<WebshopCheckoutHeader :step="4" />
			</template>
			<template #wcCol2>
				<BaseWebshopCheckout>
					<WebshopStep :step="1" :completed="true" />
					<WebshopStep :step="2" :completed="true" />
					<div class="step-form-cnt step-form3-cnt">
						<WebshopStep :step="3" :title="true" />
						<BaseWebshopPaymentForm class="form step3-form form-animated-label" v-slot="{fields, loading, status, onPaymentUpdate, selectedField}">
							<div class="step3-form-inner">
								<div v-if="status?.success && status?.data?.label_name">
									<div class="global-success"><BaseCmsLabel :code="status.data.label_name" /></div>
								</div>
								<div v-if="status?.data?.errors">
									<div class="global-error" v-for="error in status.data.errors" :key="error">{{ error.error }}</div>
								</div>

								<p v-for="field in fields" :key="field.value">
									<BaseFormField :item="field" v-slot="{errorMessage}">
										<BaseFormInput :checked="field.selected" :id="field.code" @click="onPaymentUpdate(field)" mode="custom" />
										<label :for="field.code">
											{{ field.title }}
											<div v-show="field.description" class="payment-info" v-html="field.description"></div>
										</label>
										<span class="error" v-show="errorMessage" v-html="errorMessage" />
										<BaseThemeWebshopCreditCardInstallmentsForm :field="field" :selected-field="selectedField" :onPaymentUpdate="onPaymentUpdate" />
									</BaseFormField>
								</p>
							</div>
							<button class="btn btn-green" type="submit" :class="{'loading': loading}" :disabled="loading"><UiLoader v-if="loading" /><BaseCmsLabel code="next_step" /></button>
						</BaseWebshopPaymentForm>
					</div>
					<WebshopStep :step="4" />
				</BaseWebshopCheckout>
			</template>
		</WebshopCheckoutLayout>
	</BaseCmsPage>
</template>

<style lang="less" scoped>
	.step-form-cnt{
		padding: 17px 0 40px;
		@media(max-width: @m){padding: 20px 0 32px;}
	}
	.step3-form-inner{
		padding-left: 44px;
		@media(max-width: @m){padding-left: 0;}
	}
	.btn-green{
		margin-top: 5px;
		@media(max-width: @m){width: 100%; margin-top: 12px;}
	}
	input[type=radio]:checked + label{
		font-weight: normal;
		:deep(.payment-info){display: block;}
	}
	:deep(.global-success,.global-error){display: none;}
	.payment-info{
		padding: 8px 0 0 0; font-size: 14px; line-height: 1.5; display: none;
		@media(max-width: @m){font-size: 13px; padding-top: 6px;}
	}
	:deep(.wc-card-payment){
		padding: 10px 0 0 32px; font-size: 14px;
		@media (max-width: @m){font-size: 13px;}
		.label{padding: 0 0 3px 0;}
	}
</style>
