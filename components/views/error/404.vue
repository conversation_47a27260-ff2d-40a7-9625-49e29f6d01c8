<template>
	<BaseCmsPage :fetch="{slug: '/404/'}" v-slot="{page}">
		<Body class="page-error page-error-404" />
		<CmsPageLayout>
			<h1 v-if="page?.title">{{ page.title }}</h1>
			<h1 v-else>404</h1>

			<div v-if="page?.content" v-html="page.content" v-interpolation />

			<div class="main-categories">
				<CatalogMainCategories />
			</div>
			<div class="return">
				<NuxtLink class="btn" to="/"><BaseCmsLabel code="return_back" /></NuxtLink>
			</div>
		</CmsPageLayout>
	</BaseCmsPage>
</template>

<style scoped lang="less">
	.main-categories{padding-top: 30px;
		@media (max-width: @m){padding-top: 15px;}
	}
	.return{
		text-align: center; padding: 40px 0 0;
		@media (max-width: @m){padding: 20px 0 0;}
	}
</style>
