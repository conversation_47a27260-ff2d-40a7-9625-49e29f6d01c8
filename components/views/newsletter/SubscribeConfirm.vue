<template>
	<BaseCmsPage :fetchSlugSegments="2" v-slot="{page}">
		<CmsPageLayout>
			<h1 v-if="page?.seo_h1">{{ page.seo_h1 }}</h1>
			<div v-if="page?.content" v-html="page.content" />
			<ClientOnly>
				<BaseNewsletterConfirmSubscribe v-slot="{status}">
					<BaseCmsLabel tag="div" class="global-success" :code="status?.label_name" />
				</BaseNewsletterConfirmSubscribe>
			</ClientOnly>
		</CmsPageLayout>
	</BaseCmsPage>
</template>
