<template>
	<BaseCmsPage :fetchSlugSegments="2" v-slot="{page}">
		<CmsPageLayout>
			<h1 v-if="page?.seo_h1">{{ page.seo_h1 }}</h1>
			<div v-if="page?.content" v-html="page.content" />
			<ClientOnly>
				<BaseNewsletterConfirmUnsubscribe v-slot="{status}">
					<BaseCmsLabel tag="div" :code="status?.label_name" />
				</BaseNewsletterConfirmUnsubscribe>
			</ClientOnly>
		</CmsPageLayout>
	</BaseCmsPage>
</template>
