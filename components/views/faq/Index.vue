<template>
	<BaseCmsPage v-slot="{page}">
		<CmsPageLayout>
			<CmsBreadcrumbs v-if="page?.breadcrumbs" :items="page.breadcrumbs" />
			<h1 v-if="page?.seo_h1">{{ page.seo_h1 }}</h1>
			<div v-if="page?.content" class="cms-content" v-html="page.content" ref="content" v-interpolation />

			<span class="faq-main">
				<BaseFaqCategories :fetch="{limit: 4}" v-slot="{items}">
					<BaseUiTabs v-if="items" class="faq-c-items">
						<BaseUiTab class="faqc" :class="['facq-'+ item.code]" v-for="item in items" :key="item.id" :title="item.title">
							<BaseFaqQuestions :fetch="{category_position: item.position_h}" v-slot="{items}">
								<div v-if="items" class="faq-section">
									<CmsFaqItem v-for="faq_item in items" :key="faq_item.id" :item="faq_item" />
								</div>
							</BaseFaqQuestions>
						</BaseUiTab>
					</BaseUiTabs>
				</BaseFaqCategories>

				<ClientOnly>
					<CmsFaqInfo />
				</ClientOnly>
				<CmsShare />
			</span>
		</CmsPageLayout>
	</BaseCmsPage>
</template>

<script setup></script>

<style lang="less">
	.page-faq-index .main-content{
		@media (max-width: @ms){padding-bottom: 0;}
	}
</style>

<style lang="less" scoped>
	.page-faq-index{
		@media (max-width: @m){
			.cms-content{display: none;}
			.faq-main{display: flex; flex-flow: column;}
		}
	}
	.faq-c-items{
		@media (max-width: @m){width: calc(~"100% - -32px"); margin-left: -16px; margin-bottom: 9px;}
	}
	:deep(.tabs-nav){
		display: flex!important; border-bottom: 1px solid var(--colorBorderLightForms); padding-top: 12px;
		.faqc{
			width: 100%; margin-right: 8px; background: var(--colorWhite); height: 96px; display: flex; align-items: center; justify-content: center; cursor: pointer; .transition(background);
			&:last-child{margin-right: 0;}
			span{
				position: relative; padding-left: 48px; font-size: 20px; line-height: 1.4; font-weight: bold; color: var(--colorBase); .transition(color);
				&:before{.pseudo(40px, 40px); left: 0; top: -6px; background: url(assets/images/custom-icons/shopping-cart.svg); background-repeat: no-repeat!important; background-size: contain!important; background-position: center center!important;}
				&:after{.pseudo(auto, 3px); border-radius: 2px; background: var(--colorBaseBlue); bottom: -34px; left: 0; right: 0; opacity: 0; .transition(opacity);}
			}
			@media (min-width: @h){
				&:hover{
					background: var(--colorLightBlueBackground);
					span{
						color: var(--colorBaseBlue);
						&:after{opacity: 1;}
					}
				}
			}
			&.active{
				background: var(--colorLightBlueBackground);
				span{
					color: var(--colorBaseBlue);
					&:after{opacity: 1;}
				}
			}
		}
		.facq-dostava span:before{background: url(assets/images/custom-icons/box.svg); width: 41px;}
		.facq-servis span:before{background: url(assets/images/custom-icons/service.svg); width: 41px;}
		.facq-ostalo span:before{background: url(assets/images/custom-icons/other.svg);}
		@media (max-width: @m){
			border-top: 1px solid var(--colorBorderLightForms); padding-top: 0; margin-top: 2px;
			.faqc{
				margin-right: 0px; width: 25%; padding:	0 0 16px 0; align-items: flex-end; height: 90px;
				span{
					font-size: 13px; line-height: 1.1; padding-left: 0; display: flex; flex-flow: column; align-items: center; justify-content: center; padding-top: 38px;
					&:before{width: 29px; height: 28px; left: 50%; margin-left: -14px; top: 0; background-size: cover!important;}
					&:after{bottom: -16px;}
				}
			}
			.facq-dostava span:before{width: 28px; height: 30px;}
			.facq-servis span:before{width: 28px;}
			.facq-ostalo span:before{width: 25px; height: 24px; top: 2px;}
		}
	}

	.faq-info{
		margin-bottom: 12px;
		@media (max-width: @m){margin-bottom: 0;}
	}

	.faq-section{
		padding-top: 8px; position: relative; display: block;
		@media (max-width: @m){padding-top: 3px;}
	}
	:deep(.tab-title){display: none!important;}
</style>
