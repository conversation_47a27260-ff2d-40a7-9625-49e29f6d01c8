<template>
	<BaseCmsPage>
		<BaseSearchResults :search-modules="[{module: 'catalog'}, {module: 'publish'}, {module: 'cms'}]" v-slot="{items, searchContent, searchTerm}">
			<SearchTitleContainer :extraterm="searchTerm" />

			<div class="search-container">
				<template v-if="searchContent == 'publish'">
					<div class="wrapper">
						<BasePublishPosts v-slot="{items, nextPage, loading, loadMore}">
							<div class="s-publish" v-if="items">
								<PublishIndexEntry v-for="post in items" :key="post.id" :item="post" :short-description="true" />
							</div>
							<ClientOnly>
								<div class="load-more-container" v-if="nextPage" data-posts-scroll-trigger>
									<a href="javascript:void(0);" class="btn load-more btn-load-more" :class="{'loading': loading}" @click="loadMore"><UiLoader v-if="loading" /><BaseCmsLabel code="load_more_articles" /></a>
								</div>
								<BaseUiPagination class="pagination" />
							</ClientOnly>
						</BasePublishPosts>
					</div>
				</template>
				<template v-if="searchContent == 'cms' && items?.cms">
					<div class="wrapper s-cms-wrapper">
						<BaseCmsPage :fetch="{mode: 'search', 'search_q': searchTerm}" v-slot="{page}" :seo="false">
							<article class="s-item" v-for="item in page.items" :key="item.id">
								<h2 class="s-item-title">
									<NuxtLink :to="item.url_without_domain">{{ item.title }}</NuxtLink>
								</h2>
								<div v-if="item?.content" v-html="limitWords(stripHtml(item.content), 50)" />
							</article>
						</BaseCmsPage>
					</div>
				</template>
			</div>
		</BaseSearchResults>
	</BaseCmsPage>
</template>

<script setup>
	const {stripHtml, limitWords} = useText();
</script>

<style lang="less" scoped>
	.search-container{
		display: block; padding: 47px 0 88px;
		@media (max-width: @t){padding: 40px 0 55px;}
		@media (max-width: @m){padding: 16px 0 32px;}
	}
	.s-publish{
		display: flex; flex-wrap: wrap; gap: 40px; row-gap: 50px;
		.pp{margin: 0;}
		@media (max-width: @t){
			gap: 20px; row-gap: 30px;
			.pp{width: calc(~"33.3333% - 14px");}
		}
		@media (max-width: @m){
			gap: 10px; row-gap: 24px;
			.pp{flex-flow: column; align-items: center; justify-content: center; width: calc(~"33.3333% - 7px");}
			:deep(.pp-cnt){padding: 12px 24px 0;}
		}
		@media (max-width: @m){
			gap: 0; row-gap: 32px;
			.pp{flex-flow: column; align-items: center; justify-content: center; width: 100%;}
			:deep(.pp-cnt){padding: 12px 24px 0;}
		}
	}
	.load-more-container{
		position: relative; display: flex; align-items: center; justify-content: center; margin-top: 56px;
		@media (max-width: @m){margin-top: 40px;}
		@media (max-width: @ms){
			margin-top: 32px; width: 100%;
			.btn{width: 100%;}
		}
	}
	.s-cms-wrapper{width: 776px;}
	.s-item{
		display: block; margin-bottom: 30px;
		&:last-child{margin-bottom: 0;}
		@media (max-width: @t){margin-bottom: 24px;}
	}
	.s-item-title{
		display: block; font-size: var(--fontSizeH4); padding: 0 0 8px;
		a{
			text-decoration: 1px underline; text-underline-offset: 5px;
			@media (min-width: @h){
				&:hover{text-decoration-color: transparent;}
			}
		}
		@media (max-width: @ms){font-size: 19px; line-height: 23px; padding: 0 0 6px;}
	}
</style>

<style lang="less">
	/*
	.s-header{position: relative; display: block; width: 100%; background: var(--colorLightBlueBackground); padding: 56px 0 32px;}
	.s-header-wrapper{display: flex; flex-flow: column; align-items: center;}
	.s-header-title{
		display: flex; flex-flow: column; align-items: center; font-size: var(--fontSizeLabel); text-transform: uppercase; color: var(--colorBaseBlue); font-weight: bold;
		strong{text-transform: none; color: var(--colorBase); font-size: var(--fontSizeH3); padding-top: 3px;}
	}
	.s-nav-cnt{position: relative; }
	.s-nav{
		position: relative; display: flex; gap: 8px; list-style: none; padding: 0; margin: 28px 0 0;
		li{
			display: block;
			&.selected{
				a{
					background: var(--colorBaseBlue); color: var(--colorWhite);
					&:after{opacity: 1;}
				}
				.s-counter{color: var(--colorWhite);}
			}
		}
		a{
			position: relative; display: flex; align-items: center; justify-content: center; font-weight: bold; background: var(--colorWhite); padding: 11px 24px; color: var(--colorBase); text-decoration: none; border-radius: var(--inputBorderRadius); box-shadow: 0 8px 18px rgba(0,89,194,0.12); transition: color 0.3s, background 0.3s;
			&:after{.pseudo(8px,8px); background: var(--colorBaseBlue); .rotate(45deg); bottom: -3px; left: calc(~"50% - 4px"); opacity: 0; .transition(opacity);}
			@media (min-width: @h){
				&:hover{
					background: var(--colorBaseBlue); color: var(--colorWhite);
					.s-counter{color: var(--colorWhite);}
				}
			}
		}
		.s-counter{font-weight: normal; font-size: 14px; color: var(--colorBaseBlue); .transition(color);}
	}
	*/
</style>
