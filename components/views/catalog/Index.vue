<template>
	<BaseCatalogCategory v-slot="{item: category, contentType, isLanding}" :seo="true">
		<Body :class="{ 'page-catalog-lvl1': category?.level == 1, 'page-catalog-lvl2': contentType == 'manufacturer' || category?.level && category?.level > 1, 'page-brand': contentType == 'manufacturer', 'active-filters': activeFilters}" />
		<CatalogLandingCategory :category="category" v-if="isLanding(category)" />
		<template v-else>
			<BaseCatalogProducts v-slot="{items: products, loading, pagination, nextPage, loadMore}" @loadProducts="onLoadProducts">
				<div class="s-header" v-if="contentType == 'search'">
					<ClientOnly>
						<BaseSearchResults :search-modules="[{module: 'catalog'}, {module: 'publish'}, {module: 'cms'}]" v-slot="{searchTerm}">
							<SearchTitleContainer :extraterm="searchTerm" />
						</BaseSearchResults>
					</ClientOnly>
				</div>

				<div class="title-container" :class="[{'title-container-manufacturer': contentType == 'manufacturer'}]" v-else>
					<div class="wrapper title-container-wrapper">
						<CmsBreadcrumbs v-if="category?.breadcrumbs && contentType != 'search'" :items="category.breadcrumbs" />

						<template v-if="contentType == 'manufacturer'">
							<CatalogManufacturesDetails :category="category" />
						</template>

						<template v-else>
							<h1 v-if="category?.title">{{ category.title }}</h1>
						</template>

						<BaseUiAccordion v-if="category?.content && category?.hide_description !== 1 && contentType != 'manufacturer'">
							<BaseUiAccordionPanel id="catalogDescription" v-slot="{ onToggle, active }">
								<div class="c-desc" :class="{ active }">
									<div ref="contentRef" class="c-desc-content" :class="{ active: active && isLongText }" v-html="category.content" v-interpolation />

									<div v-if="isLongText && active" class="c-desc-btn" @click="onToggle">
										<span class="less"><BaseCmsLabel code="category_description_less" /></span>
									</div>

									<div v-else-if="isLongText && !active" class="c-desc-btn" @click="onToggle">
										<span class="more"><BaseCmsLabel code="category_description_more" /></span>
									</div>
								</div>
							</BaseUiAccordionPanel>
						</BaseUiAccordion>

						<LazyBaseCatalogCategoriesWidget v-if="contentType == 'category' && category?.level == 2" :fetch="{start_position: category.position_h}" v-slot="{items: categories}" @load="onSubcategoriesLoad">
							<div class="c-categories-wrapper" v-if="categories.length">
								<ul class="c-categories">
									<li class="c-category" v-for="category in categories" :key="category.id">
										<NuxtLink :to="category.url_without_domain">
											<span>{{ category.title }}</span>
										</NuxtLink>
									</li>
								</ul>
							</div>
						</LazyBaseCatalogCategoriesWidget>
						<LazyBaseCatalogCategoriesWidget v-if="contentType == 'category' && category?.level > 2" :fetch="{start_position: category.parent_position_h, level_to: category.level}" v-slot="{items}" @load="onSubcategoriesLoad">
							<div class="c-categories-wrapper" v-if="items?.length">
								<ul class="c-categories">
									<template v-for="item in items" :key="item.id">
										<li class="c-category" :class="{active: item.id == category.id}">
											<NuxtLink :to="item.url_without_domain">
												<span>{{ item.title }}</span>
											</NuxtLink>
										</li>
									</template>
								</ul>
							</div>
						</LazyBaseCatalogCategoriesWidget>

						<BaseCmsRotator
							v-if="category?.level && category?.level == 2"
							:fetch="{code: 'c_promo', limit: 6, catalogcategory_id: category.id, response_fields: ['id','link','template','url_without_domain','title','title2','content','image_upload_path','image_2_upload_path','image_thumbs','image_2_thumbs','catalogcategory_ids']}"
							v-slot="{items}">
							<CmsPromo class="category-promos" :items="items" v-if="items?.length" />
						</BaseCmsRotator>
					</div>
				</div>

				<div v-if="category?.level || contentType == 'manufacturer' || contentType == 'search'" class="c-main-items">
					<BaseCatalogFilters v-slot="{searchFields, selectedFiltersCounter}">
						<div class="c-items-row wrapper">
							<div class="c-filters-section">
								<ClientOnly>
									<template v-if="category && contentType == 'category'">
										<CatalogCategories :category="category" :subcategories="subcategories" />
									</template>

									<CatalogFilters @close-filters="activeFilters = false;" :total-products="pagination?.items?.total || 0" :search-fields="searchFields" :content-type="contentType" />
								</ClientOnly>
							</div>
							<div class="c-items-col">
								<ClientOnly>
									<div class="c-toolbar-fixed" v-if="searchFields.length">
										<div class="c-toolbar-fixed-inner">
											<div class="cf-toggle" @click="activeFilters = !activeFilters">
												<BaseCmsLabel code="filters" /><span v-if="selectedFiltersCounter && selectedFiltersCounter > 0" class="cf-toggle-counter">{{selectedFiltersCounter}}</span>
											</div>
											<CatalogSort v-if="products?.length > 1" />
										</div>
									</div>

									<div class="c-toolbar">
										<div class="c-total" v-if="pagination?.items?.total">{{ pagination.items.total }} <BaseCmsLabel code="total_products_label" /></div>
										<div class="c-toolbar-right">
											<div class="cf-special">
												<BaseCatalogSpecialFilter filter="with_qty" v-if="products?.length || route.query?.with_qty" />
												<BaseCatalogSpecialFilter filter="discount" v-if="products?.length || route.query?.discount" />
											</div>
											<CatalogSort v-if="products?.length > 1" />
										</div>
									</div>
								</ClientOnly>

								<div id="items_catalog_layout" :class="{'loading': loading}">
									<template v-if="products.length">
										<div class="c-items">
											<BaseCatalogPromo v-slot="{items: promoItems}">
												<template v-for="(product, index) in products" :key="product.id">
													<CatalogIndexEntry :item="product" :order="index + 1" :promo="promoItems" />
												</template>
											</BaseCatalogPromo>
										</div>
										<ClientOnly>
											<div class="c-items-footer" v-if="loading || nextPage">
												<div class="c-load-more-container" v-if="nextPage" data-products-scroll-trigger>
													<div class="c-pagination-status-label">
														Pregledano <span class="total_items_display fw-b" v-if="pagination?.items?.current">{{ pagination.items.current }}</span> od <span class="fw-b" v-if="pagination?.items?.total">{{ pagination.items.total }}</span>
													</div>
													<progress v-if="pagination.items" :value="pagination.items.current" :max="pagination.items.total" />
													<button type="button" class="btn load-more btn-load-more" :class="{'loading': loading}" @click="loadMore()"><UiLoader v-if="loading" /><BaseCmsLabel code="load_more_catalog" /></button>
												</div>
											</div>
										</ClientOnly>
										<BaseUiPagination class="pagination c-pagination-items" />
									</template>
									<div v-else class="c-empty"><BaseCmsLabel code="no_products" /></div>
								</div>
							</div>
						</div>
					</BaseCatalogFilters>
				</div>
			</BaseCatalogProducts>
		</template>
	</BaseCatalogCategory>
</template>

<script setup>
	const {appendTo} = useDom();
	const {waitForWindowProperty} = useMeta();
	const route = useRoute();

	const activeFilters = ref(false);
	const activeFiltersTotal = ref(0);

	const subcategories = ref([]);
	function onSubcategoriesLoad(data) {
		subcategories.value = data.items;
	}

	//show/hide hide_description
	const contentRef = ref(null);
	const isLongText = ref(false);

	watchEffect(() => {
		if (contentRef.value) {
			const contentHeight = contentRef.value.offsetHeight;
			isLongText.value = contentHeight > 50;
		}
	});

	function onLoadProducts(data) {
		// Custom remarketing event
		waitForWindowProperty('gtag', () => {
			let totalValue = 0;
			let items = [];

			if(data.items?.length) {
				items = data.items.map(item => {
					totalValue += Number(item.price_custom);
					return {
						'id': item.id,
						'location_id': data.contentType === 'search' ? 'searchresults' : 'category',
						'google_business_vertical': 'custom'
					}
				})
			}

			gtag('event', 'page_view', {
				'send_to': 'AW-***********',
				'value': totalValue,
				'items': items
			});
		})
	}
</script>

<style lang="less" scoped>
	progress{height: 4px; width: 200px; border-radius: 4px; background-color: var(--colorBorderLightForms); margin: 5px 0 0;}
	progress::-webkit-progress-bar { background: transparent; }
	progress::-webkit-progress-value { background-color: var(--colorBaseBlue); border-radius: 4px; }

	/* Catalog header */
	.title-container{
		position: relative; display: block; background: var(--colorLightBlueBackground); padding: 55px 0 40px;
		.bc{padding-bottom: 16px;}
		h1{padding-bottom: 20px;}
		@media (max-width: @t){
			padding: 40px 0 25px;
			.bc{padding-bottom: 14px;}
			h1{padding-bottom: 16px;}
		}
		@media (max-width: @m){
			padding: 20px 0 0; margin-bottom: 15px; background: var(--colorWhite);
			h1{padding-bottom: 0; margin-bottom: 16px;}
			.bc{padding-bottom: 11px;}
		}
	}
	.c-desc {
		width: 100%; margin-bottom: 16px; position: relative;
		@media (max-width: @t){margin-bottom: 30px;}
		@media (max-width: @m){
			margin-bottom: 24px;
			&.active {
				.c-desc-btn {
					display: inline-block; position: relative; text-decoration: underline;
					& > span:after {.rotate(90deg);}
					.more {display: none;}
					.less {display: inline-block;}
				}
			}
		}
	}
	.c-desc-content {
		display: block; padding: 0;
		:deep(p){padding-bottom: 0;}
		@media (max-width: @m){
			display: -webkit-box; overflow: hidden; text-overflow: ellipsis; -webkit-line-clamp: 4; -webkit-box-orient: vertical; padding: 0; position: relative; font-size: 13px; line-height: 1.5;
			&.active {display: block; overflow: initial; text-overflow: unset; -webkit-line-clamp: unset; -webkit-box-orient: unset;}
		}
	}
	.c-desc-btn {
		display: none;
		@media (max-width: @m){
			display: inline-block; margin: 0; padding: 0; font-size: 13px; line-height: 1.5; text-decoration: underline; text-underline-offset: 3px; color: var(--colorBaseBlue); position: relative; cursor: pointer; z-index: 1; margin-top: 10px;
			& > span {
				padding-right: 13px; position: relative;
				&:after {.icon-arrow2(); font: 8px/8px var(--fonti); font-weight: 600; color: var(--colorBaseBlue); position: absolute; top: 5px; right: 0; .rotate(-90deg);}
			}
			.less {display: none;}
		}
	}

	.c-categories-wrapper{
		@media (max-width: @m){
			position: relative;
			&:after{.pseudo(39px, 48px); top: 0; right: -16px; background: linear-gradient(270deg, #FFFFFF 0%, rgba(255,255,255,0.98) 25.71%, rgba(255,255,255,0) 100%);}
		}
		@media (max-width: @m){
			&:after{height: 32px;}
		}
	}
	.c-categories{
		list-style: none; position: relative; display: flex; flex-wrap: wrap;
		li{
			display: block; width: auto; height: 48px; margin: 0 8px 8px 0; font-size: 0; line-height: 0;
			&>a{
				display: flex; align-items: center; height: 100%; padding: 0 15px; background: var(--colorWhite); border: 1px solid var(--colorBorderLightForms); font-size: 14px; line-height: 1.3; color: var(--colorBase); text-decoration: none; position: relative; border-radius: var(--inputBorderRadius); white-space: nowrap; transition: border-color 0.3s, color 0.3s, background 0.3s;
				@media (min-width: @h){
					&:hover{color: var(--colorBaseBlue); border-color: var(--colorBaseBlue);}
				}
			}
			&.active{
				&>a{
					border-color: var(--colorBaseBlue); background: var(--colorBaseBlue); color: var(--colorWhite);
					@media (min-width: @h){
						&:hover{border-color: var(--colorBaseBlue); background: var(--colorBaseBlue); color: var(--colorWhite);}
					}
				}
			}
			&.discount{
				&>a{color: var(--colorRed);}
			}
		}
		@media (max-width: @m){
			flex-flow: row; width: calc(~"100% - -32px"); margin-left: -16px; padding: 0 16px; overflow-y: hidden; gap: 8px;
			&::-webkit-scrollbar{-webkit-appearance: none; height: 0; background: transparent; z-index: 0;}
			&::-webkit-scrollbar-thumb{background-color: transparent;}
			li{
				height: 32px; margin: 0;
				&>a{padding: 0 11px; font-size: 13px;}
			}
		}
	}

	//Manufacturer
	.title-container-manufacturer{
		padding: 56px 0 48px;
		.bc{padding-bottom: 24px;}
		@media (max-width: @m){
			padding: 24px 0 8px;
			:deep(.wrapper-bc:after){display: none;}
		}
	}

	/* Catalog Main */
	.c-items-row{
		display: flex; align-items: flex-start; padding-bottom: 80px;
		@media (max-width: @t){padding-bottom: 70px;}
		@media (max-width: @m){padding-bottom: 0;}
	}
	.c-items-col{
		flex-grow: 1;
	}
	#items_catalog_layout{
		position: relative;
		&.loading{
			&:before{ .pseudo(auto,auto); top: 0; left: 0; right: 0; bottom: 0; background: rgba(255,255,255,0.5); z-index: 10;}
		}
	}
	.c-items{
		display: flex; flex-wrap: wrap; width: 100%; overflow-x: hidden; padding-left: 1px; padding-top: 1px;
		@media (max-width: @t){margin-left: -1px;}
		@media (max-width: @m){width: calc(~"100% - -32px"); margin-left: -16px;}
	}
	.c-items-footer{
		padding-top: 40px;
		@media (max-width: @m){padding: 25px 0 40px;}
	}
	.c-load-more-container{
		display: flex; flex-flow: column; align-items: center;
		@media (max-width: @m){width: 100%;}
	}
	.c-pagination-status-label{
		font-size: 12px; line-height: 1.5;
		span{font-weight: bold;}
		@media (max-width: @m){line-height: 1.4;}
	}
	.btn-load-more{
		margin-top: 16px;
		@media (max-width: @t){font-size: 14px; margin-top: 14px;}
		@media (max-width: @m){font-size: 15px; margin-top: 16px; height: 48px; width: 100%;}
	}
	.load-more-loader{height: 50px; margin-bottom: 10px; background: white url(/assets/images/loader.svg) no-repeat center; background-size: contain; position: relative; z-index: 11;}

	.c-empty{
		padding: 30px 0 0 25px;
		@media (max-width: @m){padding: 20px 0;}
	}


	//toolbar+filters
	.c-filters-section{
		flex-shrink: 0; width: 242px; margin: 21px 16px 0 0;
		@media (max-width: @t){width: 190px; margin-right: 0;}
		@media (max-width: @m){display: none; margin: 0; position: fixed; top: 0; bottom: 0; left: 0; right: 0; width: auto; z-index: 10000; background: #fff; overflow: auto;}
	}
	.c-toolbar{
		display: flex; align-items: center; justify-content: space-between; padding: 32px 0 12px 24px; position: relative; font-size: 14px;
		@media (max-width: @m){display: block; padding: 12px 16px; margin: 0 -16px; border-top: 1px solid var(--colorBorderLightForms);}
	}
	.c-total{
		@media (max-width: @m){display: none;}
	}
	.c-toolbar-right{
		display: flex; align-items: center;
		@media (max-width: @m){display: block;}
	}
	.cf-special{
		display: flex; align-items: center;
		@media (max-width: @m){justify-content: space-between;}
	}
	.c-toolbar-fixed{
		display: none; position: sticky; top: -6px; z-index: 100; background: #fff; padding: 16px; margin: 0 -16px;
		@media (max-width: @m){display: block; }
	}
	.c-toolbar-fixed-inner{display: flex; border-radius: var(--inputBorderRadius); overflow: hidden;}
	.cf-toggle{
		background: var(--colorBaseBlue); color: #fff; font-size: 13px; font-weight: bold; height: 48px; flex: 1 1 50%; display: flex; align-items: center; padding: 0 16px; position: relative;
		&:after{position: absolute; .icon-filters(); font: 11px/11px var(--fonti); color: var(--colorIconLightBlue); right: 15px; top: 19px;}
	}
	.cf-toggle-counter{width: 20px; height: 20px; line-height: 20px; border-radius: 100%; background: #fff; font-size: 12px; color: var(--colorBaseBlue); text-align: center; margin-left: 9px;}
	:deep(.cf-with_qty), :deep(.cf-discount){
		font-weight: bold; margin-right: 32px;
		@media (max-width: @m){margin: 0;}
		input[type=checkbox]+label{
			padding: 0 34px 0 0;
			@media (max-width: @m){font-size: 13px;}
			&:before{width: 24px; height: 10px; background: var(--colorBorderLightForms); border: 0; border-radius: 5px; left: auto; right: 0; top: 5px; content:"";}
			&:after{ .pseudo(16px,16px); top: 2px; right: 9px; box-shadow: 0 2px 4px 0 rgba(0,0,0,0.24); border-radius: 100%; background: #fff; .transition(~"right 0.3s,background");}
		}
		input[type=checkbox]:checked+label{
			&:before{background: var(--colorGreen); }
			&:after{right: -1px; background: var(--colorBaseBlue);}
		}
	}
	:deep(.cf-discount){
		color: var(--colorRed);
		input[type=checkbox]:checked+label{
			&:before{background: var(--colorRed);}
		}
	}
</style>

<style lang="less">
	body.fixed-header #__nuxt {
		@media (max-width: @m){
			.c-toolbar-fixed{top: 56px;}
		}
	}
	.active-filters{
		overflow: hidden;
		#__nuxt .c-filters-section{display: block;}
	}
</style>
