<template>
	<BaseCatalogDetail v-slot="{item}" @load="onLoad">
		<Body :class="{'fixed-footer': !addToCartVisible}" />
		<template v-if="item">
			<div class="cd-main" ref="cdMain">
				<div class="wrapper cd-main-wrapper">
					<div class="cd-col cd-col1">
						<!--
						<div class="cd-images" v-interpolation>
							<CatalogDetailBadges :item="item" v-if="item.category_template != 'gifts'" />
							<template v-if="item.images?.length">
								<BaseUiSwiper class="cd-hero-slider" :options="{effect: 'fade', fadeEffect: {crossFade: true}, navigation: {enabled: false}}" :thumbs-swiper="thumbsSwiper">
									<BaseUiSwiperSlide v-for="(file, index) in item.images" :key="index" class="cd-hero-slide">
										<a :href="file.url" class="fancybox" rel="catalog" :data-index="index" :data-thumb="file.file_thumbs?.['width150-height150']?.thumb">
											<BaseUiImage :title="file.title" :alt="file.description" :loading="index == 0 ? 'eager' : 'lazy'" :data="file.file_thumbs?.['width980-height980']" default="/images/no-image-560.jpg" />
										</a>
									</BaseUiSwiperSlide>
									<BaseUiSwiperSlide class="cd-hero-slide video" v-if="item.video_url">
										<BaseUiVideo :src="item.video_url" width="560" height="315" />
									</BaseUiSwiperSlide>
								</BaseUiSwiper>
								<ClientOnly>
									<BaseUiSwiper v-if="item.images?.length > 1 || item.video_url" class="cd-thumbs cd-thumbs-slider" :options="{slidesPerView: 6, slidesPerGroup: 6, watchSlidesProgress: true, navigation: {enabled: false}}" @init="setThumbsSwiper">
										<BaseUiSwiperSlide v-for="(thumb, index) in item.images" :key="index" class="cd-thumb">
											<span><BaseUiImage loading="lazy" :data="thumb.file_thumbs?.['width150-height150']" default="/images/no-image-88.jpg" /></span>
										</BaseUiSwiperSlide>
										<BaseUiSwiperSlide class="cd-thumb video" v-if="item.video_url">
											<span v-if="item.video_image"><BaseUiImage :src="item.video_image" /></span>
										</BaseUiSwiperSlide>
									</BaseUiSwiper>
								</ClientOnly>
							</template>
							<template v-else>
								<div class="cd-hero-image cd-no-image">
									<BaseUiImage src="/images/no-image-560.jpg" :title="item.seo_h1" :alt="item.seo_h1" />
								</div>
							</template>
						</div>
						-->

						<ClientOnly>
							<BaseCatalogProductsWidget :fetch="{related_code: 'bought_together', related_item_id: item.id, only_with_image: true, only_available: true}" v-slot="{items, meta}">
								<LazyCatalogDetailBoughtTogether :items="items" :meta="meta" hydrate-on-visible />
							</BaseCatalogProductsWidget>
						</ClientOnly>
					</div>
					<div class="cd-col cd-col2">
						<div class="cd-col2-main">
							<BaseCatalogVariations :item="item" v-slot="{attributes, items: variationItems, selectedVariation, variationAddToCartData}">
								<div class="cd-col2-top">
									<CmsBreadcrumbs v-if="item?.breadcrumbs" :items="item.breadcrumbs" mode="catalogDetail" />
									<div class="cd-manufacturer" v-if="item.manufacturer_code">
										<NuxtLink :to="item.manufacturer_url_without_domain">
											<BaseUiImage loading="lazy" :src="item.manufacturer_main_image_upload_path" default="/images/no-image-104.jpg" width="100" height="100" v-if="item.manufacturer_main_image_upload_path" />
											<span class="cd-manufacturer-title" v-else>{{item.manufacturer_title}}</span>
										</NuxtLink>
									</div>
									<h1 class="cd-title">{{item.title}}</h1>
									<div class="cd-code-availability">
										<ClientOnly>
											<div class="cd-code"><BaseCmsLabel code="code" />: {{(selectedVariation) ? selectedVariation.code : item.code}}</div>
										</ClientOnly>
										<template v-if="item.category_template != 'gifts'">
											<div v-if="item.status > 1" class="cd-availability" :class="{'in-comming': [2,4].includes(Number(item.status)), 'not-available': [3, 9].includes(Number(item.status))}">{{item.status_description}}</div>
											<template v-else>
												<template v-if="variationItems?.length">
													<div v-if="selectedVariation" class="cd-availability" :class="{'available': selectedVariation.is_available, 'not-available': !selectedVariation.is_available}">
														<BaseCmsLabel :code="(selectedVariation.is_available) ? 'product_available' : 'product_not_available'" />
													</div>
												</template>
												<template v-else>
													<div class="cd-availability" :class="{'available': item.is_available, 'not-available': !item.is_available}">
														<BaseCmsLabel :code="(item.is_available) ? 'product_available' : 'product_not_available'" />
													</div>
												</template>
											</template>
										</template>
									</div>

									<ClientOnly>
										<div class="cd-content-intro-container" :class="{'active': shortDesc}" v-if="item.content">
											<div class="cd-content-intro" ref="contentIntro" v-html="limitWords(stripHtml(item.content), 33)" v-interpolation v-if="item.content" :class="{custom: item.content.split(' ').length > 33}"></div>
											<div class="cd-content-intro-btn" v-if="item.content.split(' ').length > 33" @click="scrollTo('#tab-desc', {offset: 170}), shortDesc = true">
												<span><BaseCmsLabel code="more_details" /></span>
											</div>
										</div>
									</ClientOnly>
								</div>

								<ClientOnly>
									<template v-if="item.status == 2">
										<!-- Preorder -->
										<div class="cd-preorder-container">
											<FeedbackNotificationForm :item="item" status="preorder" />
											<CatalogDetailSetCompare :item="item" />
										</div>
									</template>
									<template v-else-if="item.status == 9">
										<div class="cd-retired-container">
											<!-- End of life -->
											<BaseCmsLabel code="product_retired" tag="div" class="retired-product-title" />
											<NuxtLink :to="item.category_url_without_domain" class="btn btn-green"><BaseCmsLabel code="product_retired_button" /></NuxtLink>
										</div>
									</template>
									<template v-else>
										<div ref="cdAdd">
											<div class="cd-col2-middle" v-if="item.price_custom > 0" :class="{'has-discount': (selectedVariation && +selectedVariation.discount_percent_custom) || (!selectedVariation && +item.discount_percent_custom)}">
												<div id="cd-price-container" v-if="+item.status != 2">
													<div class="cd-price-container">
														<div class="cd-price-container-left">
															<template v-if="(selectedVariation && +selectedVariation.discount_percent_custom) || (!selectedVariation && +item.discount_percent_custom)">
																<div class="cd-price">
																	<div class="cd-old-price"><BaseUtilsFormatCurrency :price="(selectedVariation) ? selectedVariation.basic_price_custom : item.basic_price_custom" /></div>
																	<div class="cd-current-price red"><BaseUtilsFormatCurrency :price="(selectedVariation) ? selectedVariation.price_custom : item.price_custom" /></div>
																</div>
																<div class="cd-price-saving">
																	<BaseCmsLabel code="saving" /><BaseUtilsFormatCurrency :price="selectedVariation ? selectedVariation.basic_price_custom - selectedVariation.price_custom : item.basic_price_custom - item.price_custom" />
																</div>
															</template>
															<template v-else>
																<div class="cd-price">
																	<div class="cd-current-price"><BaseUtilsFormatCurrency :price="selectedVariation ? selectedVariation.price_custom : item.price_custom" /></div>
																</div>
															</template>
															<div class="cd-tax"><BaseCmsLabel code="tax_info" /></div>
														</div>
														<CatalogDetailInstallments :item="item" :selectedVariation="selectedVariation" v-if="(item.price_custom >= 100) || (selectedVariation && selectedVariation.price_custom >= 100)" />
													</div>
												</div>
											</div>
											<div class="variations" v-if="variationItems?.length">
												<BaseCatalogVariationsAttributeItem v-for="(attribute, index) in attributes" :key="attribute.id" :item="attribute" v-slot="{item: attr, items: attrItems, onSelect}">
													<div class="variation-attribute" v-if="!attribute.empty">
														<div class="variation-attribute-title">
															{{attr.attribute_title}}
															<span v-if="index == 0 && item.size_guide"><CatalogSizeGuideLink :guide="item.size_guide" /></span>
														</div>
														<div class="variation-attribute-items">
															<div v-for="item in attrItems" :key="item.id" class="variation-attribute-item" :class="{'not-available': !item.available, 'selected': item.selected}" @click="onSelect(item)">
																{{item.title}}
															</div>
														</div>
													</div>
												</BaseCatalogVariationsAttributeItem>
											</div>
											<div class="size-guide-btn" v-if="!variationItems?.length && item.size_guide"><CatalogSizeGuideLink :guide="item.size_guide" /></div>

											<slot name="col2" :item="item">
												<div id="cd-add-to-cart-container">
													<WebshopAddToCartContainer :item="item" :selected-variation="variationAddToCartData" v-if="+item.status != 2" />
												</div>
											</slot>
											<LazyCatalogWarehouse v-if="+item.status != 3" :item="item" :selected-variation="selectedVariation" />
										</div>
									</template>
								</ClientOnly>
							</BaseCatalogVariations>
						</div>
					</div>
				</div>
			</div>

			<slot name="aftermain" :item="item">
				<LazyCatalogDetailContent :item="item" hydrate-on-visible />
				<CatalogDetailBottomBar :item="item" v-show="!addToCartVisible" v-if="item.status != 2" />
			</slot>
		</template>
	</BaseCatalogDetail>
</template>

<script setup>
	const {stripHtml, limitWords} = useText();
	const {scrollTo, prependTo, insertBefore, appendTo, onElementVisibility, onMediaQuery} = useDom();
	const route = useRoute();
	const {waitForWindowProperty} = useMeta();
	const shortDesc = ref(false);
	const thumbsSwiper = ref(null);
	const setThumbsSwiper = (swiper) => {
		thumbsSwiper.value = swiper;
	};

	// scroll to main elemente if url hash (variation id) is present
	onMounted(() => {
		watch(() => route.hash, () => {
			scrollTo('.cd-main');
		},{immediate: true});
	})

	// show bottom bar function
	const cdAdd = ref(null);
	const {isVisible: addToCartVisible} = onElementVisibility({
		target: cdAdd,
		callback: ({isVisible}) => {
			if(!isVisible) {
				appendTo('.cd-price-container', '#add-to-cart-bar-container');
				appendTo('.cd-add-to-cart-container', '#add-to-cart-bar-container');
			} else {
				appendTo('.cd-price-container', '#cd-price-container');
				appendTo('.cd-add-to-cart-container', '#cd-add-to-cart-container');
			}
		}
	});

	onMediaQuery({
		query: '(max-width: 980px)',
		enter: () => {
			prependTo('.bc', '.cd-col1');
			appendTo('.cd-bougth-together', '.cd-col2');
		},
	});

	function onLoad(data) {
		// Custom remarketing tag
		waitForWindowProperty('gtag', () => {
			gtag('event', 'page_view', {
				'send_to': 'AW-***********',
				'value': data.item.price_custom,
				'items': [{
					'id': data.item.id,
					'location_id': 'offerdetail',
					'google_business_vertical': 'custom'
				}]
			});
		})
	}
</script>

<style lang="less" scoped>
	.size-guide-btn{
		margin: -15px 0 20px; font-size: 14px;
		.size-guide{margin-left: 0;}
	}
	.variations{padding: 0 0 25px; display: flex; flex-direction: column; gap: 20px;}
	.variation-attribute-title{
		font-weight: bold; font-size: 14px; line-height: 1.4; padding-bottom: 10px;
	}
	.variation-attribute-items{display: flex; gap: 8px; flex-wrap: wrap;}
	.variation-attribute-item{
		height: 40px; min-width: 48px; font-weight: bold; font-size: 14px; line-height: 1; padding: 0 17px; cursor: pointer; background: #fff; border: 1px solid var(--colorBorderLightForms); border-radius: var(--inputBorderRadius); display: flex; align-items: center; justify-content: center;
		&.selected{background: var(--colorBaseBlue); color: #fff; border-color: var(--colorBaseBlue);}
		&.not-available{border-color: var(--colorBorderLightForms); color: #ACACAC; font-weight: normal;
			&.selected{border-color: var(--colorRed); color: var(--colorRed); background: #fff;}
		}
	}

	.cd-main{
		position: relative; display: block;
		&:before{
			.pseudo(50%,auto); top: 0; left: auto; right: 0; bottom: 0; background: var(--colorLightBlueBackground);
			@media (max-width: @m){display: none;}
		}
	}
	.cd-main-wrapper{
		position: relative; display: flex; z-index: 1;
		@media (max-width: @m){flex-flow: column; padding: 0;}
	}
	.cd-col{
		position: relative; display: block; width: 50%;
		@media (max-width: @m){width: 100%;}
	}
	.cd-col1{
		padding: 50px 80px 88px 0;
		@media (max-width: @t){padding: 45px 48px 50px 0;}
		@media (max-width: @m){
			padding: 20px 16px 40px;
			.bc{padding-bottom: 28px;}
		}
	}
	.cd-col2{
		padding: 40px 0 88px 72px;
		@media (max-width: @t){padding: 48px 0 50px 48px;}
		@media (max-width: @m){padding: 0 15px;}
	}
	.cd-col2-main{
		position: sticky; top: 40px;
		@media (max-width: @m){position: relative; top: auto;}
	}
	.cd-col2-middle{
		min-height: 85px;
		@media (max-width: @m){min-height: 0;}
		&.has-discount{
			min-height: 100px;
			@media (max-width: @m){min-height: 0;}
		}
	}
	.cd-images{
		position: relative; display: block; min-height: 560px;
		@media (max-width: @m){min-height: 315px;}
	}
	.cd-hero-slider{position: relative; display: block;}
	.cd-hero-slide{
		width: 100%; height: auto; opacity: 0; visibility: hidden;
		&.swiper-slide-active{opacity: 1; visibility: visible;}
		a, iframe{
			display: flex; align-items: center; justify-content: center; width: 100%; height: 100%; min-height: 450px;
			@media (max-width: @t){min-height: 300px;}
			@media (max-width: @m){min-height: 250px;}
		}
		:deep(span){display: flex; align-items: center; justify-content: center; width: 100%; height: 100%;}
		:deep(figure){display: flex; align-items: center; justify-content: center; width: 100%!important; height: 100%;}
		.single{
			:deep(.splide__pagination){display: none;}
		}
		:deep(img){display: block; width: auto; height: auto;}
		@media (max-width: @t){
			:deep(p){padding-bottom: 0; display: flex; align-items: center; justify-content: center; width: 100%; height: 100%;}
			:deep(iframe){display: block; width: 100%;}
		}
	}
	.cd-thumbs{
		position: relative; display: block; margin-top: 6px;
		:deep(.swiper-wrapper){display: flex; gap: 6px; flex-wrap: wrap;}
		@media (max-width: @t){margin-top: 8px;}
	}
	.cd-thumb{
		position: relative; display: flex; align-items: center; justify-content: center; border: 1px solid var(--colorBorderLightForms); border-radius: var(--inputBorderRadius); padding: 5px; cursor: pointer; width: calc(~"16.6666% - 3px")!important; flex-grow: 0; flex-shrink: 0; aspect-ratio: 1; .transition(border-color);
		&.swiper-slide-thumb-active{border-color: var(--colorBaseBlue);}
		@media (min-width: @h){
			&:hover{border-color: var(--colorBaseBlue);}
		}
		:deep(img){display: block; width: auto; height: auto; max-width: 100%;}
		&.video{
			&:before{
				.pseudo(32px,32px); background: url('/assets/images/custom-icons/play.svg') center no-repeat; border-radius: 50%; top: calc(~"50% - 16px"); left: calc(~"50% - 16px"); display: flex; align-items: center; justify-content: center;
				@media (max-width: @t){width: 23px; height: 23px; top: calc(~"50% - 11.5px"); left: calc(~"50% - 11.5px");}
			}
		}
	}
	.cd-col2-top{
		.bc{padding-bottom: 16px;}
	}
	.cd-title{
		font-size: var(--fontSizeSpecial); padding: 0 170px 10px 0;
		@media (max-width: @m){font-size: 17px; line-height: 1.5; padding: 0 0 7px;}
	}
	.cd-manufacturer{
		max-width: 160px; float: right; position: relative; z-index: 1;
		@media (max-width: @m){float: none; max-width: 120px; padding-bottom: 5px;}
		a{display: block;}
		:deep(img){display: block; width: auto; height: auto; max-width: 100%;}
	}
	.cd-code-availability{
		position: relative; display: flex; margin-bottom: 22px; font-size: var(--fontSizeSmall); line-height: 19px;
		@media (max-width: @m){font-size: 13px;}
	}
	.cd-availability{
		position: relative; display: block; padding-left: 21px; margin-left: 10px; font-weight: bold;
		&.available{
			color: var(--colorGreen);
			&:before{.pseudo(17px,17px); background: var(--colorGreen); display: flex; align-items: center; justify-content: center; .icon-check(); font: 8px/9px var(--fonti); color: var(--colorWhite); top: 1px; left: 0; border-radius: 50%;}
		}
		&.not-available{
			color: var(--colorRed);
			&:before{.pseudo(16px,16px); background: url(/assets/images/custom-icons/not-available.svg) center no-repeat; background-repeat: no-repeat; top: 2px; left: 0; border-radius: 50%;}
		}
		&.in-comming{
			color: var(--colorOrange);
			&:before{.pseudo(16px,16px); background: url(/assets/images/custom-icons/in-comming.svg) center no-repeat; background-repeat: no-repeat; top: 1px; left: 0; border-radius: 50%;}
		}
		@media (max-width: @m){margin-left: 16px;}
	}
	.cd-content-intro{
		display: block; position: relative;
		&.custom{
			max-height: 69px; overflow: hidden;
			&:before{
				.pseudo(100%,40px); background: linear-gradient(180deg, rgba(255,255,255,0) 0%, var(--colorLightBlueBackground) 100%); bottom: 0; left: 0;
				@media (max-width: @m){background: linear-gradient(180deg, rgba(255,255,255,0) 0%, #fff 100%);}
			}
			p{display: inline-block; text-overflow: ellipsis;}
			@media (max-width: @m){
				font-size: 13px;
			}
		}
	}
	.cd-content-intro-btn{
		position: relative; display: block; font-size: var(--fontSizeSmall); color: var(--colorBaseBlue); text-decoration: 1px underline; text-underline-offset: 4px; transition: text-decoration-color 0.3s; cursor: pointer; margin-top: 11px;
		span{
			position: relative; padding-right: 14px;
			&:before{.pseudo(8px,8px); .icon-arrow2(); font: 8px/8px var(--fonti); color: var(--colorBaseBlue); right: 0; top: 6px; display: flex; align-content: center; justify-content: center; .rotate(-90deg);}
		}
		@media (min-width: @h){
			&:hover{text-decoration-color: transparent;}
		}
		@media (max-width: @m){font-size: 13px; margin-top: 7px;}
	}
	.cd-price{
		@media (max-width: @m){order: 1;}
	}
	.cd-price-container{
		position: relative; display: flex; align-items: center; padding-bottom: 30px;
		@media (max-width: @t){flex-flow: column; align-content: flex-start;}
	}
	.cd-price-container-left{
		position: relative; display: flex; flex-wrap: wrap; align-items: flex-end;
		@media (max-width: @t){width: 100%;}
	}
	.cd-old-price{
		display: block; font-size: var(--fontSizeSmall); line-height: 1.3; text-decoration: line-through;
		@media (max-width: @m){font-size: 13px; line-height: 19px;}
	}
	.cd-current-price{
		display: block; font-size: var(--fontSizeSpecial); line-height: 28px; font-weight: bold;
		&.red{color: var(--colorRed);}
		@media (max-width: @t){font-size: var(--fontSizeH3);}
		@media (max-width: @m){font-size: 19px; line-height: 21px;}
	}
	.cd-tax{
		display: block; font-size: 13px; line-height: 19px; margin-top: 4px; width: 100%;
		@media (max-width: @m){margin-top: 7px; order: 3;}
	}
	.cd-price-saving{
		position: relative; display: block; font-size: 13px; line-height: 22px; font-weight: bold; color: var(--colorWhite); border-radius: var(--inputBorderRadius); padding: 3px 10px; background: var(--colorRed); margin-left: 21px;
		&:before{.pseudo(6px,6px); background: var(--colorRed); .rotate(45deg); top: calc(~"50% - 3px"); left: -3px;}
		@media (max-width: @t){margin-left: 14px; font-size: 11px;}
		@media (max-width: @m){line-height: 17px; order: 2;}
	}
	:deep(.qty-input-container){display: flex; width: 100%; height: 48px; border-radius: var(--inputBorderRadius); border: 1px solid var(--colorBorderLightForms);}
	:deep(.qty-input){
		display: block; height: 100%; border: 0; text-align: center; padding: 0;
		@media (max-width: @m){font-size: 15px;}
	}
	:deep(.qty-btn){
		display: block; height: 100%; font-size: 0; flex: 0 0 36px; position: relative; cursor: pointer; background: var(--colorWhite);
		&:before{ .pseudo(12px,2px); top: 50%; left: 50%; margin: 0 0 0 -6px; background: var(--colorBaseBlue); }
	}
	:deep(.qty-btn-inc){
		&:after{ .pseudo(2px,12px); top: 50%; left: 50%; background: var(--colorBaseBlue); margin: -5px 0 0 -1px;}
	}
	:deep(.qty-status){
		position: absolute; left: -10px; right: -10px; top: 108%; font-size: 11px; line-height: 1.2; text-align: center;
		@media (max-width: @m){background: #fff;}
	}
	:deep(.cd-notifyme-form){
		position: relative; min-height: 40px;
		input{
			border: 0; box-shadow: 0 5px 20px 0 rgba(0,12,26,0.05);
			@media (max-width: @m){border: 1px solid var(--colorBorderLightForms); box-shadow: none;}
		}
	}

	.cd-retired-container{position: relative; display: flex; flex-flow: column; width: 100%;}
	.retired-product-title{
		display: block; font-size: var(--fontSizeSpecial); font-weight: bold; color: var(--colorRed); padding-bottom: 15px;
		@media (max-width: @m){font-size: 19px;}
	}
	.cd-preorder-container{
		position: relative; padding-right: 140px;
		@media (max-width: @t){padding-right: 0;}
	}

	// gift
	.cd-col1-gift, .cd-col2-gift{
		padding-bottom: 160px;
		.cd-images :deep(img){box-shadow: 0 18px 34px rgba(0,0,0,0.07);}
		.cd-hero-slider{overflow: inherit;}
		@media (max-width: @t){padding-bottom: 50px;}
		@media (max-width: @m){
			padding-bottom: 40px;
			:deep(.bc){padding-bottom: 23px;}
		}
	}
	.cd-content-intro-container{padding-bottom: 35px;}
	.page-catalog-detail-gifts .cd-content-intro-container{
		&.active{
			.cd-content-intro.custom{
				max-height: inherit; overflow: inherit;
				&:before{display: none;}
			}
			.cd-content-intro-btn{display: none;}
		}
	}
	.cd-tab-body{
		position: relative; display: block; width: 100%;
		@media (max-width: @m){padding: 0 15px;}
	}
	.faq-more-info{
		position: relative; display: flex; align-items: center; gap: 40px; background: var(--colorBaseBlue); width: 100%; height: auto; border-radius: var(--inputBorderRadius); color: var(--colorWhite); padding: 40px; overflow: hidden; margin-top: 12px;
		&:before{.pseudo(182px,152px); background: url('/assets/images/bike.svg') center no-repeat; background-size: cover; top: -11px; right: -20px;}
		@media (max-width: @m){
			margin-top: 40px; border-radius: 0; width: calc(~"100% - -30px"); margin-left: -15px; padding: 22px 15px 30px; flex-flow: column; align-items: flex-start; gap: 8px;
			&:before{top: -2px; right: -30px;}
		}
	}
	:deep(.faq-item:first-child){border-top: 1px solid var(--colorBorderLightForms);}
	.faq-more-info-title{
		display: block; font-size: 15px; line-height: 19px; flex-grow: 0; flex-shrink: 0; width: 245px;
		:deep(strong){font-size: 19px; line-height: 25px;}
		:deep(p){
			padding-bottom: 4px;
			&:last-child{padding-bottom: 0;}
		}
	}
	.faq-more-info-contact{
		position: relative; display: flex; flex-flow: column; width: 100%; gap: 9px; justify-content: flex-end; align-items: flex-end;
		:deep(a){color: var(--colorWhite); font-size: 15px; line-height: 19px; font-weight: bold; text-underline-offset: 5px;}
		@media (max-width: @m){flex-flow: column; align-items: flex-start; gap: 4px;}
	}
	.cd-price.custom{
		flex-grow: 0; flex-shrink: 0; margin-left: 15px; margin-right: 50px;
		.cd-current-price{font-size: var(--fontSize); line-height: 1.2; font-weight: bold;}
	}
	.cd-qty-container{position: relative; display: block; width: 104px; flex-grow: 0; flex-shrink: 0; margin-right: 8px;}
	.wp-unit{position: absolute; left: 36px; right: 36px; bottom: 1px; text-align: center; font-size: 10px; text-transform: lowercase;}
	.cd-btn-add{
		display: flex; align-items: center; justify-content: center; width: 100%; flex-grow: 1; flex-shrink: 1; cursor: pointer; box-shadow: none; white-space: nowrap;
		span{
			position: relative; display: block; padding-left: 32px;
			&:before{.pseudo(20px,20px); .icon-cart(); font: 20px/20px var(--fonti); color: var(--colorWhite); top: 0; left: 0;}
		}
		@media (max-width: @t){width: calc(~"100% - 112px");}
	}

	:deep(.add-to-cart-bar-container){
		.cd-add-to-cart-container{
			padding: 0 0 0 50px;
			@media (max-width: @t){padding: 0 0 0 25px;}
			@media (max-width: @m){padding: 0;}
		}
		.cd-price-container{padding: 0;}
		.cd-price-saving, .cd-installment-payment, .cd-tax, .cd-not-available, .cd-compare-container{display: none!important;}
		.cd-current-price{
			font-size: 16px; line-height: 1.2;
			@media (max-width: @m){font-size: 15px;}
		}
		.cd-old-price{
			font-size: 12px;
			@media (max-width: @m){font-size: 11px;}
		}
		.cd-btn-add{
			min-width: 305px;
			@media (max-width: @t){min-width: 0;}
			@media (max-width: @m){
				padding: 0; width: 40px; height: 40px; font-size: 0;
				span{padding: 0; width: 22px; height: 22px;}
			}
		}
		@media (max-width: @t){
			.cd-btn-select-variation{width: auto;}
		}
		@media (max-width: @m){
			.cd-qty-container{width: 86px;}
			.wp-unit{left: 34px; right: 34px;}
			.qty-input-container{height: 40px;}
			.qty-btn{flex: 0 0 30px;}
			.cd-btn-select-variation{font-size: 15px; padding: 0 20px;}
		}
		.cd-price-container{
			margin: 0; display: block; white-space: nowrap; text-align: right;
			@media (max-width: @m){flex-grow: 1; text-align: left;}
		}
	}
</style>
