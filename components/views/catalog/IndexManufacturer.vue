<template>
	<BaseCmsPage v-slot="{page}">
		<Body class="page-manufacturers" />

		<div class="manufacturers-header">
			<div class="wrapper manufacturers-header-wrapper">
				<CmsBreadcrumbs v-if="page?.breadcrumbs" :items="page.breadcrumbs" />
				<h1 v-if="page?.title">{{ page.title }}</h1>
			</div>

			<BaseCatalogManufacturers :fetch="{special: 1, limit: 8, sort: 'position_h'}" v-slot="{items}">
				<ClientOnly>
					<CatalogManufacturers class="manufactures-index" :items="items" v-if="items?.length" />
				</ClientOnly>
			</BaseCatalogManufacturers>
		</div>

		<BaseCatalogManufacturers :fetch="{hierarchy_by: 'alphabet', limit: 0, sort: 'title'}" v-slot="{items}">
			<ClientOnly>
				<div class="m-alphabet">
					<div class="wrapper">
						<div class="ma-items">
							<div v-for="manufacturer in items" :key="manufacturer.alphabet" @click="scrollTo(manufacturer.alphabet)" class="ma-item">
								<span>{{ manufacturer.alphabet }}</span>
							</div>
						</div>
					</div>
				</div>
			</ClientOnly>

			<div class="m-main">
				<div class="wrapper">
					<div class="m-items">
						<template v-if="items?.length">
							<div class="m-column" v-for="manufacturer in items" :key="manufacturer.alphabet" :class="{single: manufacturer.items?.length === 1}">
								<div class="m-letter" :id="manufacturer.alphabet">
									<span>{{ manufacturer.alphabet }}</span>
								</div>
								<div class="m-list-section">
									<ul class="m-list">
										<li v-for="item in manufacturer.items" :key="item.id">
											<NuxtLink :to="item.url_without_domain">
												<span class="m-item-title">{{ item.title }}</span>
												<span class="m-item-counter">{{item.total}}</span>
											</NuxtLink>
										</li>
									</ul>
								</div>
							</div>
						</template>
						<template v-else>
							<BaseCmsLabel code="no_manufacturers" />
						</template>
					</div>
				</div>
			</div>
		</BaseCatalogManufacturers>
	</BaseCmsPage>
</template>

<script setup>
	function scrollTo(index){
		let el = '#' + index;
		document.querySelector(el).scrollIntoView({behavior: 'smooth'});
	}

	const {insertAfter, onMediaQuery} = useDom();

	onMediaQuery({
		query: '(max-width: 990px)',
		timeout: 200,
		enter: () => {
			insertAfter('.manufactures-index', '.m-alphabet');
		}
	});
</script>

<style lang="less" scoped>
	.manufacturers-header{
		position: relative; display: block; width: 100%; background: var(--colorLightBlueBackground); padding: 57px 0 40px;
		h1{padding-bottom: 0;}
		:deep(.bc-last){padding-right: 0;}
		@media (max-width: @m){
			background: var(--colorWhite); padding: 24px 0 22px;
			:deep(.wrapper-bc:after){display: none;}
			.manufactures-index{display: none;}
		}
	}
	.manufacturers-header-wrapper{
		display: flex; flex-flow: column; align-items: center; justify-content: center;
		@media (max-width: @m){align-items: flex-start; justify-content: flex-start;}
	}
	.m-alphabet{
		position: relative; display: block; width: 100%; height: 58px; border-top: 1px solid var(--colorBorderLightForms); border-bottom: 1px solid var(--colorBorderLightForms);
		.wrapper{height: 100%;}
		@media (max-width: @m){
			height: 49px;
			.wrapper{padding: 0;}
			&:after{.pseudo(52px,100%); background: linear-gradient(270deg, #FFFFFF 0%, rgba(255,255,255,0.98) 25.71%, rgba(255,255,255,0) 100%); top: 0; right: 0;}
		}
	}
	.manufactures-index{
		//@media (max-width: @m){display: none!important;}
	}
	.manufactures-index-m{
		display: none!important;
		//@media (max-width: @m){display: flex!important;}
	}
	.ma-items{
		display: flex; justify-content: center; height: 100%;
		@media (max-width: @m){
			justify-content: flex-start; padding: 0 8px; overflow-x: auto;
			&::-webkit-scrollbar {-webkit-appearance: none; height: 0; background: transparent;}
			&::-webkit-scrollbar-thumb {background-color: transparent;}
		}
	}
	.ma-item{
		position: relative; display: flex; align-items: center; justify-content: center; color: var(--colorBase); text-decoration: none; padding: 0 14px; max-width: 56px; flex-grow: 1; flex-shrink: 1; cursor: pointer; .transition(color);
		@media (min-width: @h){
			&:hover{color: var(--colorBaseBlue);}
		}
		@media (max-width: @m){
			font-size: 15px; max-width: auto; flex-grow: 0; flex-shrink: 0; padding: 0 8px;
			&:last-child{z-index: 2;}
		}
	}
	.m-main{
		position: relative; display: block; padding: 34px 0 88px;
		.wrapper{width: 620px;}
		@media (max-width: @m){
			padding: 0;
			.wrapper{padding: 0; width: 100%;}
		}
	}
	.m-items{display: flex; flex-flow: column;}
	.m-column{
		display: flex; align-items: flex-start; width: 100%; border-bottom: 1px solid var(--colorBorderLightForms); margin-bottom: 14px; padding-bottom: 23px;
		&:last-child{margin-bottom: 0; border-bottom: 0; padding-bottom: 0;}
		&.single{align-items: center; padding-bottom: 14px;}
		@media (max-width: @m){
			padding: 19px 16px; margin: 0; flex-flow: column;
			&.single{padding-bottom: 19px; align-items: flex-start;}
		}
	}
	.m-letter{
		display: block; font-size: 35px; line-height: 1.25; font-weight: bold; text-transform: uppercase; width: 70px; flex-grow: 0; flex-shrink: 0;
		@media (max-width: @m){margin-bottom: 11px;}
	}
	.m-list-section{position: relative; display: block; width: 100%; flex-grow: 1; flex-shrink: 1;}
	.m-list{
		display: block; list-style: none;
		li{display: block; padding: 0; margin: 0;}
		a{
			display: flex; align-items: flex-start; font-size: var(--fontSizeSmall); text-transform: uppercase; text-decoration: none; color: var(--colorBase); padding: 7px 16px; transition: background 0.3s, color 0.3s; border-radius: var(--inputBorderRadius);
			@media (min-width: @h){
				&:hover{
					background: var(--colorLightBlueBackground); color: var(--colorBaseDarker);
					.m-item-counter{color: var(--colorBase);}
					.m-item-title{color: var(--colorBaseBlue);}
				}
			}
		}
		@media (max-width: @m){
			li{padding: 0 0 16px;}
			a{padding: 0; font-size: 13px; line-height: 16px; }
		}
	}
	.m-item-title{width: 100%; flex-grow: 1; flex-shrink: 1; color: var(--colorBase); text-decoration: none; text-underline-offset: 3px; .transition(color);}
	.m-item-counter{width: 37px; margin-left: 15px; flex-grow: 0; flex-shrink: 0; text-align: right; color: var(--colorTextLightGray); .transition(color);}
</style>
