<template>
	<CatalogDetail>
		<template #col2="{item}">
			<ClientOnly>
				<div class="cd-add-to-cart-gift-container" v-if="item.is_available">
					<BaseCmsLabel code="gift_send_options_title" tag="div" class="cd-add-to-cart-gift-title" />
					<div class="gift-send-options">
						<BaseCatalogGiftCardForm v-slot="{emailFields, postFields, values, meta}" class="form-animated-label">
							<div class="gift-send-option">
								<BaseFormField v-for="field in emailFields" :key="field.name" :item="field" v-slot="{errorMessage, floatingLabel}">
									<div class="field" :class="['field-'+field.name, {'ffl-floated': floatingLabel}]" v-if="field.name == 'type'">
										<BaseFormInput :id="`gift-email-${field.name}`" />
										<label :for="`gift-email-${field.name}`">
											<BaseCmsLabel :code="(field.name == 'type') ? 'gift_type_email' : 'gift_'+field.name" />
											<template v-if="field.name == 'type'">
												<BaseCmsLabel class="gift-option-description" tag="div" code="gift_type_email_description" />
											</template>
										</label>
										<span class="error" v-show="errorMessage" v-html="errorMessage" />
									</div>
								</BaseFormField>
								<div class="gift-form" v-if="values.type == 'email'">
									<BaseFormField v-for="field in emailFields" :key="field.name" :item="field" v-slot="{errorMessage, floatingLabel}">
										<div class="field" :class="['field-'+field.name, {'ffl-floated': floatingLabel}]" v-if="field.name != 'type'">
											<BaseFormInput :id="`gift-email-${field.name}`" />
											<label :for="`gift-email-${field.name}`">
												<BaseCmsLabel :code="(field.name == 'type') ? 'gift_type_email' : 'gift_'+field.name" />
											</label>
											<span class="error" v-show="errorMessage" v-html="errorMessage" />
										</div>
									</BaseFormField>
								</div>
							</div>

							<div class="gift-send-option">
								<BaseFormField v-for="field in postFields" :key="field.name" :item="field" v-slot="{errorMessage, floatingLabel}">
									<div class="field" :class="['field-'+field.name, {'ffl-floated': floatingLabel}]" v-if="field.name == 'type'">
										<BaseFormInput :id="`gift-post-${field.name}`" />
										<label :for="`gift-post-${field.name}`">
											<BaseCmsLabel :code="(field.name == 'type') ? 'gift_type_address' : 'gift_'+field.name" />
											<template v-if="field.name == 'type'">
												<BaseCmsLabel class="gift-option-description" tag="div" code="gift_type_address_description" />
											</template>
										</label>
										<span class="error" v-show="errorMessage" v-html="errorMessage" />
									</div>
								</BaseFormField>
								<div class="gift-form" v-if="values.type == 'address'">
									<BaseFormField v-for="field in postFields" :key="field.name" :item="field" v-slot="{errorMessage, floatingLabel}">
										<div class="field" :class="['field-'+field.name, {'ffl-floated': floatingLabel}]" v-if="field.name != 'type'">
											<BaseFormInput :id="`gift-post-${field.name}`" />
											<label :for="`gift-post-${field.name}`">
												<BaseCmsLabel :code="(field.name == 'type') ? 'gift_type_address' : 'gift_'+field.name" />
											</label>
											<span class="error" v-show="errorMessage" v-html="errorMessage" />
										</div>
									</BaseFormField>
								</div>
							</div>

							<BaseWebshopAddToCart v-slot="{onAddToCart, loading}" :data="{modalData: item, shopping_cart_code: item.shopping_cart_code, quantity: 1, couponrecipient: values}">
								<div class="cd-add-to-cart-container">
									<button type="submit" class="btn btn-green cd-btn-add" :class="{'loading': loading}" @click="onAddToCart" :disabled="!meta.valid || loading">
										<UiLoader v-if="loading" /><span><BaseCmsLabel code="add_to_shopping_cart" /></span>
									</button>
								</div>
							</BaseWebshopAddToCart>
						</BaseCatalogGiftCardForm>
					</div>
				</div>
			</ClientOnly>
		</template>

		<template #aftermain="{item}">
			<BaseCatalogProductsWidget :fetch="{category_id: item.category_id, related_item_id: item.id, id_exclude: item.id, only_with_image: true, only_available: true}" v-slot="{items}">
				<BaseFaqQuestions :fetch="{catalogproduct_id: item.id, catalogcategory_id: item.category_id, category_code: 'faq_product', limit: 100}" mode="product" v-slot="{items: faqItems}">
					<div class="cd-gift-aftermain" v-if="items?.length || faqItems?.length">
						<div class="wrapper cd-gift-aftermain-wrapper">
							<div class="cd-col cd-col1">
								<div class="cd-related-gift-items-container" v-if="items?.length">
									<div class="other-gift-cards-title"><BaseCmsLabel code="gift_other_products" /></div>
									<div class="cd-related-gift-items">
										<CatalogIndexEntryGift :products="items" />
									</div>
								</div>

								<CmsShare />
							</div>
							<div class="cd-col cd-col2">
								<div class="cd-tab-body cd-tab-body-faq" id="tab3" v-if="faqItems?.length">
									<div class="cd-tab-body-title"><BaseCmsLabel code="tabs_product_faq" /></div>
									<div class="cd-tab-content faq-content" ref="catalog-faq-content">
										<CmsFaqItem v-for="(faq_item, index) in faqItems" :key="faq_item.id" :item="faq_item" :index="index" />
									</div>
									<ClientOnly>
										<div class="faq-more-info">
											<BaseCmsLabel code="faq_more_info_title" v-interpolation tag="div" class="faq-more-info-title" />
											<BaseCmsLabel code="faq_more_info_contact" v-interpolation tag="div" class="faq-more-info-contact" />
										</div>
									</ClientOnly>
								</div>
							</div>
						</div>
					</div>
				</BaseFaqQuestions>
			</BaseCatalogProductsWidget>
		</template>
	</CatalogDetail>
</template>

<style lang="less" scoped>
	.cd-gift-aftermain{
		position: relative; display: block; border-top: 1px solid var(--colorBorderLightForms);
		@media (max-width: @t){
			.faq-more-info{flex-flow: column; align-items: flex-start; justify-content: flex-start; gap: 16px;}
			.faq-more-info-contact{align-items: flex-start; justify-content: flex-start;}
		}
		@media (max-width: @m){border-top: 0;}
	}
	.cd-col{
		position: relative; display: block; width: 50%;
		@media (max-width: @m){width: 100%;}
	}
	.cd-col1{
		padding: 50px 80px 88px 0;
		@media (max-width: @t){padding: 45px 48px 50px 0;}
		@media (max-width: @m){
			padding: 24px 16px 40px;
			.bc{padding-bottom: 28px;}
		}
	}
	.cd-col2{
		padding: 40px 0 88px 72px;
		@media (max-width: @t){padding: 48px 0 50px 48px;}
		@media (max-width: @m){padding: 0 15px;}
	}
	.cd-gift-aftermain-wrapper{
		display: flex;
		.cd-col1{
			display: flex; flex-flow: column;
			@media (max-width: @t){padding-top: 48px;}
		}
		@media (max-width: @m){
			flex-flow: column;
			.cd-col{padding: 0;}
			.cd-col1{flex-flow: column-reverse;}
			.faq-more-info{display: none;}
			:deep(.share){padding: 0 0 40px;}
			.cd-tab-body-faq{width: calc(~"100% - -32px"); margin-left: -16px;}
			.faq-content{width: calc(~"100% - -30px"); margin-left: -15px;}
			.cd-tab-body-title{padding-bottom: 10px;}
			.cd-related-gift-items-container{padding-bottom: 40px;}
		}
	}
	.cd-related-gift-items-container{height: 100%; flex-grow: 1; flex-shrink: 1;}
	.cd-related-gift-items{
		display: flex; flex-flow: column; gap: 16px;
		@media (max-width: @m){gap: 8px;}
	}
	.cd-tab-body{
		position: relative; display: block; width: 100%;
		@media (max-width: @m){padding: 0 15px;}
	}
	.other-gift-cards-title, .cd-tab-body-title{
		display: block; font-size: var(--fontSizeSpecial); line-height: var(--fontSizeH3); font-weight: bold; padding-bottom: 16px; color: var(--colorBaseBlue);
		@media (max-width: @t){font-size: var(--fontSizeH1); font-size: 19px; line-height: 21px; padding-bottom: 8px; color: var(--colorBaseBlue);}
	}
	.other-gift-cards-title{
		@media (max-width: @m){padding-bottom: 16px;}
	}
	.cd-btn-add{
		width: 100%; margin-top: 20px;
		span{
			position: relative; display: inline-block; padding-left: 32px;
			&:before{.pseudo(20px,20px); .icon-cart(); font: 20px/20px var(--fonti); color: var(--colorWhite); top: 0; left: 0;}
		}
		&.loading span{opacity: 0;}
		@media (max-width: @m){margin: 20px 0 40px;}
	}
	.faq-more-info{
		position: relative; display: flex; align-items: center; gap: 40px; background: var(--colorBaseBlue); width: 100%; height: auto; border-radius: var(--inputBorderRadius); color: var(--colorWhite); padding: 40px; overflow: hidden; margin-top: 12px;
		&:before{.pseudo(182px,152px); background: url('/assets/images/bike.svg') center no-repeat; background-size: cover; top: -11px; right: -20px;}
		@media (max-width: @m){
			margin-top: 40px; border-radius: 0; width: calc(~"100% - -30px"); margin-left: -15px; padding: 22px 15px 30px; flex-flow: column; align-items: flex-start; gap: 8px;
			&:before{top: -2px; right: -30px;}
		}
	}
	:deep(.faq-item:first-child){border-top: 1px solid var(--colorBorderLightForms);}
	.faq-more-info-title{
		display: block; font-size: 15px; line-height: 19px; flex-grow: 0; flex-shrink: 0; width: 245px;
		:deep(strong){font-size: 19px; line-height: 25px;}
		:deep(p){
			padding-bottom: 4px;
			&:last-child{padding-bottom: 0;}
		}
	}
	.faq-more-info-contact{
		position: relative; display: flex; flex-flow: column; width: 100%; gap: 9px; justify-content: flex-end; align-items: flex-end;
		:deep(a){color: var(--colorWhite); font-size: 15px; line-height: 19px; font-weight: bold; text-underline-offset: 5px;}
		@media (max-width: @m){flex-flow: column; align-items: flex-start; gap: 4px;}
	}
	:deep(.share){padding: 30px 0 0;}

	// gift coice and form
	.cd-add-to-cart-gift-container{
		max-width: 430px;
		@media (max-width: @m){max-width: 100%;}
	}
	.cd-add-to-cart-gift-title{
		display: block; font-weight: bold; margin-bottom: 18px;
		@media (max-width: @m){margin-bottom: 12px;}
	}
	.gift-form{padding-left: 32px; padding-bottom: 10px;}
	.gift-send-option{
		input[type=radio]+label{
			font-size: var(--fontSize); font-weight: normal; padding-top: 2px; padding-left: 34px;
			&:before{background: var(--colorWhite);}
			@media (max-width: @m){padding-left: 32px;}
		}
	}
	.gift-option-description{
		display: block; font-size: var(--fontSizeSmall);
		:deep(p){padding: 0;}
		@media (max-width: @m){font-size: 13px;}
	}
</style>
