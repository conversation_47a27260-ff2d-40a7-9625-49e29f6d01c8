<template>
	<BaseCatalogCategory v-slot="{item: category, contentType}" :seo="true">
		<div class="gift-card-main">
			<div class="wrapper gift-card-main-wrapper">
				<div class="gift-card-col gift-card-col1">
					<div class="gift-card-header">
						<CmsBreadcrumbs v-if="category?.breadcrumbs && contentType != 'search'" :items="category.breadcrumbs" />
						<h1>{{category.title}}</h1>
					</div>
					<BaseCatalogProducts v-slot="{items: products}" :infinite-scroll="0">
						<div class="gift-card-items" v-if="products?.length">
							<CatalogIndexEntryGift :products="products" />
						</div>

						<div class="gift-card-items-empty" v-else><BaseCmsLabel code="no_products" /></div>
					</BaseCatalogProducts>
				</div>
				<div class="gift-card-col gift-card-col2">
					<div class="gift-card-content-container">
						<div class="gift-card-content" v-interpolation v-html="category.content" v-if="category.content"></div>
						<ClientOnly>
							<BaseUiImage :src="category.main_image_upload_path" loading="lazy" v-if="category.main_image_upload_path" :alt="category.title" />
						</ClientOnly>
					</div>
				</div>
			</div>
		</div>
	</BaseCatalogCategory>
</template>

<style lang="less" scoped>
	// gift card index
	.gift-card-main{
		position: relative; display: block; overflow: hidden;
		@media (max-width: @ms){padding: 24px 0 48px;}
	}
	.gift-card-main-wrapper{
		display: flex;
		@media (max-width: @ms){flex-flow: column; gap: 24px;}
	}
	.gift-card-col{
		width: 50%; padding: 56px 0 90px;
		@media (max-width: @t){padding: 40px 0 70px;}
		@media (max-width: @ms){padding: 0; width: 100%;}
	}
	.gift-card-col1{
		padding-right: 88px;
		@media (max-width: @t){padding-right: 40px;}
		@media (max-width: @ms){padding-right: 0;}
	}
	.gift-card-col2{
		position: relative; display: block; padding-left: 88px; padding-top: 75px;
		&:before{.pseudo(auto,auto); background: var(--colorLightBlueBackground); top: 0; right: calc(~"-100vw - 1280px"); bottom: 0; left: 0;}
		@media (max-width: @t){padding-left: 40px;}
		@media (max-width: @ms){
			padding-top: 0; padding-left: 0;
			&:before{display: none;}
		}
	}
	.gift-card-content-container{
		position: relative; display: flex; flex-flow: column; z-index: 1;
		img{display: block; width: auto; height: auto; max-width: 100%;}
	}
	.gift-card-content{
		display: block; margin-bottom: 17px;
		@media (max-width: @t){margin-bottom: 0;}
		@media (max-width: @ms){font-size: 13px; line-height: 20px; margin-bottom: 11px;}
	}
	.gift-card-items{
		display: flex; flex-flow: column; gap: 16px;
		@media (max-width: @ms){gap: 8px;}
	}
</style>

<style lang="less">
	.active-filters{
		overflow: hidden;
		#__nuxt .c-filters-section{display: block;}
	}
</style>
