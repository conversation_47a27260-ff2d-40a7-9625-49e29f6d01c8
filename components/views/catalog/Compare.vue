<template>
	<BaseCmsPage>
		<ClientOnly>
			<BaseCatalogCompare v-slot="{items, onRemove, attributes, differentAttributes, onToggleMode, mode, loading}">
				<Body :class="{'has-compare-items': items?.length}" />
				<div class="compare-cnt" :class="{'loading': loading}">
					<div class="wrapper compare-header">
						<BaseCmsLabel code="compare_products_title" tag="h1" class="compare-title" />
					</div>
					<div class="wrapper wrapper-compare">
						<div class="compare-items-container">
							<div class="compare-items" ref="compareItems" @scroll="onScroll('items')">
								<template v-if="items?.length">
									<div v-for="item in items" :key="item.id" class="compare-item">
										<div class="cp-compare-header-cnt">
											<div class="cp-compare-header">
												<BaseThemeCatalogCompareSearch :placeholder="labels.get('compare_add_title')" :item="item" imageField="main_image_thumbs['width150-height150']" no-image="/images/no-image-80.jpg" />
											</div>
										</div>
										<CatalogIndexEntry :item="item" mode="compare" />
									</div>
								</template>
								<template v-if="items?.length < 5">
									<div v-for="index in 5 - items.length" :key="index" class="compare-item">
										<div class="cp-compare-header-cnt">
											<div class="cp-compare-header">
												<BaseThemeCatalogCompareSearch :placeholder="labels.get('compare_add_title')" imageField="main_image_thumbs['width150-height150']" no-image="/images/no-image-80.jpg" />
											</div>
										</div>
										<div class="cp">
											<div class="cp-compare-image-empty">
												<img loading="lazy" src="/images/no-image-compare.jpg" alt="" />
											</div>
										</div>
									</div>
								</template>
							</div>
						</div>

						<div v-if="items?.length" class="compare-table-header">
							<h3 class="compare-table-title"><BaseCmsLabel code="product_specifications" /></h3>
							<template v-if="items.length > 1">
								<span v-if="mode == 'difference'" @click="onToggleMode('all')" class="compare-difference-toggle active"><BaseCmsLabel code="compare_differences" /></span>
								<span v-else @click="onToggleMode('difference')" class="compare-difference-toggle"><BaseCmsLabel code="compare_differences" /></span>
							</template>
						</div>

						<div v-if="items?.length" class="compare-table" ref="compareTable" @scroll="onScroll('table')">
							<div v-for="attribute in attributes" :key="attribute.id" class="attribute-row" :class="{'difference': differentAttributes.includes(attribute.attribute_code) && mode == 'difference'}" :data-compare-attribute="attribute.attribute_code">
								<div class="attribute-row-title">{{ attribute.attribute_title }}</div>
								<div class="attribute-row-items">
									<div v-for="item in items" :key="item.id" class="attribute-row-item">
										{{item?.attributes.length && item.attributes.find((obj) => obj.id == attribute.id).title ? item.attributes.find((obj) => obj.id == attribute.id).title : '-'}}
									</div>
									<template v-if="items.length < 5">
										<div v-for="index in 5 - items.length" :key="index" class="attribute-row-item">-</div>
									</template>
								</div>
							</div>
						</div>
					</div>
					<div class="compare-fixed" v-if="items?.length">
						<div class="wrapper wrapper-compare">
							<div class="compare-items" ref="compareFixed" @scroll="onScroll('fixed')">
								<template v-if="items?.length">
									<div v-for="item in items" :key="item.id" class="compare-item compare-fixed-item">
										<div class="compare-fixed-image">
											<BaseUiImage loading="lazy" :data="item.main_image_thumbs?.['width150-height150']" default="/images/no-image-210.jpg" :title="item.main_image_title" :alt="item.main_image_description ? item.main_image_description : item.title" />
										</div>
										<p class="compare-fixed-title">{{limitCharacter(item.title, 30)}}</p>
										<div class="compare-fixed-remove" @click="onRemove(item)"></div>
									</div>
								</template>
								<template v-if="items?.length < 5">
									<div v-for="index in 5 - items.length" :key="index" class="compare-item compare-fixed-item compare-fixed-item-empty">
										<div class="compare-fixed-image-empty">
											<img loading="lazy" src="/images/no-image-compare.jpg" alt="" />
										</div>
									</div>
								</template>
							</div>
						</div>
					</div>
				</div>
			</BaseCatalogCompare>
			<template #fallback>
				<BaseThemeUiLoading />
			</template>
		</ClientOnly>
	</BaseCmsPage>
</template>

<script setup>
	const labels = useLabels();
	const {limitCharacter} = useText();

	const compareItems = ref(null);
	const compareTable = ref(null);
	const compareFixed = ref(null);

	function onScroll(el) {
		if(el == 'items') syncScroll(compareItems, compareTable, compareFixed);
		if(el == 'table') syncScroll(compareTable, compareItems, compareFixed);
		if(el == 'fixed') syncScroll(compareFixed, compareTable, compareItems);
	}

	let isScrolling;
	function syncScroll(source, target, target2) {
		if (source.value && target.value && target2.value) {
			clearTimeout(isScrolling);
			isScrolling = setTimeout(() => {
				if(target.value?.scrollLeft != source.value?.scrollLeft) target.value.scrollLeft = source.value.scrollLeft;
				if(target2.value?.scrollLeft != source.value?.scrollLeft) target2.value.scrollLeft = source.value.scrollLeft;
			}, 10);
		}
	}
</script>

<style lang="less" scoped>
	.compare-cnt{
		padding-bottom: 88px; position: relative;
		@media (max-width: @m){padding-bottom: 0;}
		&.loading{
			pointer-events: none;
			&:before{.pseudo(auto,auto); position: fixed; background: rgba(255,255,255,0.5); top: 0; bottom: 0; left: 0; right: 0; z-index: 11111; opacity: 1; .transition(opacity);}
			&:after{content: ""; width: 64px; height: 64px; position: fixed; left: 50%; top: 50%; margin-left: -32px; margin-top: -32px; background: white url(/assets/images/loader.svg) no-repeat center center; background-size: contain; display: block; z-index: 11111;}
		}
	}
	@media (max-width: @m){
		.wrapper-compare{padding: 0;}
	}
	.compare-title{
		text-align: center; padding: 56px 0 32px;
		@media (max-width: @t){padding: 30px 0;}
		@media (max-width: @m){padding: 24px 0; text-align: left;}
	}
	.compare-items{display: flex; overflow-x: auto;}
	//.compare-items-container{overflow-x: auto;}
	.cp-compare-image-empty{
		text-align: center; padding: 40px 20px 60px;
		@media (max-width: @t){padding: 40px 16px 60px;}
	}
	.compare-item{
		border: 1px solid var(--colorBorderLightForms); border-right: 0; width: 20%; display: flex; flex-direction: column;
		@media (max-width: @m){width: 150px; flex-grow: 0; flex-shrink: 0;}
	}
	.compare-items .compare-item:last-child{border-right: 1px solid var(--colorBorderLightForms);}
	.cp-compare-header-cnt{
		padding: 24px 24px 0;
		@media (max-width: @t){padding: 16px 16px 0;}
	}
	.cp-compare-header{
		background: #fff; position: relative;
		:deep(.field-compare-autocomplete){
			border: none; position: absolute; top: 45px; left: 0; right: 0; z-index: 111; box-shadow: 0 0 20px 0 rgba(0,0,0,0.1);
			@media (max-width: @m){top: 37px; left: -16px; right: -16px;}
		}
		:deep(.field-autocomplete-container){position: static; border: none;}
		:deep(.ui-menu-item){
			display: flex; width: 100%; padding: 10px!important; background: #fff; cursor: pointer;
			.image{
				display: flex; align-items: center; justify-content: center; flex-shrink: 0; width: 50px; height: 50px; margin-right: 10px;
				@media (max-width: @t){width: 34px; height: 34px;}
				img{width: auto; height: auto; max-width: 100%; max-height: 100%; display: block;}
			}
			.title{
				display: block; font-size: 12px; line-height: 1.3;
				@media (max-width: @t){font-size: 11px; word-break: break-word;}
			}
			.price{display: block; margin-top: 2px; font-size: 11px; line-height: 1.4; font-weight: 600;}
			&.active, &:hover{background: var(--colorBorderLightForms);}
		}
		&:before{
			position: absolute; .icon-search(); font: 18px/18px var(--fonti); color: var(--colorBaseBlue); right: 20px; top: 15px;
			@media (max-width: @m){font-size: 16px; line-height: 16px; right: 12px; top: 12px;}
		}
	}
	:deep(input[type="search"]){
		height: 48px; font-size: 14px; background: 0; z-index: 1; position: relative; padding: 0 42px 0 20px;
		@media (max-width: @m){height: 40px; font-size: 12px; padding: 0 30px 0 14px;}
	}
	.compare-table-header{
		display: flex; align-items: center; margin: 47px 0 24px;
		@media (max-width: @m){margin: 33px 16px 8px; justify-content: space-between;}
	}
	.compare-table-title{padding: 0 32px 0 0;}
	.compare-difference-toggle{
		font-size: 13px; font-weight: bold; padding: 0 40px 0 0; position: relative; display: block; margin-top: 3px; cursor: pointer;
		&:before{width: 24px; height: 10px; background: var(--colorBorderLightForms); border: 0; border-radius: 5px; left: auto; right: 0; top: 4px; content:""; display: block; position: absolute;}
		&:after{ .pseudo(16px,16px); top: 1px; right: 9px; box-shadow: 0 2px 4px 0 rgba(0,0,0,0.24); border-radius: 100%; background: #fff;}
		&.active{
			&:before{background: var(--colorGreen); }
			&:after{right: -1px; background: var(--colorBaseBlue);}
		}
	}
	.compare-table{
		font-size: 14px; line-height: 1.4; border-bottom: 1px solid var(--colorBorderLightForms);
		@media (max-width: @m){font-size: 13px; line-height: 1.3; overflow-x: auto;}
	}
	.attribute-row{
		display: inline-block; min-width: 100%;
		&.difference{background: var(--colorIconLightBlue);}
	}
	.attribute-row-title{
		background: var(--colorLightBlueBackground); color: var(--colorBaseBlue); font-weight: bold; padding: 10px 24px;
		@media (max-width: @m){padding: 10px 16px;}
	}
	.attribute-row-items{
		display: flex;
		.attribute-row-item:first-child{
			border-left: 0;
			@media (max-width: @m){width: calc(~'20% - -16px');}
		}
	}
	.attribute-row-item{
		width: 20%; padding: 15px 24px; border-left: 1px solid var(--colorBorderLightForms);
		@media (max-width: @m){font-size: 12px; line-height: 1.5; width: 150px!important; flex-grow: 0; flex-shrink: 0; padding: 5px 16px;}
	}
	.compare-fixed{
		position: fixed; left: 0; right: 0; bottom: 0; background: #fff; z-index: 50; box-shadow: 0 -3px 15px 0 rgba(0,0,0,0.15);
		.compare-item{
			border-top: 0; border-bottom: 0; height: 88px; flex-direction: row;;
			@media (max-width: @m){height: 64px;}
		}
		@media (max-width: @m){overflow: auto; -webkit-overflow-scrolling: touch;}
	}
	.compare-fixed-item{
		display: flex; align-items: center; padding: 16px; position: relative;
		@media (max-width: @m){padding: 10px 10px 10px 6px;}
	}
	.compare-fixed-image{
		flex: 0 0 56px; height: 56px; margin-right: 15px; display: flex; align-items: center; justify-content: center;
		@media (max-width: @m){flex: 0 0 38px; height: 38px; margin-right: 6px;}
		img{display: block;}
	}
	.compare-fixed-title{
		font-size: 11px; line-height: 1.3; font-weight: bold; padding: 0 15px 0 0; max-height: 34px; overflow: hidden;
		@media (max-width: @m){padding: 0 18px 0 0; max-height: 43px;}
	}
	.compare-fixed-remove{
		width: 30px; height: 30px; position: absolute; right: 4px; top: 4px; cursor: pointer;
		@media (max-width: @m){width: 28px; height: 30px; top: 0; right: 0;}
		&:before, &:after{ .pseudo(16px,2px); top: 50%; left: 50%; margin-top: -1px; margin-left: -8px; background: var(--colorRed); .rotate(45deg); .transition(background-color,0.3s);}
		&:after{.rotate(-45deg);}
		@media (min-width: @h){
			&:hover{
				&:before, &:after{background: var(--colorBaseBlue);}
			}
		}
	}
	.compare-fixed-item-empty{align-items: center; justify-content: center;}
	.compare-fixed-image-empty{
		display: flex; align-items: center; justify-content: center; height: 40px; overflow: hidden;
		img{max-width: 80%;}
	}
	:deep(.cp-compare.compare_active){
		background: var(--colorLightBlueBackground);
		&:after {color: var(--colorRed); .icon-close(); font-weight: bold; font-size: 11px; padding-top: 1px;}
		&:hover{
			background: var(--colorRed);
			&:after{color: #fff;}
		}
	}
</style>
