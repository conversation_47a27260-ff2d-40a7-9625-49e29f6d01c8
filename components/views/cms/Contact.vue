<template>
	<BaseCmsPage v-slot="{page}">
		<CmsPageLayout>
			<CmsBreadcrumbs v-if="page?.breadcrumbs" :items="page.breadcrumbs" />
			<h1 v-if="page?.seo_h1">{{ page.seo_h1 }}</h1>
			<div class="contact-content flex">
				<div class="content-left">
					<BaseCmsLabel class="support-info" code="support_info" tag="div" />
					<BaseCmsLabel class="social-title" code="social_title" tag="div" />
					<BaseCmsLabel class="social-links" code="social_links" tag="div" />
					<BaseCmsLabel class="locations-link" code="locations_link" tag="div" />
				</div>
				<div class="content-right">
					<BaseCmsLabel class="mbike-title" code="mbike_title" tag="div" />
					<BaseCmsLabel class="mbike-subtitle" code="mbike_subtitle" tag="div" />
					<BaseCmsLabel class="mbike-address" code="mbike_address" tag="div" />
					<div v-if="page?.content" class="cms-content" v-html="page.content" v-interpolation />
				</div>
			</div>

			<template #main_extra>
				<BaseLocationPoints v-slot="{items}">
					<ClientOnly>
						<CmsMap :items="items" />
					</ClientOnly>
				</BaseLocationPoints>
			</template>
		</CmsPageLayout>
	</BaseCmsPage>
</template>

<script setup></script>

<style>
	.page-cms-contact .sidebar .support-info{display: none;}
</style>

<style lang="less" scoped>
	.contact-content{
		display: flex;
		@media (max-width: @m){flex-flow: column;}
	}
	.content-left{
		padding-right: 214px;
		@media (max-width: @m){padding-right: 0;}
	}
	.support-info {
		padding-top: 82px; display: block; position: relative;
		&:before { .pseudo(64px,64px); background: url(assets/images/custom-icons/conversation.svg) no-repeat center center; background-size: contain; left: 0; top: 0; }
		:deep(.support-info-title) { font-size: 24px; line-height: 1.4; font-weight: bold; }
		:deep(.support-info-desc) { display: block; padding-top: 11px; }
		:deep(a) {
			font-weight: bold; text-decoration: none; display: block; color: var(--colorBase);
			&.support-info-mail{
				text-decoration: underline; text-decoration-color: var(--colorBaseBlue); text-underline-offset: 3px; padding-bottom: 5px; .transition(text-decoration-color);
				@media (min-width: @h){
					&:hover{text-decoration-color: transparent;}
				}
			}
		}
		:deep(strong) { font-weight: bold; display: block; }
		@media (max-width: @m){
			padding-top: 64px;
			&:before{width: 56px; height: 56px;}
			:deep(.support-info-title){font-size: 19px; line-height: 1.2;}
			:deep(.support-info-desc){padding-top: 4px; font-size: 13px; line-height: 1.7;}
			:deep(a) {text-decoration: underline; text-decoration-color: var(--colorBaseBlue); text-underline-offset: 3px; padding-bottom: 6px;}
		}
	}
	.social-title {
		font-size: var(--fontSizeSmall); line-height: 1.2; display: block; padding: 21px 0 8px;
		@media (max-width: @m){font-size: 13px; line-height: 1.3; padding: 12px 0 8px;}
	}


	:deep(.social-links){
		position: relative; display: flex; align-items: center;
		a{
			position: relative; display: block; width: 40px; height: 40px; margin-right: 8px; font-size: 0;
			&:before{.pseudo(40px,40px); background: url('/assets/images/social-icons/facebook.svg') center no-repeat; background-size: cover; top: 0; left: 0; opacity: 1; .transition(opacity);}
			@media (min-width: @h){
				&:hover{
					&:before{opacity: 0.5;}
				}
			}
		}
		a[title~="instagram"]{
			&:before{background: url('/assets/images/social-icons/instagram.svg') center no-repeat; background-size: cover;}
		}
		a[title~="youtube"]{
			&:before{background: url('/assets/images/social-icons/youtube.svg') center no-repeat; background-size: cover;}
		}
		a[title~="tiktok"]{
			&:before{background: url('/assets/images/social-icons/tiktok.svg') center no-repeat; background-size: cover;}
		}
		a[title~="linkedin"]{
			&:before{background: url('/assets/images/social-icons/linkedin.svg') center no-repeat; background-size: cover;}
		}
		@media (max-width: @m){
			a{
				width: 32px; height: 32px; margin-right: 7px;
				&:before{width: 32px; height: 32px;}
			}
		}
	}

	.locations-link{
		margin-top: 31px; position: relative;
		:deep(a){
			line-height: 1.4; font-weight: bold; text-decoration: underline; text-decoration-color: transparent; .transition(text-decoration-color);
			span{
				padding-right: 32px; position: relative;
				&:before{.icon-arrow3(); position: absolute; right: 0; top: 0; font: 24px/1 var(--fonti); color: var(--colorBaseBlue);}
			}
			@media (min-width: @h){
				&:hover{text-decoration-color: var(--colorBaseBlue);}
			}
		}
		@media (max-width: @m){
			margin-top: 24px;
			:deep(a){
				line-height: 1.3;
				span:before{top: -2px;}
			}
		}
	}

	.mbike-title{
		padding-top: 82px; font-size: 24px; line-height: 1.4; font-weight: bold;
		@media (max-width: @m){padding-top: 35px; font-size: 19px; line-height: 1.2;}
	}
	.mbike-subtitle{
		font-weight: bold; padding-top: 10px;
		@media (max-width: @m){padding-top: 6px; line-height: 1.4;}
	}
	.mbike-address{
		padding: 5px 0 20px;
		:deep(a){
			font-size: 14px; line-height: 1.3; text-decoration: underline; color: var(--colorBase); text-decoration-color: var(--colorBaseBlue); text-underline-offset: 5px; .transition(text-decoration-color);
			@media (min-width: @h){
				&:hover{text-decoration-color: transparent;}
			}
		}
		@media (max-width: @m){
			padding: 4px 0 12px;
			:deep(a){font-size: 13px;}
		}
	}
	.cms-content{
		font-size: 14px; line-height: 1.4;
		:deep(p){padding-bottom: 6px;}
		@media (max-width: @m){
			font-size: 13px; line-height: 1.3;
			:deep(p){padding-bottom: 8px;}
		}
	}

	.map-content{
		@media (max-width: @m){margin-top: -16px;}
	}
</style>
