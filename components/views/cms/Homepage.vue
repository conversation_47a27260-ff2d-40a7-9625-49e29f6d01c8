<template>
	<BaseCmsPage v-slot="{page}">
		<h1 style="display: none" v-if="page?.title">{{ page.title }}</h1>

		<BaseCatalogCategory :fetch="{code: 'biciklizam', level: 1, response_fields: ['id', 'code', 'position_h', 'related_manufacturers_ids']}" v-slot="{item: categoryItem}">
			<BaseCmsRotator
				:fetch="{code: 'c_lvl_one_intro', limit: 4, catalogcategory_id: categoryItem[0].id, response_fields: ['id','link','url_without_domain','title','title2','content','image_upload_path','image_2_upload_path','image_thumbs','image_2_thumbs','catalogcategory_ids']}"
				v-slot="{items}">
				<CmsIntroSlider v-if="items?.length" :items="items" />
			</BaseCmsRotator>

			<CmsBenefits extclass="special" />
			<BaseCmsNav code="main_categories" :start-position="categoryItem[0].position_h" v-slot="{ items }">
				<CmsHomepageCategories :items="items" :category="categoryItem[0].code" v-if="items?.length">
					<template #header>
						<BaseCmsLabel code="view_categories" tag="div" class="homepage-categories-title" />
					</template>
				</CmsHomepageCategories>
			</BaseCmsNav>

			<BaseCmsRotator v-if="!mobileBreakpoint" :fetch="{code: 'popular_categories', limit: 7, catalogcategory_id: categoryItem[0].id, response_fields: ['id','element_class','link','url_without_domain','image_upload_path','image_thumbs','title', 'catalogcategory_ids']}" v-slot="{items}">
				<CmsCategories :items="items" />
			</BaseCmsRotator>

			<BaseCmsRotator
				:fetch="{code: 'c_lvl_one_promo_first', catalogcategory_id: categoryItem[0].id, limit: 3, response_fields: ['id','template','link','url_without_domain','title2','title','content','image_upload_path','image_2_upload_path','image_thumbs','image_2_thumbs','catalogcategory_ids']}"
				v-slot="{items}">
				<LazyCmsPromo class="homepage-promo-widget homepage-promo-widget1" :items="items" v-if="items" hydrate-on-visible />
			</BaseCmsRotator>

			<LazyCmsHomeSections hydrate-on-visible :categoryItem="categoryItem" />
		</BaseCatalogCategory>

		<BasePublishCategory :fetch="{start_position_with: '01', mode: 'full', hierarhy_by_position: true, response_fields: ['id','code','url_without_domain','children','title','seo_h1']}" v-slot="{item}">
			<BasePublishPostsWidget :fetch="{category_code: item.code, limit: 5}" v-slot="{items}" v-if="item?.code">
				<LazyPublishFeatured :items="items" :rootCategory="item" :homepage="true" hydrate-on-visible />
			</BasePublishPostsWidget>
		</BasePublishCategory>
	</BaseCmsPage>
</template>

<script setup>
	const {onMediaQuery} = useDom();
	const {matches: mobileBreakpoint} = onMediaQuery({
		query: '(max-width: 980px)',
	});
</script>
