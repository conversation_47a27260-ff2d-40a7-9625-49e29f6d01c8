<template>
	<div class="homepage-categories" v-if="items[0].items?.length">
		<div class="wrapper homepage-categories-wrapper">
			<div class="homepage-categories-header">
				<slot name="header" />
			</div>
			<div class="homepage-categories-main">
				<div class="homepage-categories-list" v-for="item in items" :key="item.id">
					<div class="homepage-categories-list-lv2" v-if="item.items?.length">
						<div class="homepage-categories-list-lv3" v-for="item in item.items" :key="item.id">
							<LazyCmsHomepageCategoriesItem :item="item" hydrate-on-visible />
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	const props = defineProps(['items', 'category']);
</script>

<style lang="less">
	.homepage-categories{
		display: none;
		@media (max-width: @m){display: block;}
	}
</style>

<style lang="less" scoped>
	.homepage-categories{position: relative; background: var(--colorLightBlueBackground); padding: 30px 0; margin-bottom: 15px; overflow: hidden;}
	.homepage-categories-header{position: relative; display: block; margin-bottom: 18px;}
	:deep(.homepage-categories-title){display: block; font-size: 23px; line-height: 28px; font-weight: bold; margin-bottom: 5px;}
	.homepage-categories-list-lv3{
		position: relative; display: block; background: var(--colorWhite); margin-bottom: 8px; border-radius: var(--inputBorderRadius);
		&:last-child{margin-bottom: 0;}
	}
</style>
