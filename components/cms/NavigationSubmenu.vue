<template>
	<li
		class="nav-item2"
		:class="[{'has-children': item.items}, {'big': hasProducts}, {'active': (item.url_without_domain == parentCategoryPath) || (item.url_without_domain == route.fullPath)}, {'open': submenuOpen}, item.style, item.code, {'selected': isSelected(item)}]"
		@mouseenter="handleMouseEnter"
		@mouseleave="handleMouseLeave">
		<BaseUiLink native :prevent-default="onPreventDefault(item)" :href="item.url_without_domain" class="nav-lvl2" @click="onClick(item), handleMouseLeave()">
			<span>
				<span v-if="item.image_upload_path" class="img"><BaseUiImage :src="item.image_upload_path" width="36" height="36" /></span>
				<span>{{item.title}}</span>
			</span>
		</BaseUiLink>
		<LazyCmsNavigationSubmenuLevel3 :submenu-open="submenuOpen" :on-products-loaded="onProductsLoaded" :handle-mouse-leave="handleMouseLeave" :item="item" v-if="submenuOpen" />
	</li>
</template>

<script setup>
	const route = useRoute();
	const {onMediaQuery} = useDom();
	const props = defineProps({
		item: Object,
	})

	const {matches: mobileBreakpoint} = onMediaQuery({
		query: '(max-width: 980px)',
	});

	// add css class only if products are available
	const hasProducts = ref(false);
	function onProductsLoaded(data) {
		hasProducts.value = (data.items?.length) ? true : false;
	}

	// delay menu open
	const submenuOpen = ref(false);
	let hoverTimeout;
	function handleMouseEnter() {
		hoverTimeout = setTimeout(() => {
			submenuOpen.value = true;
		}, 100); // 200ms delay
	}
	function handleMouseLeave() {
		if(mobileBreakpoint.value) return;

		clearTimeout(hoverTimeout);
		submenuOpen.value = false;
	}

	// get data provided by parent components
	const {onClick, parentCategoryPath, onPreventDefault, isSelected} = inject('cmsnavigation');
</script>
