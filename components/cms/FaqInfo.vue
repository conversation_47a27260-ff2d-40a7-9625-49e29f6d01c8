<template>
	<BaseCmsLabel class="faq-info" code="faq_info" tag="div" />
</template>

<script></script>

<style lang="less" scoped>
	.faq-info{
		background: var(--colorBaseBlue); position: relative; margin-top: 13px; margin-bottom: 32px; padding: 40px 40px 36px; display: flex; align-items: center; border-radius: var(--inputBorderRadius); min-height: 128px;
		:deep(span){
			font-size: 15px; line-height: 1.3; color: var(--colorWhite); margin-right: 50px; display: block;
			strong{display: block; font-weight: bold; font-size: 19px; line-height: 1.3; padding-bottom: 4px;}
		}
		:deep(a){
			font-size: 15px; line-height: 1.3; font-weight: bold; color: var(--colorWhite); text-decoration: underline; text-underline-offset: 5px; margin-right: 40px; .transition(text-decoration-color);
			&:last-child{margin-right: 0;}
			@media (min-width: @h){
				&:hover{text-decoration-color: transparent;}
			}
		}
		&:after{.pseudo(162px, 128px); right: 0; top: 0; background: url(assets/images/Symbol.svg) no-repeat right top; background-size: contain;}
		@media (max-width: @m){
			order: 3; width: calc(~"100% - -32px"); margin-left: -16px; background: var(--colorLightBlueBackground); border-radius: 0; padding: 24px 16px 32px; min-height: unset; flex-flow: column; align-items: flex-start; margin-bottom: 0; margin-top: 32px;
			:deep(span){
				line-height: 1.4; color: var(--colorBase); margin-right: 0px; padding-top: 64px; position: relative;
				strong{font-size: 19px; line-height: 1.2; padding-bottom: 0px;}
				&:before{.pseudo(56px,56px); background: url(assets/images/custom-icons/conversation.svg) no-repeat center center; background-size: contain; left: 0; top: 0; }
			}
			:deep(a){line-height: 1.4; color: var(--colorBase); text-decoration-color: var(--colorBaseBlue); margin-right: 0; margin-top: 8px;}
			&:after{display: none;}
		}
	}
</style>
