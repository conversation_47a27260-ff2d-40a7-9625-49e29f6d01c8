<template>
	<div class="location-city-container" :id="'item-container-'+place.location_code" :class="{'hide-navigation': place.items.length < 2}">
		<div class="location-city-title" @click="handleClick">{{place.title}} <span class="icon-dropdown"></span></div>
		<div class="location-city-items">
			<BaseUiSwiper class="location-city-items-slider" :name="'location-'+place.location_code" :options="sliderOptions">
				<BaseUiSwiperSlide class="location-city-item" v-for="item in place.items" :key="item.id">
					<div class="location-city-item-image">
						<BaseUiImage loading="lazy" :data="item.main_image_thumbs?.['width504-height320']" :alt="item.title" default="/images/no-image-560.jpg" :picture="[{maxWidth: '980px', src: item.main_image_thumbs?.['width980-height623']?.thumb}]" />
					</div>
					<div class="location-city-item-cnt">
						<div class="location-city-item-title">{{item.title}}</div>
						<div class="location-city-item-address" v-html="item.address" v-if="item.address" @click="scrollToMap(item.location_code)" />
						<div class="location-city-item-contact" v-html="item.contact" v-if="item.contact" v-interpolation />
						<div class="location-city-item-business-hour" v-html="item.business_hour" v-if="item.business_hour" />
					</div>
				</BaseUiSwiperSlide>
			</BaseUiSwiper>
		</div>
	</div>
</template>

<script setup>
	const props = defineProps(['place']);

	function handleClick(event){
		const parent = event.target.parentNode;
		if (parent.classList.contains("active")) {
			parent.classList.remove("active");
		} else {
			parent.classList.add("active");
		}
	}

	function scrollToMap(index){
		const map = '#map-content';
		document.querySelector(map).scrollIntoView({behavior: 'smooth'});
	}

	const sliderOptions = {
		slidesPerView: 1,
		slidesPerGroup: 1,
		loop: props.place?.items?.length > 1 ? true : false,
		navigation: {
			enabled: true,
			wrapperClass: 'splide__arrows',
			nextElClass: 'splide__arrow splide__arrow--next',
			prevElClass: 'splide__arrow splide__arrow--prev'
		},
		breakpoints: {
			980: {
				enabled: false,
			}
		}
	}
</script>

<style lang="less" scoped>
	.location-city-container{
		&.hide-navigation{
			:deep(.swiper-navigation){display: none!important;}
		}
		@media (max-width: @m){
			background: var(--colorLightBlueBackground); transition: background 0.3s; position: relative;
			&.active{
				background: var(--colorWhite);
				.location-city-title{padding-bottom: 12px;}
				.icon-dropdown:after{opacity: 0;}
				:deep(.location-city-items){padding: 0 16px 24px; max-height: inherit;}

			}
		}
	}
	.location-city-title{
		position: relative; display: block; font-size: var(--fontSizeH4); line-height: 1.4; font-weight: bold; margin: 0 55px 24px; padding-left: 40px;
		&:before{.pseudo(25px,34px); background: url(assets/images/custom-icons/pin.svg) center no-repeat; background-size: cover; top: -2px; left: 0;}
		.icon-dropdown{display: none;}
		@media (max-width: @m){
			margin: 0; font-size: 15px; line-height: 21px; padding: 18px 36px 18px 16px;
			&:before{display: none;}
			.icon-dropdown{display: block; right: 16px;}

		}
		/*
		@media (max-width: @ms){
			margin: 0; font-size: 15px; line-height: 21px; padding: 18px 36px 18px 16px;
			&:before{display: none;}
			.icon-dropdown{display: block; right: 16px;}
		}
		*/
	}
	.icon-dropdown{
		display: block; position: absolute; width: 10px; height: 10px; top: 24px; right: 0;
		&:before{.pseudo(10px,2px); background: var(--colorBaseBlue); left: 0; top: calc(~"50% - 1px");}
		&:after{.pseudo(2px,10px); background: var(--colorBaseBlue); left: calc(~"50% - 1px"); top: 0; .transition(opacity);}
	}
	:deep(.location-city-items-slider){
		width: 100%; display: block; margin: 0!important;

	}
	:deep(.location-city-items){
		position: relative; display: flex; flex-flow: column; background: var(--colorWhite); border-radius: var(--inputBorderRadius); box-shadow: 0 10px 40px rgba(0,89,194,0.08); padding: 56px;
		.swiper-wrapper{
			display: flex; flex-flow: column; gap: 56px;
		}
		.swiper-navigation{position: absolute; top: 0; height: 48px; left: 32px; right: 32px; display: block; top: calc(~"((100vw * 0.635) / 2) - 16px - 24px");} // calc: širina screena * aspect ratio slike / 2 - padding jedne strane - visina navigacije->pola visine
		.swiper-button-prev, .swiper-button-next{
			top: 0;
			background: var(--colorWhite); width: 48px; height: 48px; top: calc(~"50% - 24px"); box-shadow: 0 2px 30px 0 rgba(0,12,26,0.17);
			&:before{color: var(--colorBase); font-size: 14px;}
			&.swiper-button-lock{display: none;}
			@media (min-width: @h){
				&:hover{
					background: var(--colorBaseBlue);
					&:before{color: var(--colorWhite);}
				}
			}
		}
		.swiper-button-prev{left: 0;}
		.swiper-button-next{right: 0;}
		/*
		:deep(.splide__arrow){
			background: var(--colorWhite); width: 48px; height: 48px; top: calc(~"50% - 24px"); box-shadow: 0 2px 30px 0 rgba(0,12,26,0.17);
			&:before{color: var(--colorBase); font-size: 14px;}
			@media (min-width: @h){
				&:hover{
					background: var(--colorBaseBlue);
					&:before{color: var(--colorWhite);}
				}
			}
		}
		:deep(.splide__arrow--next){right: 16px;}
		:deep(.splide__arrow--prev){left: 16px;}
		*/
		@media (max-width: @m){
			box-shadow: none; padding: 0 16px; max-height: 0; overflow: hidden; transition: max-height 0.3s;
			.swiper-wrapper{flex-flow: row; gap: 0;}
		}
	}
	.location-city-item{
		position: relative; display: flex; align-items: center; gap: 56px;
		@media (max-width: @m){flex-flow: column; align-items: flex-start; gap: 16px;}
	}
	.location-city-item-image, :deep(.location-city-item-image){
		position: relative; display: block; width: 504px; height: auto; border-radius: var(--inputBorderRadius); overflow: hidden;
		img{display: block; width: auto; height: auto; max-width: 100%; border-radius: var(--inputBorderRadius);}
		@media (max-width: @m){
			width: 100%; display: flex; align-items: center; justify-content: center;
			picture{display: flex; align-items: center; justify-content: center; width: 100%; height: 100%;}
			img{display: block; width: 100%; height: 100%; object-fit: fill;}
		}
	}
	.location-city-item-cnt{
		display: block;
		&>div{
			&:last-child{margin-bottom: 0;}
		}
		@media (max-width: @m){margin: 0; width: 100%;}
		@media (max-width: @ms){margin: 0; width: 100%;}
	}
	.location-city-item-title{
		display: block; font-size: var(--fontSizeSpecial); line-height: 1.4; font-weight: bold; margin-bottom: 11px;
		@media (max-width: @m){font-size: 13px; line-height: 17px; margin-bottom: 8px;}
		//@media (max-width: @ms){font-size: 13px; line-height: 17px; margin-bottom: 8px;}
	}
	.location-city-item-address, .location-city-item-contact, .location-city-item-business-hour{
		display: block; margin-bottom: 13px; line-height: 22px;
		//@media (max-width: @ms){font-size: 13px; line-height: 20px; margin-bottom: 8px;}
		@media (max-width: @m){font-size: 13px; line-height: 20px; margin-bottom: 8px;}
	}
	.location-city-item-address{
		position: relative; display: block; padding-left: 26px; text-decoration: underline 1px var(--colorBaseBlue); text-underline-offset: 3px; transition: text-decoration-color 0.3s; cursor: pointer;
		&:before{.pseudo(14px,19px); background: url(assets/images/custom-icons/pin-small.svg) center no-repeat; background-size: cover; top: 1px; left: 0;}
		:deep(p){padding-bottom: 0;}
		@media (min-width: @h){
			&:hover{text-decoration-color: transparent;}
		}
	}
	.location-city-item-contact{
		position: relative; display: block;
		:deep(p:last-child){padding-bottom: 0;}
		:deep(a){
			color: var(--colorBase); text-decoration-color: var(--colorBaseBlue); text-underline-offset: 3px; position: relative; padding-left: 27px; display: inline-block;
			&:before{
				.pseudo(18px,13px); background: url(assets/images/custom-icons/mail.svg) center no-repeat; background-size: cover; top: 4px; left: 0;
				//@media (max-width: @ms){top: 2px;}
				@media (max-width: @m){top: 5px;}
			}
			@media (min-width: @h){
				&:hover{text-decoration-color: transparent;}
			}
		}
		:deep(a[href^='tel']){
			padding-bottom: 10px;
			&:before{
				.pseudo(18px,18px); background: url(assets/images/custom-icons/phone.svg) center no-repeat; background-size: cover; top: 0; left: 0;
				//@media (max-width: @ms){top: -1px;}
				@media (max-width: @m){top: 1px;}
			}
		}
		:deep(p){
			@media (max-width: @m){
				padding-bottom: 0;
				strong{padding-bottom: 8px; display: block;}
				a{display: block;}
				br{display: none;}
			}
		}
		@media (max-width: @m){
			margin-bottom: 10px;
		}
	}
	.location-city-item-business-hour{
		position: relative; display: block; padding-left: 26px;
		&:before{.pseudo(18px,18px); background: url(assets/images/custom-icons/clock.svg) center no-repeat; background-size: cover; top: 1px; left: 0;}
		:deep(p){padding-bottom: 0;}
	}
</style>
