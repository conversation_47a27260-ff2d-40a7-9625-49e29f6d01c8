<template>
	<LazyCmsHelloBar :hydrate-after="1000" />
	<Body :class="{'active-nav-m': mMenuOpen}" />
	<header class="header">
		<div class="header-top">
			<div class="wrapper">
				<BaseCmsLogo class="logo" id="logo" />
				<div @click="mobileMenu" class="m-btn"><span class="icon"></span></div>
				<div class="m-categories-title" :class="{'active': mActiveSubNav}" @click="menuBack">{{ mCategoriesText }}</div>

				<div class="responsive-cnt" :class="[{'header-support-active': headerSupport }]">
					<div class="header-buttons">
						<SearchForm />

						<div class="header-support" :class="[{'active': headerSupport }]" ref="menuSupport">
							<div class="btn-header header-btn-support" @click="headerSupportToggle">
								<BaseCmsLabel code="header_btn_support" tag="span" class="btn-header-text" />
							</div>

							<ClientOnly>
								<div class="header-support-box">
									<div class="header-support-box-col header-support-box-col1" :class="{'mobile-support-box' : mobileBreakpoint}">
										<BaseCmsLabel code="support_info" tag="div" class="support-info header-support-info" />
										<BaseCmsLabel code="faq_link" class="faq-link" tag="div" v-interpolation />
										<BaseCmsLabel code="social_title" tag="div" class="social-title" />
										<BaseCmsLabel code="social_links" tag="div" class="social-links" v-interpolation />
									</div>
									<div class="header-support-box-col header-support-box-col2">
										<BaseCmsNav code="main" v-slot="{items}">
											<ul class="nav nav-header">
												<li v-for="item in items" :key="item.id">
													<NuxtLink :target="item.target_blank != 0 ? '_blank' : null" :to="item.url_without_domain">{{ item.title }}</NuxtLink>
												</li>
											</ul>
										</BaseCmsNav>
									</div>
								</div>
							</ClientOnly>
						</div>
						<AuthUserBox />
						<CatalogCompareWidget />
						<WebshopShoppingCartBtn />
					</div>
				</div>
			</div>
		</div>
	</header>
	<div class="header-placeholder"></div>
	<CmsNavigation :open="mMenuOpen" />
</template>

<script setup>
	const {insertAfter, insertBefore, prependTo, appendTo, onClickOutside, onMediaQuery} = useDom();
	const labels = useLabels();

	const activeNavItem = useState('activeNavItem');

	// check if submenu is active
	const mActiveSubNav = computed(() => {
		return (activeNavItem.value?.length || headerSupport.value) ? true : false;
	});

	// mobile menu header title
	const mCategoriesText = computed(() => {
		if(headerSupport.value) return labels.get('header_btn_support');
		if(activeNavItem.value?.length) {
			return activeNavItem.value[activeNavItem.value.length - 1].title;
		}
		return labels.get('header_categories');
	})

	// mobile menu back button. Remove last item from activeNavItem to show parent menu title
	function menuBack() {
		if (activeNavItem.value?.length) {
			activeNavItem.value.pop();
		}
	};

	const route = useRoute();
	watch(
		() => route.fullPath,
		() => {
			mMenuOpen.value = false;
			headerSupport.value = false;
			activeNavItem.value = [];
			if(mobileBreakpoint.value) {
				appendTo('.nav-categories', '.wrapper-nav');
			}
		}
	)

	const mMenuOpen = ref(false);
	let windowOffset = 0;
	function mobileMenu() {
		activeNavItem.value = [];
		if (mMenuOpen.value == false) {
			insertBefore('.nav-categories', '.header-buttons');
			windowOffset = window.scrollY;
			mMenuOpen.value = true;
		} else {
			mMenuOpen.value = false;
			setTimeout(function(){
				window.scroll(0,windowOffset);
			},50);
			appendTo('.nav-categories', '.wrapper-nav');
		}
	}
	provide('header', {mobileMenu});

	const headerSupport = ref(false);
	function headerSupportToggle() {
		if (headerSupport.value == false) {
			headerSupport.value = true;
		} else {
			headerSupport.value = false;
		}
	}

	const menuSupport = ref(null);
	onClickOutside(menuSupport, () => {
		headerSupport.value = false;
	});

	const {matches: tabletBreakpoint} = onMediaQuery({
		query: '(max-width: 1300px)',
	});
	const {matches: mobileBreakpoint} = onMediaQuery({
		query: '(max-width: 980px)',
		timeout: 100,
		enter: () => {
			insertAfter('.header-support-box-col1', '.header-buttons');
			insertBefore('.sw', '.m-btn');
			insertBefore('.cw-compare', '.m-btn');
			insertBefore('.ww', '.m-btn');
		},
	});
</script>

<style lang="less">
	@media (max-width: @m){
		.header{height: 56px;}
	}
</style>

<style lang="less" scoped>
	.header { position: relative; }
	.header-top {
		background: var(--colorBaseBlue); height: 80px;
		& > .wrapper{display: flex; align-items: center; height: 100%; justify-content: space-between;}
		@media (max-width: @m){
			height: 56px;
			&>.wrapper{padding: 0;}
		}
	}
	.logo {
		position: relative; top: -2px; display: block; width: 185px; height: 46px; margin-right: 40px; background: url(assets/images/logo_text.svg) no-repeat center center; background-size: contain; flex-shrink: 0;
		@media (max-width: @t){width: 143px; height: 35px; margin-right: 32px; margin-left: 8px;}
		@media (max-width: @m){width: 124px; height: 32px; margin-right: auto; margin-left: 16px;}
	}
	.responsive-cnt{
		height: 100%; width: 100%; display: flex; position: relative;
		@media (max-width: @m){display: none;}
	}
	.header-buttons {display: flex; align-items: center; height: 100%; width: 100%;}

	.header-support {
		position: relative; height: 100%; margin-left: 34px;
		&.active{
			.btn-header{background: var(--colorDarkBlue);}
			.header-support-box{display: flex;}
		}
		@media (max-width: @t){
			margin-left: 32px;
		}
		@media (max-width: @m){
			display: none; width: 100%; margin-left: 0;
			&.active{
				.header-support-box-col2{display: block;}
			}
		}
	}
	.header-btn-support {
		&:before {
			.icon-info2(); font: 22px/1 var(--fonti);
			@media (max-width: @t){font-size: 18px;}
			@media (max-width: @m){.pseudo(24px,24px); background: url(assets/images/custom-icons/info.svg) center center no-repeat; background-size: contain; left: 19px;}
		}
	}
	.header-support-box{
		display: none; width: 559px; position: absolute; top: 80px; background: var(--colorWhite); box-shadow: 0 16px 40px 0 rgba(0, 12, 26, 0.16); left: -170px; z-index: 1111; border-bottom: 4px solid var(--colorBaseBlue); transition: opacity .3s, visibility .3s;
		//&:before { .pseudo(auto,20px); left: 0; right: 0; top: -20px; z-index: 1; }
		&:after { .pseudo(10px,10px); top: -4px; left: 208px; background: var(--colorWhite); .rotate(45deg); z-index: 1; }
		@media (max-width: @l){
			left: -215px;
			&:after{left: 253px;}
		}
		@media (max-width: @t){
			width: 364px; box-shadow: 0 12px 30px 0 rgba(0, 12, 26, 0.16); left: -75px;
			&:after{left: 106px;}
		}
		@media (max-width: @m){
			width: 100%; top: unset; box-shadow: unset; left: unset; border-bottom: unset; opacity: 1; visibility: visible;
			&:before{display: none;}
			&:after{display: none;}
		}
	}
	.header-support-box-col {
		width: 50%; padding: 25px 48px 44px;
		&:first-child { border-right: 1px solid var(--colorLightGray); }
		@media (max-width: @t){
			padding: 20px 31px 24px 32px; width: auto; flex-shrink: 0; min-width: 175px;
			&:first-child{width: 188px; flex-grow: 1;}
		}
		@media (max-width: @m){background: var(--colorLightBlueBackground); border-right: 0!important; width: 100%!important;}
	}
	.header-support-box-col2{
		@media (max-width: @m){
			display: none; background: var(--colorWhite); width: 100%; padding: 0; position: relative; overflow: auto; overflow-x: hidden;
		}
	}
	.support-info {
		padding-top: 34px; display: block; position: relative; font-size: var(--fontSizeSmall); line-height: 1.3;
		&:before { .pseudo(28px,28px); background: url(assets/images/custom-icons/conversation.svg) no-repeat center center; left: 0; top: 0; background-size: contain;}
		:deep(.support-info-title) { font-size: var(--fontSize); line-height: var(--lineHeight); font-weight: bold; }
		:deep(.support-info-desc) { display: block; padding-top: 11px; }
		:deep(a) {
			padding-top: 3px; font-weight: bold; padding-bottom: 10px; text-decoration: none; display: block; color: var(--colorBase);
			&.support-info-mail{
				text-decoration: underline; text-decoration-color: var(--colorBaseBlue); text-underline-offset: 3px; .transition(text-decoration-color);
				&:hover{text-decoration-color: transparent;}
			}
		}
		:deep(strong) { padding-top: 3px; font-weight: bold; display: block; }

		@media (max-width: @t){
			padding-top: 31px; font-size: 11px; line-height: 1.5;
			&:before {width: 24px; height: 24px;}
			:deep(.support-info-title) {font-size: 12px; line-height: 1.5;}
			:deep(.support-info-desc) {
				padding-top: 10px;
				&:first-child{padding-top: 6px;}
			}
			:deep(a) {padding-bottom: 0; padding-top: 0;}
			:deep(strong) {padding-top: 0;}
		}
		@media (max-width: @m){
			padding-top: 0; padding-left: 40px; font-size: 13px;
			&:before{width: 29px; height: 29px;}
			:deep(.support-info-title){font-size: 15px; line-height: 1.4; padding-top: 4px;}
			:deep(a) {
				padding-top: 1px; padding-bottom: 6px; font-size: 15px; line-height: 1.4;
				&.support-info-tel{text-decoration: underline; text-decoration-color: var(--colorBaseBlue); text-underline-offset: 3px;}
			}
			:deep(strong){font-size: 15px; line-height: 1.4;}
		}
	}
	.social-title {
		font-size: var(--fontSizeSmall); line-height: 1.2; display: block; padding: 49px 0 6px;
		@media (max-width: @t){font-size: 11px; line-height: 1.5; padding: 25px 0 3px;}
		@media (max-width: @m){display: none;}
	}
	:deep(.social-links){
		position: relative; display: flex; align-items: center;
		a{
			position: relative; display: block; width: 26px; height: 26px; margin-right: 6px; font-size: 0;
			&:before{.pseudo(26px,26px); background: url('/assets/images/social-icons/facebook.svg') center no-repeat; background-size: cover; top: 0; left: 0; opacity: 1; .transition(opacity);}
			@media (min-width: @h){
				&:hover{
					&:before{opacity: 0.5;}
				}
			}
			@media (max-width: @t){
				width: 20px; height: 20px; margin-right: 4px;
				&:before{width: 20px; height: 20px; filter: grayscale(100%);}
			}
			@media (max-width: @m){
				width: 40px; height: 40px; margin-right: 8px;
				&:before{width: 40px; height: 40px; filter: unset;}
			}
		}
		a[title~="instagram"]{
			&:before{background: url('/assets/images/social-icons/instagram.svg') center no-repeat; background-size: cover;}
		}
		a[title~="youtube"]{
			&:before{background: url('/assets/images/social-icons/youtube.svg') center no-repeat; background-size: cover;}
		}
		a[title~="tiktok"]{
			&:before{background: url('/assets/images/social-icons/tiktok.svg') center no-repeat; background-size: cover;}
		}
		a[title~="linkedin"]{
			&:before{background: url('/assets/images/social-icons/linkedin.svg') center no-repeat; background-size: cover;}
		}
		@media (max-width: @m){
			padding: 24px 0 0 40px;
			a{
				width: 40px; height: 40px; margin-right: 8px;
				&:before{width: 40px; height: 40px;}
			}
		}
	}
	.header-support-box-col{
		:deep(.nav-header){
			list-style: none;
			li{
				display: block; width: 100%; margin-bottom: 12px;font-size: var(--fontSizeSmall); line-height: 1.3;
				a{
					text-decoration: none; color: var(--colorBase); .transition(color);
					&:hover { color: var(--colorBaseBlue); }
				}
				&:last-child{margin-bottom: 0;}
			}
			@media (max-width: @t){
				li{font-size: 11px;}
			}
			@media (max-width: @m){
				li{
					font-size: 15px; border-bottom: 1px solid var(--colorBorderLightForms); margin-bottom: 0;
					a{padding: 14px 15px 13px; display: inline-block; width: 100%;}
				}
			}
		}
	}
	.mobile-support-box{display: none; background: var(--colorLightBlueBackground); padding: 24px 16px 32px; width: 100%;}

	.faq-link{
		position: relative; display: none; align-items: flex-start; justify-content: flex-start; margin-top: 23px; padding-left: 40px;
		:deep(p){padding-bottom: 0; display: flex; align-items: center; justify-content: flex-start;}
		:deep(a){
			display: block; position: relative; font-size: 13px; line-height: 1.3; color: var(--colorBaseBlue); font-weight: bold; text-decoration: none; padding-right: 25px;
			&:after{.pseudo(20px,20px); top: -2px; right: 0; .icon-arrow3(); font: 20px/1 var(--fonti); color: var(--colorBaseBlue);}
		}
		@media (max-width: @m){display: flex;}
	}
</style>

<style lang="less">
	.btn-header {
		width: 86px; height: 100%; min-width: 64px; display: flex; flex-direction: column; justify-content: center; align-items: center; flex-shrink: 0; font-size: 12px; line-height: 15px; text-decoration: none; color: var(--colorWhite); position: relative; cursor: pointer; .transition(background-color);
		&:before { height: 20px; color: var(--colorWhite); .transition(opacity); }
		@media (min-width: @t){
			&:hover{background: var(--colorDarkBlue);}
		}
		@media (max-width: @t){
			width: 72px; min-width: 48px; font-size: 10px; line-height: 1.2;
			&:before{height: 17px;}
		}
		@media (max-width: @m){
			width: 20px; min-width: 20px; font-size: 0; line-height: 0; margin: 0 10px;
			&:before{height: 20px;}
		}
	}
	.btn-header-text {
		display: block; margin-top: 7px; text-align: center; .transition(opacity);
		@media (max-width: @t){margin-top: 4px;}
		@media (max-width: @m){margin-top: 0; display: none;}
	}
	.btn-header-counter{
		display: flex; align-items: center; justify-content: center; border-radius: 100%; font-size: 11px; line-height: 1.1; font-weight: bold; color: rgba(255, 255, 255, 0.5); position: absolute; top: 6px; right: 20px; z-index: 1; width: 24px; height: 24px; .transition(color);
		@media (max-width: @t){font-size: 10px; top: 10px; right: 10px; width:22px; height: 22px;}
		@media (max-width: @m){display: none; top: 6px; right: -13px;}
	}

	.m-btn {
		display: none;
		@media (max-width: @m) {
			display: flex; align-items: center; justify-content: center; flex-shrink: 0; width: 56px; height: 56px; background: var(--colorYellow); cursor: pointer; margin-left: 10px;
			.icon, .icon:after, .icon:before { content: ''; display: block; position: absolute; width: 18px; height: 2px; background: var(--colorBase); border-radius: 1px;}
			.icon {
				&:before {top: -7px;}
				&:after {top: 7px;}
			}
		}
	}
	.m-categories-title {
		display: none; align-items: center; height: 56px; position: absolute; left: 24px; right: 60px; font-size: 15px; line-height: 1.3; color: var(--colorWhite); font-weight: 600;
		&.active{
			padding-left: 35px;
			&:before{.icon-arrow-nav(); font: 12px/1 var(--fonti); color: var(--white); position: absolute; left: 0; top: 21px; .rotate(180deg);}
		}
	}



	@media (max-width: @m) {
		.active-nav-m #__nuxt{
			.page-wrapper > *:not(.header, .header> *){height: 0; overflow: hidden; min-height: 0; padding: 0; margin: 0;}

			.btn-header {
				background: var(--colorLightBlueBackground); width: 100%; height: auto; display: flex; flex-flow: row; align-items: center; justify-content: flex-start; min-width: unset; padding: 15px 16px 16px; font-size: 15px; line-height: 1.4; color: var(--colorBase); margin: 0; border-bottom: 1px solid var(--colorBorderLightForms);
				&:before{position: absolute;}
				&:after{.icon-arrow-nav(); right: 24px; position: absolute; font: 13px/1 var(--fonti); color: var(--colorBaseBlue);}
				span{padding-left: 42px;}
			}
			.btn-header-text {display: block; margin-top: 0; text-align: unset;}
			.logo, .sw, .ww, .cw-compare{height: 0; overflow: hidden; min-height: 0; padding: 0; margin: 0; display: none;}
			.header {z-index: 11111;}
			.header-top > .wrapper {justify-content: flex-end;}
			.m-btn {
				margin: 0; background: transparent;
				span {background: transparent;}
				span:after {.rotate(45deg); top: 0; background: #fff;}
				span:before {.rotate(-45deg); top: 0; background: #fff;}
			}
			.m-categories-title{display: flex;}

			.responsive-cnt{
				display: block; height: auto; position: fixed; top: 0; left: 0; right: 0; bottom: 0; margin-top: 56px; background: var(--colorWhite); overflow: auto; overflow-x: hidden;
				&::-webkit-scrollbar,&::-webkit-scrollbar-thumb {display: none;}
				.menu-lvl-active{
					.header-buttons{display: none;}
				}
			}
			.header-support-active{
				.header-categories, .header-btn-support, .aw, .nav-categories{display: none;}
				.header-support-box{position: relative;}
			}
			.header-categories{display: flex; border-bottom: unset;}
			.header-buttons{height: auto; flex-flow: column;}
			.header-support{order: 2; display: block;}
			.mobile-support-box{order: 3; display: block;}

		}
	}

	body.fixed-header #__nuxt{
		.header-placeholder{
			height: 80px;
			@media (max-width: @m){height: 56px;}
		}
		.header{position: fixed; top: -55px; left: 0; right: 0; .translate3d(0,55px); .transition(transform); border-bottom: 1px solid var(--colorBorderLightForms); z-index: 1111; background: var(--colorWhite);}
		@media (min-width: @m){
			.header-top{background: var(--colorWhite); position: relative; height: 55px;}
			.logo{display: none;}
			.header-buttons{width: auto;}
			.responsive-cnt{display: flex; width: auto; justify-content: space-between;}
			.nav-categories{
				&>li{
					height: 55px; align-items: center; margin-right: 10px;
					.nav-lvl1{
						font-size: 14px; line-height: 1.4; height: auto;
						span{
							height: auto;
							&:after{display: none;}
						}
						.img{display: none;}
					}
					&.active{
						.nav-lvl1{color: var(--colorBaseDarker); background: none;}
					}
				}
			}
			.nav-submenu-container-l2, .nav-submenu-container-l3{display: none!important;}
			.btn-header{
				width: auto; min-width: unset; padding: 0 16px;
				&:before{color: var(--colorBase);}
			}
			.btn-header-text{display: none;}
			.btn-header-counter{right: 4px; color: var(--colorBase); width: 20px; height: 20px; border: 0!important;}
			.ww.empty:hover, .cw-compare:not(.active):hover{
				.btn-header-counter{color: #fff;}
			}
			.header-support{
				margin-left: 0;
				&.active .header-btn-support:before{color: #fff;}
			}
			.header-support-box{
				left: -350px; top: 56px;
				&:after{right: 177px; left: unset;}
			}
			.header-btn-support:before{height: 21px; font-size: 21px;}

			.sw{display: block; flex-grow: unset; height: 100%;}
			.sw-toggle{
				display: flex;
				&:before{height: auto; font-size: 18px;}
			}
			.sw-form{width: 0; visibility: hidden; opacity: 0; height: 100%; position: absolute; top: 0; right: 0;}
			.active .btn-header{background: none;}
			&.search-active{
				.sw{
					&.active{
						.sw-input{padding-left: 35px;}
					}
					button{width: 30px; height: 100%; top: 0; background: var(--colorWhite); right: unset; left: 0;}
				}
				.sw-form{width: 225px; visibility: visible; opacity: 1; height: 100%;}
				.sw-toggle{display: none;}
				.sw-input{padding: 0 0 0 35px; height: 100%; font-weight: normal;}
				.sw-label{left: 35px; top: 19px; font-size: 12px;}
				.ac{
					top: 52px; right: -209px; width: 1084px; left: unset;
					&:after{right: 418px; left: unset;}
				}
				.sw-clear{display: none;}
				.sw-list-container{
					top: 52px; right: -209px; width: 707px; left: unset;
					&:after{right: 418px; left: unset;}
				}
			}
		}
		@media (min-width: @h){
			.btn-header:before{.transition(color);}
			.header-support.active{
				.header-btn-support{
					background: var(--colorBaseBlue);
					&:before{color: #fff;}
				}
			}
			.aw.active{
				.btn-header{background: var(--colorBaseBlue);
					&:before{color: var(--colorWhite);}
				}
				.aw-popup{top: 56px;}
			}
			.btn-header:hover{
				background: var(--colorBaseBlue);
				&:before{color: var(--colorWhite);}
			}
		}
		@media (max-width: @t){
			.header{top: -184px; .translate3d(0,184px);}
			.mcategories .wrapper{padding: 0;}
			.nav-categories{
				&>li{
					margin-right: 8px;
					.nav-lvl1{font-stretch: 13px;}
				}
			}
			.header-buttons{align-items: initial;}
			.btn-header{padding: 0 10px;}
			&.search-active{
				.sw .sw-form:after{.pseudo(auto, auto); background: linear-gradient(90deg, rgba(255,255,255,0) 0, #fff 100%); top: 0; bottom: 0; right: 100%; left: -40px;}
				.ac{
					right: -155px; width: 936px;
					&:after{left: unset; right: 365px;}
				}
				.sw-list-container{
					right: -155px; width: 580px;
					&:after{right: 364px;}
				}
			}
			.header-support-box{
				left: -209px;
				&:after{right: 129px;}
			}
			.aw-popup{left: -95px!important; top: 56px;}
			.aw.active{
				.btn-header{background: var(--colorWhite);}
			}
			.ww{
				margin-right: 0;
				.btn-header{width: auto;}
			}
		}
		@media (min-width: @m) and (max-width: @t){
			.btn-header-counter{right: 0; top: 6px;}
		}
		@media (max-width: @m){
			.header{top: -56px;.translate3d(0,56px); border-bottom: 0;}
			.header-top{background: var(--colorBaseBlue); height: 56px;}
			.logo{display: block!important;}
			.btn-header{
				padding: 0;
				&:before{color: var(--colorWhite);}
			}
			.mcategories,.responsive-cnt{display: none!important;}
		}
	}
</style>
