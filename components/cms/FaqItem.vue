<template>
	<div class="faq-item" :class="{'active': active === true, mode, 'hide': index > 3}">
		<div class="faq-item-title" @click="active = !active">{{item.title}} <span class="icon-dropdown"></span></div>
		<div class="faq-item-content" v-html="item.content" v-interpolation></div>
	</div>
</template>

<script setup>
	const props = defineProps(['item', 'mode', 'index']);
	const active = ref(0);
</script>

<style lang="less" scoped>
	.faq-item{
		position: relative; display: block; border-top: 1px solid var(--colorBorderLightForms); border-bottom: 1px solid var(--colorBorderLightForms); margin-top: -1px; padding-bottom: 0; .transition(padding);
		&:first-child{margin-top: 0; border-top: 0;}
		&:last-child{border-bottom: 0;}
		&.active{
			padding-bottom: 24px;
			.faq-item-title{padding-bottom: 4px;}
			.icon-dropdown:after{opacity: 0;}
			.faq-item-content{max-height: inherit;}
			@media (max-width:  @m){padding-bottom: 17px;}
		}
		@media (max-width: @m){
			&:last-child{border-bottom: 1px solid var(--colorBorderLightForms);}
		}
	}
	.faq-item-title{
		position: relative; display: block; font-weight: bold; padding: 17px 20px 17px 0; .transition(padding-bottom); cursor: pointer;
		@media (max-width: @m){padding: 13px 34px 12px 16px;}
	}
	.faq-item-content{
		display: block; overflow: hidden; max-height: 0; .transition(max-height);
		:deep(p){padding-bottom: 0;}
		@media (max-width: @m){font-size: 15px;}
		@media (max-width: @m){font-size: 13px; line-height: 1.5; padding: 0 16px 0;}
	}
	.icon-dropdown{
		display: block; position: absolute; width: 10px; height: 10px; top: 24px; right: 0;
		&:before{.pseudo(10px,2px); background: var(--colorBaseBlue); left: 0; top: calc(~"50% - 1px");}
		&:after{.pseudo(2px,10px); background: var(--colorBaseBlue); left: calc(~"50% - 1px"); top: 0; .transition(opacity);}
		@media (max-width: @m){right: 16px; top: 19px;}
	}
</style>
