<template>
	<BaseCmsNav code="main_categories" v-slot="{items}">
		<div class="header-categories">
			<div class="wrapper wrapper-nav">
				<ul class="nav nav-categories" :class="'submenu-l'+activeNavItem?.length" v-if="items?.length" ref="categoriesNav">
					<li v-for="(item, index) in items" :key="item.id" class="nav-item" :class="[{'has-children': item.items}, 'nav-item-'+ item.id, item.style, item.code, {'active': item.url_without_domain == rootCategoryPath || route.path == '/' && index == 0}, {'selected': isSelected(item)}]">
						<BaseUiLink native :prevent-default="onPreventDefault(item)" :href="item.url_without_domain" class="nav-lvl1" @click="onClick(item)">
							<span>
								<span v-if="item.image_upload_path" class="img"><BaseUiImage loading="lazy" :src="item.image_upload_path" width="36" height="36" alt="" /></span>
								<span>{{item.title}}</span>
							</span>
						</BaseUiLink>
						<div class="nav-submenu-container-l2" v-if="item.items?.length">
							<div class="wrapper wrapper-submenu">
								<ul class="nav-submenu">
									<template v-for="item in item.items" :key="item.id">
										<CmsNavigationSubmenu :item="item" />
									</template>
								</ul>
							</div>
						</div>
					</li>
				</ul>
			</div>
		</div>
	</BaseCmsNav>
</template>

<script setup>
	const route = useRoute();
	const { scrollTo, onMediaQuery } = useDom();
	const { getUrlSegments } = useUrl();
	const props = defineProps({
		open: Boolean,
	});

	const {matches: desktopBreakpoint} = onMediaQuery({
		query: '(min-width: 1300px)',
	});
	const {matches: tabletBreakpoint} = onMediaQuery({
		query: '(max-width: 1300px)',
	});
	const {matches: mobileBreakpoint} = onMediaQuery({
		query: '(max-width: 980px)',
	});

	// Get product data from state to set active category based on product category
	const product = useState('product', () => null);

	const parentCategoryPath = computed(() => {
		let path = route.path;
		if(product.value && route.meta.action == 'detail') path = product.value.category_url_without_domain;
		return getUrlSegments(path, {
			limit: 3,
			stringify: true,
			addSlashes: true,
		});
	});

	const rootCategoryPath = computed(() => {
		let path = route.path;
		if(product.value && route.meta.action == 'detail') path = product.value.category_url_without_domain;
		return getUrlSegments(path, {
			limit: 2,
			stringify: true,
			addSlashes: true,
		});
	});

	// prevent default menu click on certain items
	function onPreventDefault(item) {
		if(mobileBreakpoint.value && props.open && item.items?.length) return true;
		return false;
	}

	const {mobileMenu} = inject('header');
	const activeNavItem = useState('activeNavItem', () => []);
	function onClick(item) {
		// close menu if item has no children
		const activeMobileMenu = document.body.classList.contains('active-nav-m') ? true : false;
		if(mobileBreakpoint.value && !item.items?.length && activeMobileMenu) {
			mobileMenu();
		}

		const el = {
			id: item.id,
			title: item.title,
			url_without_domain: item.url_without_domain,
			level: item.level,
			children: (item.items?.length) ? true : false
		}

		// scroll to categories section on mobile
		if(mobileBreakpoint.value && !props.open && item.url_without_domain == rootCategoryPath.value) {
			scrollTo('.homepage-categories', {offset: 40});
			focusCategory();
			return;
		}

		// push new item to activeNavItem array only if it's not already there. If level is the same, replace it. If clicked item is level 1, remove all other items
		if(item.level == 1) {
			activeNavItem.value = [el];
			focusCategory();
			return
		}

		const alreadyActive = activeNavItem.value.find((el) => el.id == item.id);
		if(alreadyActive) {
			const index = activeNavItem.value.findIndex((el) => el.id == item.id);
			activeNavItem.value.splice(index, 1);
		} else {
			activeNavItem.value = activeNavItem.value.filter((el) => el.level != item.level);
			activeNavItem.value.push(el);
		}
		focusCategory();
	}

	const {fixedHeader} = inject('layout');
	const categoriesNav = ref(null);
	function focusCategory(timeout = 600) {
		setTimeout(() => {
			const activeItem = categoriesNav.value?.querySelector('.active');
			if(activeItem) {
				const targetPosition = activeItem.offsetLeft - categoriesNav.value.offsetWidth / 2 + activeItem.offsetWidth / 2;
				categoriesNav.value.scrollTo({
					left: targetPosition,
					behavior: 'smooth',
				});
			}
		}, timeout);
	}
	onMounted(() => focusCategory());
	watch(fixedHeader, () => focusCategory(100));

	function isSelected(item) {
		 if(tabletBreakpoint.value) {
			const active = activeNavItem.value.find((el) => el.id == item.id);
			return (active) ? true : false;
		 }
	}

	provide('cmsnavigation', {onClick, parentCategoryPath, rootCategoryPath, onPreventDefault, isSelected});
</script>

<style lang="less">
	.wrapper-submenu{position: relative;}
	.header-categories{
		border-bottom: 1px solid var(--colorBorderLightForms); position: relative; height: 65px;
		@media (max-width: @t){height: 57px;}
		@media (max-width: @m){height: 73px;}
		&:after{.pseudo(100%, 100%); opacity: 0; left: 0; right: 0; bottom: 0; top: 0; box-shadow: 0 10px 20px 0 rgba(0,12,26,0.2); pointer-events: none; z-index: 999; .transition(opacity);}
		&.active{
			padding-bottom: 55px;
			@media (max-width: @t){padding-bottom: 47px;}
			@media (max-width: @m){padding-bottom: 0;}
		}
		&.mcategories{display: none;}
	}
	.page-wrapper:has(.nav-item.has-children.active:not(&.outlet)) .header-categories{
		height: 120px;
		@media (max-width: @t){height: 104px;}
		@media (max-width: @m){height: 73px;}
	}
	// level 1
	.nav-categories{
		display: flex; justify-content: space-between; list-style: none; font-size: 14px;
		&>li{
			display: flex; justify-content: center; flex-shrink: 0;
			.nav-lvl1{
				display: flex; align-items: center; width: 100%; font-weight: bold; font-size: 15px; line-height: 1.2; height: 64px; color: var(--colorBase); position: relative;
				&>span{
					position: relative; display: flex; align-items: center; height: 100%; padding: 0 10px;
					@media (max-width: @t){padding: 0 5px;}
				}
				@media (min-width: @t){
					&:hover{color: var(--colorBaseBlue);}
				}
			}
			&.selected, &.active{
				@media (min-width: @m){
					.nav-lvl1{
						background: var(--colorYellow);
						&:hover{color: var(--colorBase);}
					}
				}
				&.has-children .nav-lvl1>span:after{.pseudo(10px,10px); bottom: -5px; left: 50%; .rotate(45deg); background: var(--colorBaseDarker);}
				.nav-submenu-container-l2{opacity: 1; max-height: none; overflow: visible;}
			}
			&.discount{
				a{color: var(--colorRed);}
			}
		}
		a{text-decoration: none; color: #000; color: var(--colorBase); .transition(color); position: relative;}
		img{
			display: block; max-width: 100%; height: auto; max-height: 100%;
			@media (max-width: @m){width: 100%;}
		}
		@media (max-width: @t){
			font-size: 13px;
			&>li{
				flex-grow: unset;
				.nav-lvl1{font-size: 13px; line-height: 1.3; height: 56px;}
			}
		}
		@media (max-width: @m){
			width: calc(~"100% - -30px"); margin-left: -15px; overflow-y: hidden;
			&::-webkit-scrollbar{display: none;}
			&::-webkit-scrollbar-thumb{display: none;}
			&>li{
				.nav-lvl1{
					flex-flow: column; height: 72px; padding: 5px;
					&>span{
						flex-flow: column; justify-content: center; padding: 0 10px; border: 1px solid transparent; border-radius: 4px;
						&:after{bottom: 0; display: none!important;}
					}
				}
				&.active{
					.nav-lvl1{
						background: none; justify-content: center; height: auto; color: var(--colorBaseDarker);
						&>span{border-color: var(--colorBaseDarker);}
					}
				}
				&.nav-selected, &.active{
					.nav-submenu-container-l2{display: none;}
				}
			}
		}
	}
	.img{
		width: 32px; height: 28px; display: flex; align-items: center; justify-content: center; margin-right: 8px;
		:deep(img){max-height: 25px;}
		@media (max-width: @t){
			width: auto; height: 20px; margin-right: 5px; min-width: 15px; max-width: 29px;
			:deep(img){max-height: 19px; width: auto; height: 100%;}
		}
		@media (max-width: @m){
			margin-bottom: 5px; margin-right: 0; width: 38px; height: 28px; min-width: unset; max-width: unset;
			:deep(img){max-height: 28px; width: initial; height: auto;}
		}
	}

	// level 2
	.nav-submenu-container-l2{
		position: absolute; top: 64px; left: 0; right: 0; z-index: 100; overflow: hidden; opacity: 0; max-height: 0; .transition(opacity);
		&:after{.pseudo(auto,auto); background: var(--colorBaseDarker); top: 0; bottom: 0; left: 0; right: 0;}
		@media (max-width: @t){top: 56px;}
	}
	.nav-submenu{
		display: flex; font-size: 14px;
		&>li{
			display: flex; align-items: center; position: relative; z-index: 1;
			.nav-lvl2{
				color: #fff; height: 56px; display: flex; align-items: center; font-weight: bold; overflow: hidden; .transition(color); cursor: pointer; position: relative; padding: 0 17px;
				.img{display: none;}
				&:after{.pseudo(10px,10px); background: #fff; .rotate(45deg); left: 50%; margin-left: -5px; bottom: -5px; opacity: 0;}
			}
			@media (min-width: @h){
				&.open{
					.nav-submenu-container-l3{opacity: 1; max-height: none; width: auto; overflow: visible;}
					.nav-lvl2{color: #B2CAE6;}
					&.has-children .nav-lvl2:after{opacity: 1; .transition(opacity);}
				}
				&.active{
					.nav-lvl2{
						background: var(--colorBase); color: #fff;
						&:hover{
							color: #fff;
							&:after{background: #fff;}
						}
						&:after{opacity: 1; background: var(--colorLightBlueBackground);}
					}
				}
			}
			@media (max-width: @t){
				&.selected{
					.nav-submenu-container-l3{opacity: 1; max-height: none; width: auto; overflow: visible;}
					.nav-lvl2{
						color: #B2CAE6;
						&:after{opacity: 1;}
					}
				}
			}
			@media (min-width: @m){
				&.discount{
					.nav-lvl2 span{padding: 5px 10px 6px; background: var(--colorRed); border-radius: var(--inputBorderRadius);}
				}
			}
			&.big{position: static;}
		}
		@media (max-width: @t){
			font-size: 11px;
			&>li{
				.nav-lvl2{height: 48px;}
				&.has-children>a:after{bottom: -7px;}
				&>a{
					height: 48px;
				}
				&.discount{
					&>a span{padding: 5px 7px 5px;}
				}
			}
		}
	}

	// level 3
	.nav-submenu-container-l3{
		position: absolute; top: 100%; left: -48px; background: #fff; z-index: 100; display: flex; border-bottom: 4px solid var(--colorBaseBlue); box-shadow: 0 16px 40px 0 rgba(0,12,26,0.16); overflow: hidden; max-height: 0; opacity: 0; width: 0; .transition(opacity);
		@media (max-width: @l){left: 0;}
		@media (max-width: @t){left: -32px; box-shadow: 0 12px 30px 0 rgba(0, 12, 26, 0.16);}
		@media (max-width: @m){transition: none;}
	}
	.big .nav-submenu-container-l3{
		.nav-submenu-col:not(:first-child){
			max-width: 280px;
			min-width: 200px;
			@media (max-width: @m){max-width: none;}
		}
		@media (max-width: @l){right: 0;}
		@media (max-width: @t){right: 62px; left: 8px;}
		@media (max-width: @m){right: 0; left: 0;}
	}
	.nav-submenu-col{
		border-right: 1px solid var(--colorLightGray); padding: 30px 40px; min-width: 280px; flex-grow: 1;
		@media (max-width: @t){padding: 20px 30px 25px; min-width: 205px;}
	}
	.nav-submenu-col-0{
		.nav-submenu-items{
			li{min-width: 150px;}
			li:not(.show_all) a{
				display: flex; position: relative; gap: 13px; align-items: center;
				&:after{
					.icon-arrow2(); font: 10px/1 var(--fonti); position: relative; top: 1px; color: var(--colorIconLightBlue); .scaleX(-1); .transition(color); margin-left: auto;
					@media (max-width: @m){display: none;}
				}
				&:hover:after{color: var(--colorBaseBlue);}
			}
		}
		.nav-submenu-image{
			display: flex; align-items: center; justify-content: center; width: 36px; height: 36px; background: var(--colorLightBlueBackground);
			img{width: 85%; height: auto; mix-blend-mode: darken;}
		}
	}

	.nav-submenu-title{
		font-size: 16px; font-weight: bold; padding-bottom: 5px;
		@media (max-width: @t){font-size: 12px;}
	}
	.nav-submenu-subtitle{display: none;}
	.nav-submenu-items{
		list-style: none; line-height: 1.3;
		li{
			padding: 4px 0; min-width: 130px;
			@media (max-width: @m){margin: 0; min-width: unset; max-width: none;}
			&.bold a{font-weight: bold;}
			&.discount a{color: var(--colorRed);}
			&.show_all a{
				color: var(--colorBaseBlue); display: flex; align-content: center;
				&:after{
					.icon-arrow3(); font: 20px/1 var(--fonti); color: var(--colorBaseBlue); margin-left: 6px;
					@media (max-width: @t){font-size: 16px; margin-left: 5px;}
				}
			}
			&.mobile{display: none;}
		}
		@media (min-width: @h){
			a:hover{color: var(--colorBaseBlue);}
		}
		&.two-columns{
			column-count: 2; column-gap: 40px;
			@media (max-width: @m){column-count: 1;}
		}
		@media (max-width: @t){padding: 6px 0;}
	}
	.nav-submenu-image{display: none;}

	.nav-submenu-col-bestsellers{
		width: 450px; flex-grow: 0; max-width: initial!important;
		.nav-submenu-title{padding-bottom: 9px;}
		@media (max-width: @t){width: 345px;}
	}
	.nav-submenu-button{display: none;}
	.novi-artikli-top-menu{
		.img{margin-bottom: 6px;}
	}
	.nav-item-193, .nav-item-196{
		position: relative;
		@media (min-width: @h){
			&:hover{
				.nav-submenu-container-l2{overflow: visible!important; max-height: unset!important; opacity: 1!important; padding: 30px 48px 40px;}
			}
		}
		.nav-submenu-container-l2{
			position: absolute; top: 100%; left: -32px; right: 0; background: var(--colorWhite); z-index: 100; display: flex;  box-shadow: 0 5px 40px 0 rgba(0,12,26,0.16); overflow: hidden!important; max-height: 0!important; opacity: 0!important; min-width: 190px; width: auto; .transition(opacity);
			&::after{.pseudo(10px,10px); background: #fff; .rotate(45deg); left: 50%; margin-left: -5px; top: -5px; opacity: 1; .transition(opacity);}
			&:before{.pseudo(100%,4px); background: var(--colorBaseBlue); bottom: 0; left: 0; right: 0;}
		}

		.nav-submenu{
			flex-flow: column;
			&>li{
				margin-right: 0; padding: 4px 0;
				&>a{
					color: var(--colorBase); height: initial; font-weight: normal; overflow: hidden; .transition(color);
					@media (min-width: @h){
						&:hover{
							color: var(--colorBaseBlue);
						}
					}
				}

				&.active{
					.nav-submenu-container-l3{display: none;}
				}
				&.bold{
					&>a span{font-weight: bold;}
				}
			}
		}
		@media (max-width: @t){
			.nav-submenu-container-l2{display: none;}
		}
	}

	//MOBILE TOGGLE NAVIGATION
	@media (max-width: @m){
		.active-nav-m .nav-categories{
			flex-wrap: wrap; width: 100%; margin: 0;
			&>li{
				width: 50%; aspect-ratio: 1.6/1; align-items: center; justify-content: center; flex-direction: column; margin: 0; border-right: 1px solid var(--colorBorderLightForms); border-bottom: 1px solid var(--colorBorderLightForms);
				.nav-lvl1{height: 100%;}
				&:nth-child(2n){border-right: 0;}
				&.active>.nav-lvl1{color: var(--colorBase);}
			}
			.nav-lvl1{
				font-size: 15px;
				span{border: none!important}
				span:after{display: none;}
			}
			.img{
				width: 45px; height: 35px; margin-bottom: 13px;
				:deep(img){width: 100%; max-height: 100%;}
			}
			.brendovi{
				width: 100%; aspect-ratio: initial; background: var(--colorLightBlueBackground);
				.nav-lvl1{
					padding: 12px 14px; display: block;
					span{flex-direction: row; justify-content: flex-start; font-weight: normal;}
					&:after{.icon-arrow-nav; font: 13px/1 var(--fonti); color: var(--colorBaseBlue); position: absolute; top: 21px; right: 24px;}
				}
				.img{margin: 0 10px 0 5px; width: 30px; height: 30px;}
			}

			&.submenu-l1, &.submenu-l2{
				&>li{display: block; border: 0; aspect-ratio: inherit; width: 100%;}
				&>li:not(.selected), .nav-lvl1{display: none;}
				.nav-submenu-container-l2{display: block; position: relative; top: auto; left: auto; max-height: none!important; box-shadow: none; border: 0; opacity: 1!important;}
			}
			&.submenu-l2{
				.nav-submenu-container-l2{
					.nav-submenu>li{display: block; border: 0; aspect-ratio: inherit; width: 100%;}
					.nav-submenu>li:not(.selected), .nav-lvl2{display: none;}
				}
			}

			&.submenu-l1, &.submenu-l2{
				&+:deep(.header-buttons){display: none!important;}
			}
		}
		.nav-submenu-container-l2{
			top: auto; position: relative; left: auto;
			@media (max-width: @m){display: none;}
			&:after{display: none;}
		}
		.nav-submenu-container-l3{position: relative; display: block; box-shadow: none; left: auto; top: auto; border: 0;}
		.nav-submenu-col-bestsellers{display: none;}
		//.nav-submenu-items>:first-child{display: none;}
		.nav-submenu-col{padding: 0;}
		.nav-submenu-items{
			padding: 0;
			li{padding: 0;}
			a{
				padding: 13px 15px; border-bottom: 1px solid var(--colorBorderLightForms); display: flex; align-items: center;
				&:after{.icon-arrow2(); display: block!important; font: 10px/1 var(--fonti); font-weight: bold; .rotate(180deg); color: var(--colorBaseBlue)!important; margin-right: 7px;}
			}
			li.show_all a{
				color: var(--colorBase);
				&:after{.icon-arrow2(); margin-left: auto; font: 10px/1 var(--fonti); height: auto;}
			}
		}
		.nav-submenu-button{
			display: block;
			a{color: #fff;}
		}
		.nav-submenu-col-brands{
			padding: 30px 15px; background: var(--colorLightBlueBackground); border-bottom: 1px solid var(--colorBorderLightForms);
			.nav-submenu-title{display: block; font-size: 19px; font-weight: bold;}
			.nav-submenu-subtitle{display: block; font-size: 15px;}
			.nav-submenu-items{
				display: flex; margin: 15px -15px 20px; padding: 0 15px; overflow-x: auto;
				&::-webkit-scrollbar{height: 5px;}
				&::-webkit-scrollbar-thumb{height: 5px; background: var(--colorBaseBlue); border-radius: 5px;}
				&::-webkit-scrollbar-track{margin: 15px; background: #fff; border-radius: 5px;}
				.nav-submenu-image{display: block;}
				a{
					border: 0; background: #fff; border-right: 1px solid var(--colorBorderLightForms); width: 150px; display: flex; align-items: center; justify-content: center; text-align: center; height: 90px;
					img{width: 90%; display: block; margin: auto;}
					&.has-image>span{display: none;}
					&:before, &:after{display: none!important;}
				}
				li:last-child a{border: 0;}
			}
		}
		.nav-submenu-title{display: none;}
		.wrapper-submenu{padding: 0;}
		.nav-submenu{
			flex-wrap: wrap; font-size: 15px; line-height: 1.3;
			&>li{
				width: 50%; margin: 0; border-right: 1px solid var(--colorBorderLightForms); border-bottom: 1px solid var(--colorBorderLightForms); aspect-ratio: 1.6/1;
				.nav-lvl2{
					color: #000; height: 100%; width: 100%; display: flex; align-items: center; justify-content: center;
					.img{display: flex; margin-inline: auto;}
					&:after{display: none!important;}
				}
				&>a{height: 100%;}
				&.discount>a span{padding: 0; color: var(--colorRed);}
			}
		}
		.outlet, .akcije{
			.nav-submenu{
				display: block;
				&>li{width: 100%; aspect-ratio: initial; padding: 0; border: 0; border-bottom: 1px solid var(--colorBorderLightForms);}
				a{padding: 14px 15px; display: block;}
			}
		}
	}
</style>
