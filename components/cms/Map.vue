<template>
	<div v-if="items" :class="mapClass" id="map-content">
		<BaseLocationGoogleMap :locations="items">
			<template v-slot="{item}">
				<div class="infoBox">
					<span class="title">{{ item.title }}</span>
					<span v-if="item.address" class="address" v-html="item.address" />
					<span v-if="item.contact" class="contact" v-html="item.contact" />
				</div>
			</template>
		</BaseLocationGoogleMap>
	</div>
</template>

<script setup>
	const props = defineProps(['items', 'extraclass']);
	let items = props.items;
	let mapClass = 'map-content';

	if(props.extraclass){
		mapClass = 'map-content ' + props.extraclass;
	}
</script>

<style lang="less" scoped>
	.map-content-contact{margin: 0 auto; position: relative; z-index: 1;}
	.map{
		position: relative; height: 520px; width: 100%;
		:deep(button){box-shadow: none; padding: 0;}
		:deep(button[title="Close"]) {display: none!important;}
		/*:deep(img)[src*="googleapis.com"]{filter: grayscale(1);}*/
		@media (max-width: @m){height: 424px;}
	}
	:deep(.infoBox-window) {
		position: relative; left: 40px; top: -20px;
		.infoBox-close{
			position: absolute; display: flex; align-items: center; justify-content: center; top: 26px; right: 25px; width: 20px; height: 20px; cursor: pointer; z-index: 100; font-size: 0;
			&:before{.icon-close; font: 15px/1 var(--fonti); color: var(--colorRed);}
			@media (max-width: @m){
				top: 18px; right: 19px;
				&:before{font-size: 14px;}
			}
		}

		.infoBox {
			width: 270px; box-shadow: 0 10px 40px 0 rgba(1, 61, 112, 0.3); position: relative; background: #fff; font-size: 14px; line-height: 1.4; font-weight: normal; padding: 27px 32px 19px 32px; border-radius: 4px;
			&:before{.pseudo(10px,10px); background: #fff; .rotate(45deg); top: 31px; left: -5px;}
			p{padding-bottom: 10px;}
			&>span{
				display: block; padding-bottom: 10px;
				p:last-child{padding: 0;}
			}
			.title{font-weight: bold; padding-right: 30px;}
			a{
				color: var(--colorBase); text-decoration: underline; text-decoration-color: var(--colorBaseBlue); text-underline-offset: 3px; .transition(color);
				&:hover{color: var(--colorBaseBlue);}
			}
			a[href^='tel']{cursor: default; color: var(--colorBase); text-decoration: none;}
			.mail, .tel {
				padding-left: 25px; position: relative; display: flex; align-items: center;
				&:before{font: 15px/15px var(--fonti); color: var(--colorBaseBlue); position: absolute; left: 0;}
			}
			.mail {
				padding-top: 5px;
				&:before{.icon-mail(); font: 11px/11px var(--fonti);}
			}
			@media (max-width: @m){
				width: 224px; padding: 20px 20px 10px 24px; box-shadow: 0 0 30px 0 rgba(0, 12, 26, 0.18); font-size: 13px;
				p{padding-bottom: 10px;}
				&>span{
					padding-bottom: 2px;
					p:last-child{padding: 10px;}
				}
				.title{padding-bottom: 2px; padding-right: 20px;}
			}
		}
	}
	.infoBox-cnt {
		padding: 20px 25px 10px; background: #fff; margin: 0; display: block; position: relative; width: auto; font-size: 14px; line-height: 19px;
		&:before{.pseudo(10px,10px); background: #fff; .rotate(45deg); left: -5px; top: 25px;}
	}
</style>
