<template>
	<div class="homepage-categories" v-if="items?.length">
		<div class="wrapper homepage-categories-wrapper">
			<div class="homepage-categories-title"><BaseCmsLabel code="homepage_categories" /></div>
			<div class="homepage-categories-list">
				<div class="homepage-categories-item" v-for="item in items" :key="item.id" :class="item.element_class">
					<NuxtLink v-if="item.link" :to="item.url_without_domain">
						<div class="homepage-categories-item-image"><BaseUiImage :data="item.image_thumbs?.['width210-height210']" default="/images/no-image-104.png" loading="lazy" /></div>
						<div class="homepage-categories-item-title">{{item.title}}</div>
					</NuxtLink>
					<div v-else>
						<div class="homepage-categories-item-image">
							<BaseUiImage :data="item.image_thumbs?.['width210-height210']" default="/images/no-image-104.png" loading="lazy" />
						</div>
						<div class="homepage-categories-item-title">{{item.title}}</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	const props = defineProps(['items']);
</script>

<style lang="less" scoped>
	// homepage popular categories
	.homepage-categories{
		position: relative; display: block; width: 100%; margin: 66px 0 72px; height: 210px;
		@media (max-width: @t){margin: 56px 0; height: 168px;}
		@media (max-width: @m){display: none;}
	}
	.homepage-categories-title{
		display: block; font-size: var(--fontSizeH3); line-height: 1.05; font-weight: bold; margin-bottom: 20px; height: 30px;
		@media (max-width: @t){margin: 0 0 16px; font-size: var(--fontSizeH1);}
		@media (max-width: @m){font-size: 19px; line-height: 23px;}
	}
	.homepage-categories-list{
		position: relative; display: flex; gap: 8px;
		@media (max-width: @m){flex-wrap: wrap;}
	}
	.homepage-categories-item{
		position: relative; display: flex; width: calc(~"100% / 7"); flex-grow: 1; flex-shrink: 1;
		&.discount{
			margin-right: 0;
			a, &>div{background: var(--colorRed); transition: box-shadow 0.3s, background 0.3s;}
			@media (min-width: @h){
				a:hover{
					background: var(--colorRed);
					.homepage-categories-item-title{color: var(--colorWhite);}
				}
			}
			.homepage-categories-item-title{color: var(--colorWhite);}
		}
		a, &>div{display: flex; flex-flow: column; align-items: center; width: 100%; background: var(--colorLightBlueBackground); border-radius: var(--inputBorderRadius); padding: 20px; box-shadow: 0 8px 20px rgba(255,255,255,0); transition: box-shadow 0.3s, background 0.3s;}
		a{text-decoration: none;}
		@media (min-width: @h){
			a:hover{
				background: var(--colorWhite); box-shadow: 0 8px 20px rgba(0,89,194,0.1);
				.homepage-categories-item-title{color: var(--colorBaseBlue);}
			}
		}
		@media (max-width: @m){
			width: calc(~"33.3333% - 6px");
			a, &>div{padding: 8px 8px 14px;}
			&:nth-child(3n){margin-right: 0;}
			&.discount{
				width: 100%; margin: 8px 0 0; height: 48px;
				a, &>div{flex-flow: row; justify-content: center; padding: 0;}
				.homepage-categories-item-image{display: none;}
				.homepage-categories-item-title{
					position: relative; padding-left: 33px; font-size: 15px; line-height: 19px; max-width: inherit; flex-grow: 0; flex-shrink: 1;
					&:before{.pseudo(25px,25px); background: url(/assets/images/discount.svg) center no-repeat; background-size: cover; left: 0; top: -3px;}
				}
			}
		}
	}
	.homepage-categories-item-image{
		position: relative; display: flex; align-items: center; justify-content: center; width: 104px; height: 104px;
		:deep(img){
			display: block; width: auto; height: auto; max-width: 104px; max-height: 104px;
			@media (max-width: @t){max-width: 100%;}
		}
		@media (max-width: @t){width: 67px; height: 67px;}
		@media (max-width: @m){width: 88px; height: 88px;}
	}
	.homepage-categories-item-title{
		display: block; font-size: var(--fontSizeSmall); line-height: 1.2; text-decoration: none!important; color: var(--colorBase); font-weight: bold; max-width: 110px; text-align: center; display: flex; align-items: center; justify-content: center; flex-grow: 1; flex-shrink: 1; .transition(color);
		@media (max-width: @t){font-size: 11px; line-height: 14px;}
		@media (max-width: @m){font-size: 13px; line-height: 17px; align-items: flex-start;}
	}
</style>
