<template>
	<BaseCmsRotator :fetch="{code: 'benefits', limit: benefitWidgetLimit, response_fields: ['id','link','title','image','image_upload_path']}" v-slot="{items}">
		<div :class="benefitsWidgetClass" v-if="items?.length">
			<div class="wrapper benefits-widget-wrapper" v-if="props.extclass == 'special'">
				<BaseUiSwiper class="benefits-widget-items benefits-widget-items-special" name="benefits-swiper" :options="sliderOptions">
					<BaseUiSwiperSlide v-for="item in items" :key="item.id" class="benefits-widget-item">
						<NuxtLink v-if="item.link" :to="item.link">
							<div class="benefits-widget-item-title">{{ item.title }}</div>
						</NuxtLink>
						<template v-else>
							<div class="benefits-widget-item-title">{{ item.title }}</div>
						</template>
					</BaseUiSwiperSlide>
				</BaseUiSwiper>
			</div>
			<div v-else class="wrapper benefits-widget-wrapper">
				<div class="benefits-widget-col benefits-widget-col1"><BaseCmsLabel code="benefits_widget_title" /></div>
				<div class="benefits-widget-col benefits-widget-col2">
					<div class="benefits-widget-items">
						<div class="benefits-widget-item" v-for="item in items" :key="item.id">
							<NuxtLink v-if="item.link" :to="item.link">
								<div v-if="item.image" class="benefits-widget-item-image"><BaseUiImage :src="item.image_upload_path" width="48" height="48" loading="lazy" /></div>
								<div class="benefits-widget-item-title">{{ item.title }}</div>
							</NuxtLink>
							<template v-else>
								<div v-if="item.image" class="benefits-widget-item-image"><BaseUiImage :src="item.image_upload_path" width="48" height="48" loading="lazy" /></div>
								<div class="benefits-widget-item-title">{{ item.title }}</div>
							</template>
						</div>
					</div>
				</div>
			</div>
		</div>
	</BaseCmsRotator>
</template>

<script setup>
	const props = defineProps(['extclass']);

	let benefitWidgetLimit = 4;
	let benefitsWidgetClass = 'benefits-widget';

	if(props.extclass){
		benefitsWidgetClass = 'benefits-widget benefits-widget-'+props.extclass;
	}

	const sliderOptions = {
		enabled: true,
		slidesPerView: 1,
		slidesPerGroup: 1,
		rewind: true,
		navigation: {enabled: false},
		autoplay: {delay: 5000, disableOnInteraction: true},
		breakpoints: {
			980: {
				enabled: false,
			}
		}
	}
</script>

<style lang="less" scoped>
	.benefits-widget {
		position: relative; display: block; width: 100%; background: var(--colorBaseBlue); padding: 93px 0; overflow: hidden;
		&:before {
			.pseudo(401px,344px); top: -27px; left: 412px; left: 21.5vw; background: url(/assets/images/bike.svg) center no-repeat; background-size: cover; opacity: 0.9;
			@media (max-width: @l){left: 11vw;}
			@media (max-width: @t){width: 315px; height: 271px; left: 10vw;}
			@media (max-width: @m){width: 233px; height: 195px; top: auto; right: -45px; bottom: -2px; left: auto;}
		}
		@media (max-width: @t){padding: 65px 0;}
		@media (max-width: @m){padding: 32px 0 28px;}
	}
	.benefits-widget-wrapper {
		position: relative; z-index: 1; display: flex; align-items: center; justify-content: space-between;
		@media (max-width: @m){flex-flow: column; justify-content: flex-start; align-items: flex-start; gap: 16px;}
	}
	.benefits-widget-col1 {
		width: 400px; font-size: var(--fontSizeH2); line-height: 1.3; color: var(--colorWhite); font-weight: bold; flex-grow: 0; flex-shrink: 0;
		@media (max-width: @t){font-size: 24px; line-height: 30px; width: 335px;}
		@media (max-width: @m){width: 240px; font-size: 19px; line-height: 25px;}
	}
	.benefits-widget-col2 {
		position: relative; display: flex; align-items: flex-start; width: 755px; justify-content: flex-end; gap: 80px;
		@media (max-width: @t){width: 645px; gap: 32px;}
		@media (max-width: @m){flex-flow: column; align-items: flex-start; justify-content: flex-start; width: 100%; gap: 11px;}
	}
	.benefits-widget-items {
		position: relative; display: flex; align-items: flex-start; justify-content: space-between; width: 100%;
		@media (max-width: @m){flex-flow: column; align-items: center; justify-content: flex-start; gap: 10px}
	}
	.benefits-widget-items-special{
		@media (min-width: @m){
			:deep(.splide__track){display: flex; width: 100%;}
			:deep(.splide__list){display: flex!important; justify-content: space-between; width: 100%;}
			:deep(.swiper){width: 100%;}
			.swiper-slide{width: auto!important;}
			:deep(.swiper-wrapper){display: flex; justify-content: space-between;}
		}
	}
	.benefits-widget-item {
		display: flex; flex-flow: column; align-items: center; color: var(--colorWhite); width: 140px; text-align: center; line-height: 1.5;
		:deep(a){display: flex; flex-flow: column; align-items: center; color: var(--colorWhite); text-align: center; text-underline-offset: 3px; text-decoration: none;
			&:hover{text-decoration: underline;}
		}
		@media (max-width: @t) {
			width: 112px;
			:deep(a){text-underline-offset: 1px;}
		}
		@media (max-width: @m) {
			max-width: 100%; width: 100%; flex-flow: row; align-items: center; justify-content: flex-start;
			:deep(a){text-underline-offset: 1px; flex-flow: row; align-items: center; justify-content: flex-start;}
		}
	}
	.benefits-widget-item-image {
		display: flex; align-items: center; justify-content: center; width: 48px; height: 48px; margin-bottom: 8px;
		@media (max-width: @t){width: 35px; height: 40px;}
		@media (max-width: @m){width: 25px; height: 30px; margin-right: 15px; margin-bottom: 0;}
	}
	:deep(.benefits-widget-item-image img) {display: block; width: auto; height: auto; max-width: 100%; max-height: 100%;}
	.benefits-widget-item-title {
		display: block; font-size: var(--fontSizeSmall); font-weight: bold;
		@media (max-width: @t){font-size: var(--fontSizeLabelSmall); line-height: 15px;}
		@media (max-width: @m){font-size: 13px; line-height: 22px; font-weight: normal;}
	}

	// special benefits
	.benefits-widget-special{
		background: var(--colorBase); padding: 0; height: 56px;
		&:before{display: none;}
		.wrapper{height: 100%;}
		.benefits-widget-item{
			flex-flow: row; text-align: left; width: auto;
			a{flex-flow: row; text-align: left;}
			&:nth-child(2){
				.benefits-widget-item-title:before{.icon-cards(); font-size: 27px;}
			}
			&:nth-child(3){
				.benefits-widget-item-title:before{.icon-guarantee();}
			}
			&:nth-child(4){
				.benefits-widget-item-title:before{.icon-service(); font-size: 24px;}
			}
		}
		.benefits-widget-item-title{
			position: relative; padding-left: 40px; min-height: 30px; display: flex; align-items: center;
			&:before{.pseudo(30px,30px); .icon-truck(); font: 30px/30px var(--fonti); color: var(--colorBaseLighter); display: flex; align-items: center; justify-content: center; left: 0;}
		}

		@media (max-width: @t){
			height: 46px;
			.benefits-widget-items{flex-flow: row; justify-content: space-between; height: 100%; align-items: center;}
			.benefits-widget-item{
				&:nth-child(2){
					.benefits-widget-item-title:before{font-size: 22px;}
				}
				&:nth-child(4){
					.benefits-widget-item-title:before{font-size: 20px;}
				}
			}
			.benefits-widget-item-title{
				padding-left: 32px; min-height: 24px;
				&:before{width: 24px; height: 24px; font-size: 24px; line-height: 24px;}
			}
		}

		@media (max-width: @m){
			height: 48px;
			.wrapper{padding: 0 24px;}
			.benefits-widget-items{
				flex-flow: row; justify-content: space-between; height: 100%; align-items: center;
				:deep(.splide__track){height: 100%;}
			}
			:deep(.benefits-widget-item){display: flex; align-items: center; height: 100%;}
			.benefits-widget-item-title{
				padding-left: 32px;
				&:before{width: 24px; height: 24px; font-size: 24px; line-height: 24px;}
			}
		}
	}
</style>
