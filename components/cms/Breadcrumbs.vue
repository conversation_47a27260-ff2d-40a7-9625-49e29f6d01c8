<template>
	<div class="bc" :class="{'bc-short': mode == 'catalogDetail'}">
		<div class="wrapper-bc" :class="{'wrapper-cd-bc': mode == 'catalogDetail'}">
			<div class="bc-items">
				<span v-for="(item, index) in items" :key="item" class="bc-item">
					<NuxtLink v-if="index != items.length - 1" :to="item.url_without_domain">
						<span>{{ item.title }}</span>
					</NuxtLink>
					<span v-else class="bc-last">{{ item.title }}</span>
				</span>
			</div>
		</div>
	</div>
</template>

<script setup>
	const props = defineProps(['items', 'mode']);
</script>

<style lang="less" scoped>
	.bc {
		font-size: var(--fontSizeLabel); padding-bottom: 10px;
		a {
			display: block; position: relative; padding: 0 10px 0 0; margin: 0 4px 0 0; text-underline-offset: 3px;
			&:after { .icon-arrow2(); font: 7px/7px var(--fonti); color: var(--colorTextLightGray); position: absolute; top: 6px; right: 0; .rotate(180deg);}
		}
		span {
			display: block;
			@media (max-width: @m){white-space: nowrap;}
		}
	}
	.wrapper-bc{
		position: relative;
		@media (max-width: @m){
			&:after{ .pseudo(60px,24px); top: 0px; right: -16px; background: linear-gradient(270deg, #FFFFFF 20%, rgba(255,255,255,0) 100%);}
		}
	}
	.bc-items {
		display: flex; flex-wrap: wrap;
		@media (max-width: @m) {
			flex-wrap: nowrap; width: calc(~'100% - -16px'); overflow-y: hidden; white-space: normal; -webkit-overflow-scrolling: touch;
			&::-webkit-scrollbar { display: none; }
			&::-webkit-scrollbar-thumb { display: none; }
		}
	}
	.bc-last {color: var(--colorTextLightGray); padding-right: 40px;}
	.bc-short {
		span:nth-last-child(2) a {
			margin-right: 0; padding-right: 0;
			&:after { content: none; }
		}
		.bc-last { display: none; }
	}
</style>
