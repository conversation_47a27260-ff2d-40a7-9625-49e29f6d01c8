<template>
	<BaseUiPageLoadingIndicator v-slot="{loading, percentage}">
		<Transition name="fade">
			<div class="page-loading" v-show="loading && percentage > 1">
				<div class="page-loading-bar" :style="{width: percentage + '%'}" />
			</div>
		</Transition>
	</BaseUiPageLoadingIndicator>
</template>

<style scoped lang="less">
	.page-loading{height: 2px; background: rgba(255,255,255,.7); position: fixed; top: 0; left: 0; right: 0; z-index: 9999999; box-shadow: 0px 0px 3px rgba(0,0,0,.6);}
	.page-loading-bar{background: var(--colorBaseBlue); height: 100%; .transition(width);}

	/* animations */
	.fade-enter-active,
	.fade-leave-active {
		transition: opacity 0.2s ease;
	}

	.fade-enter-from,
	.fade-leave-to {
		opacity: 0;
	}
</style>
