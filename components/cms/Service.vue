<template>
	<div class="service-widget" v-for="item in items" :key="item.id">
		<NuxtLink :to="item.link" class="service-widget-main" v-if="item.link">
			<BaseUiImage :data="item.image_thumbs?.['width1920-height432']" default="/images/no-image-1920.png" loading="lazy" :picture="[{maxWidth: '980px', src: item.image_2_thumbs?.['width980-height762-crop1']?.thumb, default: '/images/no-image-980.jpg'}]" />
			<div class="service-widget-content">
				<div class="wrapper">
					<div class="service-widet-title">{{item.title}}</div>
					<div class="btn service-widget-btn">{{item.title2}}</div>
				</div>
			</div>
		</NuxtLink>
		<div class="service-widget-main" v-else>
			<BaseUiImage :data="item.image_thumbs?.['width1920-height432']" default="/images/no-image-1920.png" loading="lazy" :picture="[{maxWidth: '980px', src:  item.image_2_thumbs?.['width980-height762-crop1']?.thumb, default: '/images/no-image-980.jpg'}]" />
			<div class="service-widget-content">
				<div class="wrapper">
					<div class="service-widet-title">{{item.title}}</div>
					<div class="btn service-widget-btn">{{item.title2}}</div>
				</div>
			</div>
		</div>
		<div class="service-widget-bottom" v-if="item.content">
			<div class="wrapper">
				<div class="service-widgt-bottom-cnt">
					<div v-html="item.content"></div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	const props = defineProps(['items']);
</script>

<style lang="less" scoped>
	// service widget
	.service-widget{position: relative; display: flex; flex-flow: column; max-width: 1920px; margin: auto;}
	.service-widget-image{
		display: block; width: 100%; height: auto;
		:deep(p){padding-bottom: 0;}
	}
	.service-widget-main{
		position: relative; display: block; line-height: 0;
		picture{position: relative; display: block; width: 100%; height: auto;
			img{display: block; width: auto; height: auto; max-width: 100%;}
		}
	}
	.service-widget-content{
		position: absolute; top: 0; right: 0; bottom: 0; left: 0; display: flex; align-items: flex-end; padding-bottom: 55px; line-height: var(--lineHeight);
		.wrapper{margin: 0 auto; width: var(--pageWidth);}
		@media (max-width: @t){padding-bottom: 48px;}
		@media (max-width: @m){
			padding: 24px;
			.wrapper{padding: 0; display: flex; flex-flow: column; align-items: flex-start;}
		}
	}
	.service-widet-title{
		display: block; font-size: var(--fontSizeDisplaySmall); line-height: 1.13; color: var(--colorWhite); font-weight: bold; text-shadow: 1px 2px 0 rgba(0,12,26,0.5); max-width: 470px;
		@media (max-width: @t){font-size: 38px; line-height: 42px;}
		@media (max-width: @m){font-size: 23px; line-height: 28px; max-width: 280px;}
	}
	.service-widget-btn{
		margin-top: 23px;
		@media (max-width: @t){font-size: var(--fontSizeSmall); margin-top: 15px; padding: 10px 23px;}
		@media (max-width: @m){margin-top: 18px;}
	}
	.service-widget-bottom{
		position: relative; display: block; width: 100%; height: auto; background: var(--colorBase); padding: 22px 0;
		@media (max-width: @t){padding: 19px 0;}
	}
	.service-widgt-bottom-cnt{
		position: relative; display: block; color: var(--colorWhite); padding-left: 48px;
		&:before{.pseudo(32px,32px); .icon-service2(); font: 32px/32px var(--fonti); color: var(--colorBaseBlue); top: -3px; left: 0;}
		:deep(p){padding-bottom: 0;}
		@media (max-width: @t){
			font-size: var(--fontSizeSmall); line-height: 17px; padding-left: 32px;
			&:before{width: 24px; height: 24px; font-size: 24px; line-height: 24px;}
		}
		@media (max-width: @m){
			padding-left: 35px;
			:deep(strong){display: block;}
		}
	}
</style>
