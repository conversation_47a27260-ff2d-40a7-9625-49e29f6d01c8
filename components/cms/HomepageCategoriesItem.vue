<template>
	<BaseUiLink :href="item.url_without_domain" native :prevent-default="item.items?.length ? true : false" class="homepage-categories-list-title" @click="active = !active" :class="[{'active': active === true}, item.style]" :to="item.url_without_domain">
		<span class="homepage-categories-list-image"><BaseUiImage :src="item.image_upload_path" width="30" height="30" :alt="item.title" default="/images/no-image-80.jpg" /></span>
		<span class="title">{{item.title}}</span>
		<span class="icon-dropdown" v-if="item.items?.length"></span>
	</BaseUiLink>

	<div class="homepage-categories-list-lv4" v-if="item.items?.length" :class="{'active': active === true}">
		<div class="homepage-categories-list-lv5" v-for="item in item.items" :key="item.id">
			<template v-if="item.items?.length">
				<NuxtLink :to="item.url_without_domain" class="homepage-categories-list-lv6" :class="item.style" v-for="item in item.items" :key="item.id">
					<span>{{item.title}}</span>
				</NuxtLink>
			</template>
		</div>
	</div>
</template>

<script setup>
	const props = defineProps(['item']);
	const active = ref(0);
</script>

<style lang="less" scoped>
	.homepage-categories-list-lv4{
		display: flex; flex-flow: column; padding: 0 20px 0 70px; max-height: 0; overflow: hidden; transition: max-height 0.3s, padding 0.3s;
		&.active{max-height: inherit; padding: 0 20px 16px 70px;}
	}
	.homepage-categories-list-lv5{
		display: flex; flex-flow: column;
		&:nth-child(2){display: none;}
		a{
			display: block; color: var(--colorBase); font-size: 15px; line-height: 19px; text-decoration: none; margin-bottom: 12px;
			&:last-child{margin-bottom: 0;}
			&.bold{font-weight: bold;}
			&.discount{color: var(--colorRed);}
			&.show_all{display: none;}
			&.show_all.mobile{display: block;}
			@media (max-width: @m){margin-bottom: 15px;}
		}
	}
	.homepage-categories-list-title{
		display: flex; align-items: center; position: relative; font-size: 15px; line-height: 1.4; font-weight: bold; padding: 13px 16px; color: var(--colorBase); text-decoration: none; .transition(color);
		&.active{
			color: var(--colorBaseBlue);
			.icon-dropdown:after{opacity: 0;}
		}
		&.discount{color: var(--colorRed);}
	}
	.homepage-categories-list-image{
		width: 40px; height: 28px; display: flex; align-items: center; justify-content: center; margin-right: 13px;
		:deep(img){display: block; width: 100%;	max-height: 100%;}
	}
	.title{flex-grow: 1;}
	.icon-dropdown{
		display: block; width: 10px; height: 10px; position: relative;
		&:before{.pseudo(10px,2px); background: var(--colorBaseBlue); left: 0; top: calc(~"50% - 1px");}
		&:after{.pseudo(2px,10px); background: var(--colorBaseBlue); left: calc(~"50% - 1px"); top: 0;}
	}
</style>
