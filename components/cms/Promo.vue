<template>
	<div class="promo-widget">
		<div class="wrapper promo-widget-wrapper">
			<div class="promo-items">
				<div class="promo-item" v-for="item in items" :key="item.id" :class="[!item.template ? '' : item.template]">
					<component :is="item.link ? NuxtLink : 'div'" :to="item.link ? item.url_without_domain : undefined" :class="['promo-item-main', {link: item.link}]">
						<div class="promo-item-image">
							<template v-if="item.template === 'medium_promo'">
								<BaseUiImage :data="item.image_thumbs?.['width632-height416-crop1']" default="/images/no-image-632.png" loading="lazy" :picture="[{maxWidth: '980px', src: item.image_2_thumbs?.['width980-height980-crop1']?.thumb, default: '/images/no-image-980.jpg'}]" />
							</template>
							<template v-else-if="item.template === 'big_promo'">
								<BaseUiImage :data="item.image_thumbs?.['width1280-height424-crop1']" default="/images/no-image-1280.png" loading="lazy" :picture="[{maxWidth: '980px', src: item.image_2_thumbs?.['width980-height980-crop1']?.thumb, default: '/images/no-image-980.jpg'}]" />
							</template>
							<template v-else>
								<BaseUiImage :data="item.image_thumbs?.['width416-height416-crop1']" default="/images/no-image-416.png" loading="lazy" :picture="[{maxWidth: '980px', src: item.image_2_thumbs?.['width980-height980-crop1']?.thumb, default: '/images/no-image-980.jpg'}]" />
							</template>
						</div>
						<div class="promo-item-content" v-if="item.title2 || item.title || item.content">
							<div class="promo-item-headline" v-if="item.title2">{{item.title2}}</div>
							<div class="promo-item-title" v-if="item.title">{{item.title}}</div>
							<div class="promo-item-cnt">
								<div class="btn">{{item.content ? item.content : labels.get('read_more')}}</div>
							</div>
						</div>
					</component>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	import { NuxtLink } from '#components';
	const props = defineProps(['items']);
	const labels = useLabels();
</script>

<style lang="less" scoped>
	// specific
	.homepage-promo-widget{
		position: relative; display: block; margin: 0 0 70px;
		&:before{.pseudo(100%,auto); top: 48px; bottom: 48px; left: 0; background: var(--colorLightBlueBackground);}
		@media (max-width: @t){
			margin: 0 0 56px;
			&:before{top: 32px; bottom: 32px;}
		}
		@media (max-width: @m){
			margin: 0 0 40px;
			&:before{display: none;}
		}
	}
	.category-promos{
		padding: 32px 0 19px;
		@media (max-width: @t){
			padding: 32px 0 15px;
			.wrapper{padding: 0;}
		}
		@media (max-width: @m){
			padding: 24px 0 0;
			.promo-items{
				flex-flow: row; width: calc(~"100% - -32px"); margin-left: -16px; overflow-y: hidden; padding: 0 16px;
				&::-webkit-scrollbar{-webkit-appearance: none; height: 0; background: transparent; z-index: 0;}
				&::-webkit-scrollbar-thumb{background-color: transparent;}
			}
			.promo-item{width: 80%!important; min-width: 80%!important;}
		}
		@media (max-width: @m){
			.promo-item-content{padding: 18px!important;}
			.promo-item-headline{font-size: 10px; line-height: 1.2;}
			.promo-item-title{font-size: 18px!important; line-height: 1.3!important;}
		}
	}

	// general
	.promo-widget-wrapper{position: relative; z-index: 1;}
	.promo-items{
		display: flex; flex-wrap: wrap; gap: 16px;
		@media (max-width: @m){flex-flow: column;}
	}
	.promo-item{
		position: relative; flex-grow: 1; flex-shrink: 1; width: calc(~"33.3333% - 11px");
		&.medium_promo{width: calc(~"50% - 8px");}
		&.big_promo{
			width: 100%; margin-right: 0;
			.promo-item-content{padding: 48px 56px;}
			.promo-item-headline{margin-bottom: 5px;}
			.promo-item-title{max-width: 360px; font-size: 39px; line-height: 1.2;}
			.promo-item-cnt{margin-top: 16px;}
		}
		@media (max-width: @t){
			&.big_promo{
				.promo-item-content{padding: 40px 48px}
				.promo-item-headline{margin-bottom: 5px;}
				.promo-item-title{max-width: 300px; font-size: 32px; line-height: 1.2;}
				.promo-item-cnt{margin-top: 10px;}
			}
		}
		@media (max-width: @m){
			width: 100%;
			&.medium_promo{width: 100%;}
			&.big_promo{
				.promo-item-content{padding: 32px 24px}
				.promo-item-headline{font-size: 13px; margin-bottom: 7px;}
				.promo-item-title{font-size: 23px;}
			}
		}
	}
	.promo-item-main{display: flex;}
	.promo-item-image{
		position: relative; display: block;
		:deep(img){display: block; width: auto; height: auto; max-width: 100%; border-radius: var(--inputBorderRadius);}
	}
	.promo-item-content{
		position: absolute; top: 0; right: 0; bottom: 0; left: 0; padding: 48px 40px; display: flex; flex-flow: column; justify-content: flex-end;
		@media (max-width: @t){padding: 24px 32px;}
		@media (max-width: @m){padding: 32px 24px; align-items: flex-start;}
	}
	.promo-item-headline{
		display: block; color: var(--colorWhite); line-height: 1.1; font-weight: bold; text-shadow: 1px 2px 0 rgba(0,12,26,0.5); margin-bottom: 8px;
		@media (max-width: @t){font-size: var(--fontSizeLabel); margin-bottom: 5px;}
	}
	.promo-item-title{
		display: block; color: var(--colorWhite); font-weight: bold; text-shadow: 1px 2px 0 rgba(0,12,26,0.5); font-size: var(--fontSizeH2); line-height: 1.2;
		@media (max-width: @t){font-size: 23px;}
	}
	.promo-item-cnt{
		display: block; margin-top: 18px;
		.btn{padding-top: 9px; padding-bottom: 9px;}
		@media (max-width: @t){
			font-size: var(--fontSizeSmall); margin-top: 8px;
			.btn{font-size: var(--fontSizeSmall)};
		}
	}
</style>
