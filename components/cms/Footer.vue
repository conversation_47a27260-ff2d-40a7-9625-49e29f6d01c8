<template>
	<div class="footer">
		<div class="wrapper footer-wrapper">
			<div class="footer-row footer-row1">
				<div class="footer-col footer-col1" :class="{ active: fCol1 }">
					<div class="f-col-title" @click="fCol1 = !fCol1"><BaseCmsLabel code="helo_and_support" /><span class="icon-dropdown"></span></div>
					<div class="f-support">
						<BaseCmsLabel code="support_info" tag="div" />
						<BaseCmsLabel code="faq_link" class="faq-link" tag="div" v-interpolation />
					</div>
				</div>
				<div class="footer-col footer-col2" :class="{ active: fCol2 }">
					<div class="f-col-title" @click="fCol2 = !fCol2"><BaseCmsLabel code="footer_col_info" /> <span class="icon-dropdown"></span></div>
					<ul class="nav nav-footer" v-if="footerMenu1?.items?.length">
						<li v-for="item in footerMenu1.items" :key="item.id">
							<NuxtLink :target="item.target_blank != 0 ? '_blank' : null" :to="item.url_without_domain">{{ item.title }}</NuxtLink>
						</li>
					</ul>
				</div>
				<div class="footer-col footer-col3" :class="{ active: fCol3 }">
					<div class="f-col-title" @click="fCol3 = !fCol3"><BaseCmsLabel code="footer_col_terms" /> <span class="icon-dropdown"></span></div>
					<ul class="nav nav-footer" v-if="footerMenu3?.items?.length">
						<li v-for="item in footerMenu3.items" :key="item.id">
							<NuxtLink :target="item.target_blank != 0 ? '_blank' : null" :to="item.url_without_domain">{{ item.title }}</NuxtLink>
						</li>
						<li>
							<a role="button" class="gdpr_configurator_button"><BaseCmsLabel code="gdpr_edit" /></a>
						</li>
					</ul>
				</div>
				<div class="footer-col footer-col4" :class="{ active: fCol4 }">
					<div class="f-col-title" @click="fCol4 = !fCol4"><BaseCmsLabel code="footer_col_gift_card" /> <span class="icon-dropdown"></span></div>
					<ul class="nav nav-footer" v-if="footerMenu4?.items?.length">
						<li v-for="item in footerMenu4.items" :key="item.id">
							<NuxtLink :target="item.target_blank != 0 ? '_blank' : null" :to="item.url_without_domain">{{ item.title }}</NuxtLink>
						</li>
					</ul>
				</div>
				<div class="footer-col footer-col5" :class="{ active: fCol5 }">
					<div class="payment-note-container" @click="fCol5 = !fCol5">
						<div class="payment-note-title"><BaseCmsLabel code="payment_note_title" /> <span class="icon-dropdown"></span></div>
						<BaseCmsLabel code="payment_note_content" tag="div" class="payment-note-content" />
					</div>
					<div class="cards" v-html="labels.get('cards')" />
				</div>
				<div class="footer-col footer-col6"><BaseCmsLabel code="footer_badges" tag="div" /></div>
			</div>

			<div class="footer-row footer-row2">
				<BaseCmsLabel code="social_links" tag="div" class="social-links" v-interpolation />
				<div class="copyright">
					<BaseCmsCopyright class="copy" label="copyright" />
					<BaseCmsSignature class="dev" />
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	const labels = useLabels();
	const menus = useMenus();

	const nav = await menus.fetch({code: 'footer_menu1,footer_menu3,footer_menu4'});
	const footerMenu1 = computed(() => nav.data?.find(item => item.code == 'footer_menu1'));
	const footerMenu3 = computed(() => nav.data?.find(item => item.code == 'footer_menu3'));
	const footerMenu4 = computed(() => nav.data?.find(item => item.code == 'footer_menu4'));

	let fCol1 = ref(false);
	let fCol2 = ref(false);
	let fCol3 = ref(false);
	let fCol4 = ref(false);
	let fCol5 = ref(false);

	const {insertAfter, appendTo, onMediaQuery} = useDom();
	onMediaQuery({
		query: '(max-width: 990px)',
		enter: () => {
			insertAfter('.cards', '.footer-row1');
			appendTo('.footer-col6', '.footer-row2');
		}
	});
</script>

<style lang="less" scoped>
	.footer{
		position: relative; display: block; width: 100%; background: url(/assets/images/footer.jpg) center no-repeat; background-size: cover; padding: 80px 0 88px;
		@media (max-width: @t){padding: 60px 0 63px;}
		@media (max-width: @m){padding: 0; background: var(--colorBase);}
	}
	.footer-wrapper{
		@media (max-width: @m){padding: 0;}
	}
	.footer-row{
		display: grid; grid-template-columns: 22% 27% 33%; grid-row-gap: 90px; grid-column-gap: 10%;
		@media (max-width: @l){grid-template-columns: 22% 27% 28%;}
		@media (max-width: @t){grid-template-columns: 245px 310px 220px; grid-row-gap: 53px; grid-column-gap: 8%;}
		@media (max-width: @m){display: flex; flex-flow: column; align-items: flex-start; justify-content: flex-start; grid-row-gap: 0; grid-column-gap: 0;}
	}
	.footer-col{
		position: relative; padding-left: 48px; color: var(--colorGrayFooter); font-size: var(--fontSizeSmall);
		@media (max-width: @t){padding-left: 45px; font-size: var(--fontSizeLabel);}
		@media (max-width: @m){
			width: 100%; border-top: 1px solid #1A2431; border-bottom: 1px solid #1A2431; margin-top: -1px; padding: 0; color: var(--colorWhite); .transition(background);
			&.active{
				background: #021328;
				.icon-dropdown:after{opacity: 0;}
				.f-support, .nav-footer, .payment-note-content{max-height: inherit; padding-bottom: 22px; margin-top: -7px;}
			}
		}
	}
	.footer-col1{
		&:before{
			.pseudo(34px,34px); .icon-help(); font: 34px/34px var(--fonti); color: var(--colorBaseLighter); top: -8px; left: 0;
			@media (max-width: @t){width: 32px; height: 32px; font-size: 32px; line-height: 32px;}
			@media (max-width: @m){width: 29px; height: 29px; font-size: 29px; line-height: 29px; display: flex; align-items: center; justify-content: center; top: 11px; left: 16px;}
		}
	}
	.footer-col2{
		&:before{
			.pseudo(30px,30px); .icon-info2(); font: 30px/30px var(--fonti); color: var(--colorBaseLighter); top: -2px; left: 0;
			@media (max-width: @t){width: 27px; height: 27px; font-size: 27px; line-height: 27px; top: -3px; left: 7px;}
			@media (max-width: @m){width: 29px; height: 29px; font-size: 24px; line-height: 24px; display: flex; align-items: center; justify-content: center; top: 13px; left: 16px;}
		}
	}
	.footer-col3{
		&:before{
			.pseudo(28px,28px); .icon-doc(); font: 28px/28px var(--fonti); color: var(--colorBaseLighter); top: -2px; left: 0;
			@media (max-width: @t){width: 26px; height: 26px; font-size: 26px; line-height: 26px; top: -3px; left: 9px;}
			@media (max-width: @m){width: 29px; height: 29px; font-size: 25px; line-height: 25px; display: flex; align-items: center; justify-content: center; top: 12px; left: 16px;}
		}
	}
	.footer-col4{
		&:before{
			.pseudo(32px,23px); .icon-gift(); font: 32px/23px var(--fonti); color: var(--colorBaseLighter); top: 1px; left: 0;
			@media (max-width: @t){width: 30px; height: 22px; font-size: 30px; line-height: 22px; top: -2px;}
			@media (max-width: @m){
				width: 29px; height: 29px; font-size: 28px; line-height: 28px; display: flex; align-items: center; justify-content: center; top: 13px; left: 16px;
			}
		}
		:deep(.icon-dropdown){display: none!important;}
	}
	.footer-col5{
		&:before{
			.pseudo(32px,25px); .icon-cards(); font: 32px/25px var(--fonti); color: var(--colorBaseLighter); top: 1px; left: 0;
			@media (max-width: @t){width: 29px; height: 22px; font-size: 29px; line-height: 22px; top: -3px; left: 5px;}
			@media (max-width: @m){width: 29px; height: 29px; font-size: 28px; line-height: 28px; display: flex; align-items: center; justify-content: center; top: 13px; left: 16px;}
		}
	}
	.footer-col6{
		display: flex; align-items: flex-end; border: 0;
		@media (max-width: @m){
			position: absolute; top: 23px; left: 262px; height: 65px; width: auto;
			:deep(img){max-width: 65px;}
		}
		:deep(p){padding-bottom: 0;}
	}
	.f-support{
		display: flex; flex-flow: column; line-height: 22px;
		:deep(a){
			color: var(--colorGrayFooter); font-weight: bold; text-decoration-color: var(--colorBaseLighter); text-underline-offset: 3px;
			@media (min-width: @h){
				&:hover {text-decoration-color: transparent;}
			}
			&.support-info-tel{text-decoration: none;}
		}
		:deep(.support-info-title){display: none;}
		:deep(.faq-link){display: none;}
		@media (max-width: @t){line-height: 1.5;}
		@media (max-width: @m){
			display: block; padding: 0 58px; max-height: 0; overflow: hidden; font-size: 13px;
			:deep(.support-info-title){display: none;}
			:deep(.support-info-wh){font-size: 15px;}
			:deep(a){
				color: var(--colorWhite); font-size: 15px;
				&.support-info-tel{text-decoration: underline; text-decoration-color: var(--colorBaseLighter);}
			}
			:deep(.faq-link){display: block;}
		}
	}
	:deep(.support-info-title),
	.f-col-title, .payment-note-title{
		display: block; font-size: var(--fontSizeSpecial); line-height: 28px; font-weight: bold; color: var(--colorWhite); margin-bottom: 8px;
		@media (max-width: @t){font-size: 16px; line-height: 1.3; margin-bottom: 9px;}
		@media (max-width: @m){
			position: relative; display: block; font-size: var(--fontSize); padding: 18px 58px; margin-bottom: 0; font-weight: normal;
		}
	}
	:deep(.support-info-desc){
		display: block; margin-top: 18px;
		&:first-of-type{margin-top: 0;}
		@media (max-width: @t){margin-top: 16px;}
	}
	.nav-footer{
		position: relative; display: block; list-style: none;
		li {
			display: block; margin-bottom: 8px;
			&:last-child { margin-bottom: 0; }
			&.selected a { color: var(--colorWhite); }
		}
		a{
			display: block; color: var(--colorGrayFooter); text-decoration-color: transparent; text-underline-offset: 4px; transition: text-decoration-color, color, 0.3s;
			@media (min-width: @h){
				&:hover{color: var(--colorWhite); text-decoration-color: var(--colorBaseLighter);}
			}
		}

		@media (max-width: @m){
			display: block; padding: 0 58px; max-height: 0; overflow: hidden;
			li{
				margin-bottom: 10px;
				&.selected a{color: var(--colorBaseLighter);}
			}
			a{font-size: 13px; line-height: 22px; color: var(--colorWhite);}
		}
	}
	.footer-col2 .nav-footer{
		column-count: 2;
		@media (max-width: @m){column-count: inherit;}
	}
	.payment-note-container{position: relative; display: block;}
	:deep(.payment-note-content){
		position: relative; display: block;
		ul{
			list-style: none; padding: 0; margin: 0; line-height: 1.4;
			li{
				position: relative; padding: 0 0 8px 16px;
				&:last-child{padding-bottom: 0;}
				&:before{.pseudo(5px, 5px); border: 2px solid var(--colorBaseBlue); top: 6px; left: 0; border-radius: 100%;}
			}
		}
		@media (max-width: @m){
			font-size: 13px; padding: 0 58px; max-height: 0; overflow: hidden;
			li:before{top: 5px;}
		}
	}
	.cards{
		position: relative; display: flex; align-items: center; justify-content: space-between; width: 352px; height: auto; margin-top: 30px; padding: 16px 24px; background: var(--colorLightBlueBackground); border-radius: var(--inputBorderRadius);
		:deep(p){display: flex; align-items: center; padding: 0; width: 100%;}
		:deep(a){
			display: flex; width: 48px; height: 32px; align-items: center; justify-content: center; flex-grow: 0; flex-shrink: 0; background: var(--colorWhite); border: 1px solid #E6E8EC; border-radius: var(--inputBorderRadius); margin-right: 8px;
			img{display: block; width: auto; height: auto; max-width: 33px; max-height: 20px;}
			&:last-child{
				margin-right: 0; background: none; border: none; width: auto; height: auto;
				img{max-width: 78px; max-height: 30px;}
			}
		}
		@media (max-width: @t){
			margin-top: 20px; width: 272px; padding: 12px 21px;
			:deep(a){
				width: 36px; height: 24px; margin-right: 6px;
				img{max-width: 24px; max-height: 14px;}
				&:last-child img{max-width: 59px; max-height: 23px;}
			}
		}
		@media (max-width: @m){
			display: flex; margin-top: 0; width: 100%; border-radius: 0; padding: 13px 16px; justify-content: center; gap: 8px;
			:deep(p){justify-content: center; gap: 8px;}
			:deep(a){
				width: 45px; height: 30px; margin-right: 0;
				img{max-width: 32px; max-height: 20px;}
				&:last-child{
					margin-left: calc(~"100% - 290px");
					img{max-width: 78px; max-height: 30px;}
				}
			}
		}
	}
	.footer-row2{
		display: flex; align-items: center; justify-content: space-between; margin-top: 95px; padding-left: 48px; position: relative;
		@media (max-width: @t){margin-top: 60px; padding-left: 45px; padding-right: 82px;}
		@media (max-width: @m){margin-top: 0; padding-left: 0; padding: 32px 16px; justify-content: flex-start; align-items: flex-start;}
	}
	:deep(.social-links){
		position: relative; display: flex; align-items: center;
		a{
			position: relative; display: block; width: 40px; height: 40px; margin-right: 8px; font-size: 0;
			&:before{.pseudo(40px,40px); background: url('/assets/images/social-icons/facebook.svg') center no-repeat; background-size: cover; top: 0; left: 0; opacity: 1; .transition(opacity);}
			@media (min-width: @h){
				&:hover{
					&:before{opacity: 0.5;}
				}
			}

		}
		a[title~="instagram"]{
			&:before{background: url('/assets/images/social-icons/instagram.svg') center no-repeat; background-size: cover;}
		}
		a[title~="youtube"]{
			&:before{background: url('/assets/images/social-icons/youtube.svg') center no-repeat; background-size: cover;}
		}
		a[title~="tiktok"]{
			&:before{background: url('/assets/images/social-icons/tiktok.svg') center no-repeat; background-size: cover;}
		}
		a[title~="linkedin"]{
			&:before{background: url('/assets/images/social-icons/linkedin.svg') center no-repeat; background-size: cover;}
		}

		@media (max-width: @t) and (min-width: @m){
			a{
				width: 32px; height: 32px;
				&:before{width: 32px; height: 32px;}
			}
		}
		@media (max-width: @m){
			margin-bottom: 20px;
		}
	}
	:deep(.copyright){
		position: relative; display: flex; flex-flow: column; font-size: var(--fontSizeLabel); color: var(--colorTextLightGray); align-items: flex-end;
		a{
			color: var(--colorTextLightGray); text-decoration: none; .transition(color);
			@media (min-width: @h){
				&:hover{color: var(--colorWhite);}
			}
		}
		@media (max-width: @t){font-size: 11px;}
		@media (max-width: @m){align-items: flex-start; justify-content: flex-start; font-size: 13px; line-height: 16px;}
	}
	.copy{
		margin-bottom: 5px;
		@media (max-width: @m){margin-bottom: 8px;}
	}
	.icon-dropdown{
		display: none;
		@media (max-width: @m){
			display: block; position: absolute; width: 10px; height: 10px; top: 22px; right: 16px;
			&:before{.pseudo(10px,2px); background: var(--colorBaseBlue); left: 0; top: calc(~"50% - 1px");}
			&:after{.pseudo(2px,10px); background: var(--colorBaseBlue); left: calc(~"50% - 1px"); top: 0; .transition(opacity);}
		}
	}
	.faq-link{
		position: relative; display: flex; align-items: flex-start; justify-content: flex-start; margin-top: 20px;
		:deep(p){padding-bottom: 0; display: flex; align-items: center; justify-content: flex-start;}
		:deep(a){
			display: block; position: relative; font-size: 13px; line-height: 17px; color: var(--colorBaseLighter); font-weight: bold; text-decoration: none; padding-right: 25px;
			&:after{.pseudo(18px,18px); border: 1px solid var(--colorBaseLighter); top: -2px; right: 0; border-radius: 50%; .icon-arrow(); font: 8px/7px var(--fonti); color: var(--colorBaseLighter); display: flex; align-items: center; justify-content: center; .rotate(90deg);}
		}
	}
</style>
