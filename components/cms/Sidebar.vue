<template>
	<div class="sidebar">
		<div class="sidebar-inner">
			<BaseCmsNav code="sidebar" v-slot="{items}">
				<ul class="list sidebar-nav">
					<li v-for="item in items" :key="item.id">
						<NuxtLink :to="item.url">
							{{ item.title }}
						</NuxtLink>
					</li>
				</ul>
			</BaseCmsNav>

			<ClientOnly>
				<BaseCmsLabel class="support-info" code="support_info" tag="div" />
			</ClientOnly>
		</div>
	</div>
</template>

<style lang="less" scoped>
	.sidebar {
		position: relative; max-width: 320px; padding: 56px 80px 80px 0; flex: 1 1 auto;
		&:before {.pseudo(auto,auto); top: 0; left: -50vw; right: 0; bottom: 0; background: #f4f6fa;}
		@media (max-width: @t){display: none;}
	}
	.sidebar-inner {position: relative;}
	.sidebar-nav {
	    background: var(--colorBaseBlue); font-size: var(--fontSizeSmall); border-radius: var(--inputBorderRadius); margin: 0; padding: 32px 32px 20px; box-shadow: 0 10px 40px 0 rgba(0, 89, 194, 0.08);
		li {
	        padding-bottom: 12px;
			&:before {border-color: #fff; top: 6px;}
		}
		a {
			text-decoration-color: transparent; text-underline-offset: 2px; display: block; color: #fff;
			&:hover {text-decoration-color: #fff;}
		}
		.router-link-active {opacity: 0.3; pointer-events: none;}
	}
	.support-info {
		position: relative; margin-top: 40px; padding: 0 0 0 48px; font-size: 14px;
		&:before { .pseudo(34px,34px); background: url(assets/images/custom-icons/conversation.svg) no-repeat center center; background-size: contain; left: 0; top: -5px;}
		:deep(.support-info-title) { font-size: 20px; line-height: 1.4; font-weight: bold;}
		:deep(.support-info-desc) {
			display: block; padding-top: 15px;
			&:first-of-type{padding-top: 7px;}
		}
		:deep(a) {
			font-weight: bold; text-decoration: none; display: block; color: var(--colorBase);
			&.support-info-mail{text-decoration: underline; text-decoration-color: var(--colorBaseBlue); text-underline-offset: 3px; padding-bottom: 5px;}
		}
		:deep(strong) { font-weight: bold; display: block; }
	}
</style>
