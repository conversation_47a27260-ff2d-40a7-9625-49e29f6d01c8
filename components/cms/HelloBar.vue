<template>
	<BaseCmsRotator @loadRotatorItems="onLoad" :fetch="{code: 'hello_bar', limit: 1, response_fields: ['id','link','url_without_domain','title','content','image_upload_path','image_thumbs','image_2_upload_path','image_2_thumbs','datetime_counter']}" v-slot="{items}">
		<div v-if="items?.length && !isHidden" class="hello-bar">
			<div class="hello-bar-wrapper wrapper" v-for="item in items" :key="item.id">
				<div class="hello-bar-content" :class="{'no-image': !item.image_upload_path && !item.image_2_upload_path}">
					<div class="hello-bar-image" v-if="item.image_upload_path">
						<BaseUiImage :data="item.image_thumbs?.['width160-height64-crop1']" default="/images/no-image-160.jpg" loading="lazy" :picture="[{maxWidth: '980px', src: item.image_2_thumbs?.['width980-height153-crop1']?.thumb, default: '/images/no-image-980x153.jpg'}]" />
					</div>
					<div class="hello-bar-desc" v-html="item.content" v-interpolation v-if="item?.content"></div>
				</div>
				<div class="hello-bar-timer-cnt" v-if="item?.datetime_counter">
					<BaseUiCountdown :end="item.datetime_counter" v-slot="{days, hours, minutes, seconds, ended}">
						<div class="hello-bar-timer" v-if="!ended">
							<div v-if="days && days > 1" class="days">
								<span>{{ days }}</span> d
							</div>
							<div class="hour">
								<span>{{ hours }}</span> h
							</div>
							<div class="min">
								<span>{{ minutes }}</span> m
							</div>
							<div class="sec">
								<span>{{ seconds }}</span> s
							</div>
						</div>
					</BaseUiCountdown>
				</div>
				<div class="hello-bar-close" @click="close"></div>
			</div>
		</div>
	</BaseCmsRotator>
</template>

<script setup>
	const isHidden = ref(true);
	let rotatorItemId = 0;

	function onLoad(data) {
		rotatorItemId = data?.items?.[0]?.id;
		const cookie = useCookie('hello_bar_'+rotatorItemId);
		if(!cookie.value) {
			isHidden.value = false;
		}
	}

	function close() {
		const cookie = useCookie('hello_bar_'+rotatorItemId, {
			maxAge: 60 * 60 * 24 * 30 // 1 month
		});
		cookie.value = 1;
		isHidden.value = true;
	}
</script>

<style lang="less" scoped>
	.hello-bar{
		background: var(--colorBase); display: flex; overflow: hidden; position: relative; z-index: 1; height: 64px;
		@media (max-width: @m){height: auto;}
	}
	.hello-bar-wrapper{
		width: 100%; display: flex; align-items: center; min-height: 64px; position: relative;
		@media (max-width: @t){min-height: 45px;}
		@media (max-width: @m){padding: 0; min-height: 0; flex-flow: column; align-items: flex-start; padding-bottom: 10px;}
	}
	.hello-bar-content{
		font-size: 14px; line-height: 1.4; color: var(--colorWhite); display: flex; align-items: center; margin-right: auto;
		:deep(p) {padding-bottom: 0;}
		:deep(a) {
			font-weight: bold; color: var(--colorYellow); text-underline-offset: 3px; .transition();
			&:hover{color: var(--colorYellow); text-decoration-color: transparent;}
		}
		@media (max-width: @t){font-size: 12px; line-height: 1.3;}
		@media (max-width: @m){
			flex-flow: column; width: 100%; align-items: flex-start;
			&.no-image{
				.hello-bar-desc{padding-right: 45px;}
			}
		}
	}
	.hello-bar-desc{
		@media (max-width: @m){padding: 8px 16px 0 16px;}
	}
	.hello-bar-image{
		margin-right: 24px; display: block; position: relative; display: block;
		:deep(img){display: block; width: 100%; height: auto; max-width: 100%; max-height: 64px;}
		@media (max-width: @t){margin-right: 15px;}
		@media (max-width: @m){
			margin-right: 0; width: 100%;
			:deep(img){max-height: 100%;}
			&:after{.pseudo(auto, 1px); background: var(--colorWhite); bottom: 0; right: 0; left: 0;}
		}
	}
	.hello-bar-close{
		display: flex; align-items: center; justify-content: center; width: 32px; height: 32px; background: var(--colorBase); border: 1px solid var(--colorWhite); border-radius: 200px; position: relative; flex-shrink: 0; cursor: pointer; z-index: 1; .transition(opacity);
		&:before{.icon-close(); font: 11px/11px var(--fonti); color: var(--colorWhite); position: absolute;}
		@media (min-width: @h) {
			&:hover {opacity: 0.9;}
		}
		@media (max-width: @t){
			width: 24px; height: 24px;
			&:before{font-size: 8px; line-height: 8px; text-indent: 0;}
		}
		@media (max-width: @m){position: absolute; top: 16px; right: 16px;}
	}

	.hello-bar-timer-cnt{
		flex-shrink: 0; width: auto; position: relative;
		@media (max-width: @m){width: 100%;}
	}
	.hello-bar-timer{
		font-size: 16px; line-height: 1.1; color: var(--colorYellow); display: flex; margin-right: 50px; flex: 0 0 auto; margin-left: auto;
		@media (max-width: @t){margin-right: 10px; font-size: 12px;}
		@media (max-width: @m){padding: 8px 15px 0; margin: 0; width: 100%;}
		span{
			display: inline-block; font-size: 24px; font-weight: bold; text-align: right;
			@media (max-width: @t){font-size: 18px;}
		}
		.sec span{
			width: 36px;
			@media (max-width: @t){width: 26px;}
		}
		&>div:first-child{
			@media (max-width: @t){margin-left: 0;}
		}
		&>div{
			margin-left: 13px;
			@media (max-width: @t){margin-left: 8px;}
		}
	}
</style>
