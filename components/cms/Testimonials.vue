<template>
	<div class="testimonials-widget" ref="testimonialsSlider" v-if="items?.length">
		<div class="wrapper">
			<div class="testimonials-widget-container">
				<div class="testimonials-widget-container">
					<div class="testimonials-widget-header">
						<div class="testimonals-widget-title"><BaseCmsLabel code="testimonals_widget_title" /></div>
						<div class="testimonals-widget-subtitle"><BaseCmsLabel code="testimonals_widget_subtitle" /></div>
					</div>
					<div class="testimonials-widget-items-container">
						<BaseUiSwiper class="testimonials-widget-items" name="testimonials-swiper" :options="sliderOptions">
							<BaseUiSwiperSlide v-for="item in items" :key="item.id" class="testimonials-widget-item">
								<div class="testimonials-widget-item-header">
									<div class="testimonials-widget-item-image" v-if="item.image_upload_path"><BaseUiImage :src="item.image_upload_path" loading="lazy" /></div>
									<div class="testimonials-widget-item-data">
										{{item.title}}
										<span v-if="item.image_2_upload_path"><BaseUiImage :src="item.image_2_upload_path" loading="lazy" /></span>
									</div>
								</div>
								<div class="testimonials-widget-item-content" v-html="item.content" v-interpolation></div>
							</BaseUiSwiperSlide>
						</BaseUiSwiper>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	const props = defineProps(['items']);
	const sliderOptions = {
		slidesPerView: 3,
		slidesPerGroup: 3,
		spaceBetween: 0,
		enabled: false,
		navigation: {
			enabled: true,
			wrapperClass: 'splide__arrows',
			nextElClass: 'splide__arrow splide__arrow--next',
			prevElClass: 'splide__arrow splide__arrow--prev'
		},
		breakpoints: {
			980: {
				spaceBetween: 16,
				enabled: true
			},
			1300: {
				spaceBetween: 24,
				enabled: true,
			},
		}
	}
</script>

<style lang="less" scoped>
	// testimonials widget
	.testimonials-widget{
		position: relative; display: block; padding: 88px 0;
		@media (max-width: @t){padding: 72px 0 64px;}
		@media (max-width: @m){padding: 48px 0 30px;}
	}
	.testimonials-widget-header{
		position: relative; display: block; margin-bottom: 27px;
		@media (max-width: @t){margin-bottom: 20px;}
		@media (max-width: @m){margin-bottom: 23px;}
	}
	.testimonals-widget-title{
		display: block; font-size: var(--fontSizeH2); line-height: 1.2; font-weight: bold; margin-bottom: 9px;
		@media (max-width: @t){font-size: var(--fontSizeH1); margin-bottom: 0;}
		@media (max-width: @m){font-size: 23px; margin-bottom: 3px;}
	}
	.testimonals-widget-subtitle{
		display: block; color: var(--colorTextLightGray); line-height: 1.4;
		:deep(p:last-child){padding-bottom: 0;}
		@media (max-width: @t){color: var(--colorBase); font-size: var(--fontSizeSmall);}
		@media (max-width: @m){color: var(--colorTextLightGray); font-size: 15px;}
	}
	.testimonials-widget-items-container{position: relative; display: block; width: 100%;}
	:deep(.testimonials-swiper-swiper-navigation){
		position: absolute; display: flex; height: 50px; top: 20px; left: 0; right: 0;
		.swiper-button-prev, .swiper-button-next{
			z-index: 1; width: 56px; height: 56px; background: var(--colorLightBlueBackground); border-radius: 50%; top: 0; cursor: pointer; transition: background 0.3s;
			&:before{font-size: 20px; color: var(--colorBase); transition: color 0.3s;}
			&.swiper-button-disabled{opacity: 0.4; pointer-events: none;}
			@media (min-width: @h){
				&:hover{
					background: var(--colorBaseBlue);
					&:before{color: var(--colorWhite);}
				}
			}
		}
		.swiper-button-prev{left: -80px;}
		.swiper-button-next{right: -80px;}
		@media (max-width: 1500px){
			top: 24px;
			.swiper-button-prev, .swiper-button-next{
				width: 48px; height: 48px;
				&:before{font-size: 16px;}
			}
			.swiper-button-prev{left: 16px;}
			.swiper-button-next{right: 16px;}
		}
		@media (max-width: @t){
			height: 32px; top: 31px;
			.swiper-button-prev, .swiper-button-next{
				width: 32px; height: 32px; background: var(--colorBaseBlue);
				&:before{font-size: 12px; color: var(--colorWhite);}
			}
			.swiper-button-prev{left: -35px;}
			.swiper-button-next{right: -35px;}
		}
		@media (max-width: @m){display: none!important;}
	}
	:deep(.testimonials-widget-items){
		display: flex;
		@media (max-width: @m){
			width: calc(~"100% - -32px"); margin-left: -16px;
			.swiper-wrapper{
				display: flex!important; overflow-x: auto; padding: 0 16px;
				&::-webkit-scrollbar{-webkit-appearance: none; height: 0; background: transparent; z-index: 0;}
				&::-webkit-scrollbar-thumb{background-color: transparent;}
			}
		}
	}
	.testimonials-widget-item{
		@media (max-width: @m){
			width: calc(~"100% - 30px")!important; margin-right: 24px;
			&:last-child{margin-right: 0;}
		}
	}
	.testimonials-widget-item-header{
		position: relative; display: flex; align-items: center; background: var(--colorLightBlueBackground); border-radius: var(--inputBorderRadius); padding: 16px; min-height: 96px;
		@media (max-width: @m){padding: 8px; min-height: 80px;}
	}
	.testimonials-widget-item-image{
		position: relative; display: block; width: 64px; height: 64px; border-radius: var(--inputBorderRadius); overflow: hidden; margin-right: 16px;
		img{display: block; width: auto; height: auto; max-width: 100%;}
		@media (max-width: @t){width: 56px; height: 56px; margin-right: 13px;}
		@media (max-width: @m){width: 64px; height: 64px; margin-right: 16px; box-shadow: 0 5px 20px rgba(0,89,194,0.15);}
	}
	.testimonials-widget-item-data{
		display: flex; flex-flow: column; font-size: var(--fontSizeSmall); font-weight: bold;
		span{display: block; margin-top: 4px; line-height: 0;}
		@media (max-width: @t){
			font-size: 13px;
			span img{max-height: 16px;}
		}
		@media (max-width: @m){
			span img{max-height: 20px;}
		}
	}
	.testimonials-widget-item-content{
		display: block; font-size: var(--fontSizeSmall); padding: 16px 16px 0;
		:deep(p:last-child){padding-bottom: 0;}
		@media (max-width: @t){font-size: var(--fontSizeLabel);}
		@media (max-width: @m){padding: 16px 8px 0; font-size: 13px;}
	}
</style>
