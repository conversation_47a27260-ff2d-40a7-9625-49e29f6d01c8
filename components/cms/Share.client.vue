<template>
	<BaseCmsShare :networks="['facebook', 'whatsapp', 'viber', 'link']" v-slot="{items, onShare}">
		<div class="share" v-if="items.length">
			<p class="share-title"><BaseCmsLabel code="share_with_friends" /></p>
			<div class="share-items">
				<div v-for="network in items" :key="network" :class="['ss-item', 'ss-' + network.code]" :title="network.title" @click="onShare(network), showTooltip(network)">
					<span class="share-tooltip" v-if="network.code == 'link' && tooltip">Link stranice je kopiran</span>
				</div>
			</div>
		</div>
	</BaseCmsShare>
</template>

<script setup>
	const tooltip = ref(false);
	let timer;
	function showTooltip(network) {
		clearTimeout(timer);

		if (network.code == 'link') {
			tooltip.value = true;
			timer = setTimeout(() => {
				tooltip.value = false;
			}, 1000);
		}
	}

	onBeforeUnmount(() => {
		clearTimeout(timer);
	});
</script>

<style scoped lang="less">
	.share{
		padding: 20px 0 0;
		@media (max-width: @m){padding: 15px 0 0;}
	}
	.share-tooltip{
		position: absolute; background: #fff; border-radius: 4px; font-size: 11px; line-height: 1; top: 120%; right: 20%; white-space: nowrap; padding: 5px 8px; box-shadow: 0px -1px 6px rgba(0,0,0,.2);
		&:before{.pseudo(10px,10px); background: #fff; .rotate(45deg); top: -2px; right: 10px;}
	}
	.share-items{display: flex;}
	.share-title{
		padding: 0 0 8px; font-weight: bold; color: var(--colorTextLightGray); font-size: 14px; line-height: 1.4;
		@media (max-width: @m){padding: 0 0 6px; font-size: 12px;}
	}
	.ss-item{
		display: flex; background: #1877F2; border-color: #1877F2; align-items: center; justify-content: center; width: 48px; height: 48px; margin: 0 8px 0 0; border-radius: 4px; cursor: pointer; transition: background 0.3s, border-color 0.3s; position: relative;
		@media (min-width: @h){
			&:hover:before{.scale(1.13);}
		}
		@media (max-width: @m){width: 40px; height: 40px;}
		&:before{
			.icon-fb(); font: 19px/19px var(--fonti); color: #fff; .transition(transform);
			@media (max-width: @m){font-size: 16px; line-height: 16px;}
		}
	}
	.ss-whatsapp{
		background: #25D366; border-color: #25D366;
		&:before{
			.icon-whatsapp(); font-size: 21px; line-height: 21px;
			@media (max-width: @m){font-size: 18px; line-height: 18px;}
		}
	}
	.ss-viber{
		background: #7360f2; border-color: #7360f2;
		&:before{
			.icon-viber(); font-size: 20px; line-height: 20px;
			@media (max-width: @m){font-size: 18px; line-height: 18px;}
		}
	}
	.ss-email{
		&:before{
			.icon-mail(); font-size: 22px; line-height: 22px;
			@media (max-width: @m){font-size: 18px; line-height: 18px;}
		}
	}
	.ss-link{
		background: var(--colorBaseBlue); border: 1px solid var(--colorBaseBlue);
		&:before{
			.icon-link(); font-size: 20px; line-height: 20px;
			@media (max-width: @m){font-size: 16px; line-height: 16px;}
		}
	}
</style>
