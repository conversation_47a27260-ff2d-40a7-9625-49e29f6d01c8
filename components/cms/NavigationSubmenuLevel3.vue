<template>
	<div class="nav-submenu-container-l3" v-if="item.items?.length">
		<div v-for="(item, index) in item.items" :key="item.id" class="nav-submenu-col" :class="['nav-submenu-col-'+index, item.style ? `nav-submenu-col-${item.style}` : '']">
			<div class="nav-submenu-title">{{item.title}}</div>
			<BaseCmsLabel class="nav-submenu-subtitle" code="nav_submenu_subtitle" tag="div" v-if="item.style == 'brands'" />
			<ul class="nav-submenu-items" v-if="item.items?.length" :class="{'two-columns' : item.items.length > 12}">
				<li v-for="item in item.items" :key="item.id" :class="[item.style, item.code]">
					<BaseUiLink :href="item.url_without_domain" :class="{'has-image': item.image_upload_path}" @click="closeMobileMenu(), handleMouseLeave()">
						<figure class="nav-submenu-image" v-if="item.image_upload_path">
							<BaseUiImage v-if="item.style.includes('brand')" :src="item.image_upload_path" default="/images/no-image-112.png" width="50" height="50" loading="lazy" :alt="item.title" />
							<BaseUiImage v-else :data="item.image_thumbs?.['width60-height60']" default="/images/no-image-80.jpg" width="60" height="60" loading="lazy" :alt="item.title" />
						</figure>
						<span>{{item.title}}</span>
					</BaseUiLink>
				</li>
			</ul>
			<div class="nav-submenu-button" v-if="item.style == 'brands'">
				<NuxtLink class="btn" :href="getAppUrl('manufacturer')"><BaseCmsLabel code="manufacturers_widget_button" /></NuxtLink>
			</div>
		</div>

		<ClientOnly>
			<LazyBaseCatalogProductsWidget v-if="item.code && props.submenuOpen && !mobileBreakpoint" :fetch="{list_code: ['menu_bestsellers_'+item.code], limit:4, only_with_image: true, only_available: true}" v-slot="{items: menuProducts}" @loadProductsWidget="props.onProductsLoaded">
				<div class="nav-submenu-col nav-submenu-col-bestsellers" v-if="menuProducts?.length">
					<BaseCmsLabel class="nav-submenu-title" code="bestsellers" tag="div" />
					<template v-for="item in menuProducts" :key="item.id">
						<CatalogIndexEntryMenu :item="item" />
					</template>
				</div>
			</LazyBaseCatalogProductsWidget>
		</ClientOnly>
	</div>
</template>

<script setup>
	const {getAppUrl} = useApiRoutes();
	const {onMediaQuery} = useDom();
	const props = defineProps({
		submenuOpen: Boolean,
		item: Object,
		onProductsLoaded: Function,
		handleMouseLeave: Function,
	})

	const {matches: mobileBreakpoint} = onMediaQuery({
		query: '(max-width: 980px)',
	});

	function closeMobileMenu() {
		if(mobileBreakpoint?.value) mobileMenu();
	}

	const {mobileMenu} = inject('header');
</script>
