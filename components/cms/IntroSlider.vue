<template>
	<div class="hi-rotator" :class="{single : items.length == 1}">
		<BaseUiSwiper class="hi-items" :options="sliderOptions">
			<BaseUiSwiperSlide v-for="(item, index) in items" :key="item.id" class="hi-item">
				<component :is="item.link ? NuxtLink : 'div'" :to="item.link ? item.url_without_domain : undefined" :class="['hi-item', {link: item.link}]">
					<div class="hi-item-image" v-if="item.image_upload_path">
						<BaseUiImage
							:data="item.image_thumbs?.['width1920-height624-crop1']"
							default="/images/no-image-1920.jpg"
							:loading="index === 0 ? 'eager' : 'lazy'"
							:picture="[{maxWidth: '980px', src: item.image_2_thumbs?.['width980-height1089-crop1']?.thumb ? item.image_2_thumbs['width980-height1089-crop1'].thumb : item.image_thumbs?.['width1920-height624-crop1']?.thumb, default: '/images/no-image-980.jpg'}]" />
					</div>
					<div class="hi-item-content" v-if="item.title2 || item.title || item.content">
						<div class="hi-item-headline" v-if="item.title2">{{item.title2}}</div>
						<div class="hi-item-title" v-if="item.title">{{item.title}}</div>
						<div class="hi-item-cnt" v-if="item.content">
							<div class="btn">{{item.content}}</div>
						</div>
					</div>
				</component>
			</BaseUiSwiperSlide>
		</BaseUiSwiper>
	</div>
</template>

<script setup>
	import { NuxtLink } from '#components';
	const props = defineProps(['items']);
	const sliderOptions = {
		slidesPerView: 1,
		slidesPerGroup: 1,
		speed: 500,
		effect: 'fade',
		fadeEffect: {
			crossFade: true
		},
		loop: true,
		autoplay: {
			delay: 5000,
			disableOnInteraction: true
		},
		pagination: {
			enabled: true,
			el: '.progress-bars',
			clickable: true,
			bulletClass: 'splide__slide progress-bar',
			wrapperClass: 'progress-bars',
			bulletElement: 'div',
			bulletActiveClass: 'is-active progress-bar-active'
		},
		navigation: {
			enabled: true,
			wrapperClass: 'splide__arrows',
			nextElClass: 'splide__arrow splide__arrow--next',
			prevElClass: 'splide__arrow splide__arrow--prev'
		},
	}
</script>

<style lang="less" scoped>
	.hi-rotator{
		position: relative; display: block; width: 100%; height: auto;
		@media (max-width: @ms){min-height: 457px; overflow: hidden;}
		@media (max-width: 415px){
			min-height: none;
			:deep(.swiper){
				width: 100%;
				.swiper-slide{
					height: 457px;
					@media (max-width: 400px){height: auto;}
				}
			}
		}
		@media (max-width: 400px){min-height: 200px;}
		:deep(.splide__arrows){display: block!important;}
		&.single{
			:deep(.splide__arrows), :deep(.swiper-pagination){display: none!important;}
		}
		@media (max-width: @l){
			:deep(.splide__arrows){display: none!important;}
		}
	}
	.hi-items{
		position: relative; display: flex; justify-content: center;
		.swiper:not(.swiper-initialized) .swiper-slide:not(:first-child){overflow: hidden; height: 0; visibility: hidden;}
	}
	.hi-item{
		position: relative; display: flex; justify-content: center; height: auto; width: 100%;
		//&:before{.pseudo(100%,100%); top: 0; left: 0; background: linear-gradient(180deg, rgba(0,0,0,0) 0%, rgba(0,0,0,0.55) 98.44%); z-index: 1;}
		@media (max-width: @m){background: linear-gradient(180deg, rgba(0,0,0,0) 0%, rgba(0,0,0,0.55) 100%);}
	}
	.hi-item-image{
		position: relative; display: block; width: 100%;
		@media (max-width: 1355px){height: 435px;}
		@media (max-width: @m){height: auto;}
		@media (min-width: 2500px){height: 740px;}
		:deep(picture){display: block; width: 100%; height: 100%;}
		:deep(img){display: block; width: 100%; height: 100%; max-width: 100%; object-fit: cover;}
	}
	.hi-item-content{
		position: absolute; top: 0; bottom: 0; display: flex; flex-flow: column; justify-content: center; align-items: flex-start; padding: 100px 0; width: var(--pageWidth); z-index: 2;
		@media (max-width: @t){width: 100%; padding: 40px;}
		@media (max-width: @m){padding: 68px 24px; justify-content: flex-end;}
	}
	.hi-item-headline{
		display: block; font-size: var(--fontSizeSpecial); line-height: 28px; color: var(--colorWhite); font-weight: bold; text-shadow: 1px 2px 0 rgba(0,12,26,0.2); max-width: 450px; padding-bottom: 6px;
		@media (max-width: @t){font-size: 16px; line-height: 24px;}
		@media (max-width: @m){font-size: 15px; line-height: 19px; max-width: 180px;}
	}
	.hi-item-title{
		display: block; font-size: var(--fontSizeDisplayBig); line-height: 63px; color: var(--colorWhite); font-weight: bold; text-shadow: 1px 2px 0 rgba(0,12,26,0.2); max-width: 450px;
		@media (max-width: @t){font-size: 46px; line-height: 52px;}
		@media (max-width: @m){font-size: 29px; line-height: 33px; max-width: 235px;}
	}
	.hi-item-cnt{
		display: block; color: var(--colorWhite); margin-top: 20px; max-width: 450px;
		.btn{box-shadow: 0 10px 20px rgba(0,12,26,0.2);}
		@media (max-width: @t){margin-top: 15px;}
		@media (max-width: @m){margin-top: 18px; max-width: 235px;}
	}
</style>
