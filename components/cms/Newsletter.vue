<template>
	<div class="newsletter">
		<div class="wrapper nw-wrapper">
			<div class="nw-col nw-col1" v-html="labels.get('newsletter_widget_content')"></div>
			<div class="nw-col nw-col2">
				<BaseNewsletterSignupForm class="nw-form" v-slot="{fields, gdprFields, status}">
					<template v-if="!status?.success">
						<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage}">
							<template v-if="item.type != 'hidden'">
								<BaseFormInput :id="`newsletter-${item.name}`" class="nw-input" :placeholder="labels.get('enter_email')" />
								<span class="error" v-show="errorMessage" v-html="errorMessage" />
							</template>
							<BaseFormInput v-else />
						</BaseFormField>
						<button class="btn nw-button" type="submit"><BaseCmsLabel code="newsletter_signup" /></button>
						<div class="nw-gdpr-checkbox" v-if="gdprFields">
							<BaseFormField v-for="field in gdprFields" :key="field.name" :item="field">
								<BaseFormInput v-if="field.type == 'hidden'" />
								<p class="field" v-else v-interpolation>
									<BaseFormInput :id="`nl-${field.name}`" />
									<label :for="`nl-${field.name}`" v-html="labels.get('newsletter_gpdr_link')"></label>
								</p>
							</BaseFormField>
						</div>
					</template>
					<BaseCmsLabel v-show="status?.success" tag="div" class="nw-success" code="success_subscribe" />
				</BaseNewsletterSignupForm>
			</div>
		</div>
	</div>
</template>

<script setup>
	const labels = useLabels();
</script>

<style lang="less" scoped>
	.newsletter {
		position: relative; display: block; width: 100%; background: var(--colorBase); padding: 93px 0 91px;
		@media (max-width: @t){padding: 50px 0 48px;}
		@media (max-width: @m){padding: 40px 0 45px;}
	}
	.nw-wrapper {
		display: flex; align-items: flex-start; justify-content: space-between;
		@media (max-width: @m){flex-flow: column; justify-content: flex-start;}
	}
	.nw-col1 {
		position: relative; display: block; width: 650px; padding-left: 96px; color: #999ea3; // dodati u vars
		&:before{.pseudo(80px,80px); background: url('/assets/images/custom-icons/newsletter.svg') center no-repeat; background-size: cover; top: 0; left: 0;}
		@media (max-width: @t) {
			padding-left: 0; width: 430px; flex-grow: 0; flex-shrink: 0; font-size: var(--fontSizeLabel);
			;
			&:before{display: none;}
		}
		@media (max-width: @m){max-width: 550px; width: auto;}
	}
	:deep(.nw-col1) {
		p:first-child {
			color: var(--colorWhite); padding: 0 0 7px; font-size: 31px; line-height: 1.3; letter-spacing: 0; font-weight: bold;
			strong{background: var(--colorYellow); color: var(--colorBase); border-radius: var(--inputBorderRadius); padding: 0 6px; margin-left: -6px;}
			@media (max-width: @t) {
				font-size: 22px; line-height: 26px; position: relative; padding-left: 71px;
				&:before{.pseudo(65px,65px); background: url('/assets/images/custom-icons/newsletter.svg') center no-repeat; background-size: cover; top: -18px; left: 0;}
				strong{padding: 0 5px; margin-left: -5px;}
			}
			@media (max-width: @m) {
				font-size: 23px; line-height: 28px; position: relative; padding-left: 0; padding-bottom: 15px; padding-top: 68px;
				&:before{width: 56px; height: 56px; background-size: cover; top: 0; left: 0;}
				strong{padding: 0 5px; margin-left: 0;}
			}
		}
		p:last-of-type{padding-bottom: 0;}
	}
	.nw-col2 {
		position: relative; display: block; width: 528px;
		@media (max-width: @t){width: 100%; flex-grow: 1; flex-shrink: 1; margin-left: 198px;}
		@media (max-width: @m){width: 100%; margin-top: 18px; margin-left: 0;}
	}
	.nw-input{
		position: relative; display: block;
		@media (max-width: @m){font-size: 15px;}
	}
	:deep(.nw-input input){border: none; box-shadow: 0 5px 20px rgba(0, 12, 26, 0.05);}
	:deep(.nw-input .error){position: absolute; top: calc(~'50% - -8px'); left: 0; color: var(--colorWhite); padding-top: 0;}
	.nw-button{
		position: absolute; width: 102px; height: 40px; top: 8px; right: 8px; display: flex; align-items: center; justify-content: center; padding: 0;
		@media (max-width: @m){width: 87px;}
	}
	.nw-note{display: none;}
	.nw-gdpr-checkbox {
		position: relative; display: block; margin-top: 18px;
		label {
			display: block; color: var(--colorWhite); font-size: 12px; line-height: 15px;
			&:before{width: 20px; height: 20px; top: -3px;}
		}
		p{padding-bottom: 0;}
		:deep(a){
			color: var(--colorBaseLighter); font-weight: bold; text-decoration: underline; text-underline-offset: 3px; transition: text-decoration-color 0.3s;
			&:hover {text-decoration-color: transparent;}
		}
		@media (max-width: @t) {
			label{
				font-size: 11px;
				&:before{width: 18px; height: 18px; top: -2px;}
			}
		}
	}
	.nw-success{color: var(--colorWhite);}
</style>
