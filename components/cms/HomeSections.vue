<template>
	<BaseCatalogLists :fetch="{code: 'new_biciklizam', limit: 1, response_fields: ['code','title','content']}" v-slot="{items: list}">
		<template v-if="list?.length">
			<BaseCatalogProductsWidget :fetch="{list_code: list[0].code, sort: 'list_position', limit: 15, category_code: categoryItem[0].code, only_with_image: true, only_available: true}" :gtm-tracking="{item_list_id: list[0].id, item_list_name: list[0].title}" v-slot="{items}">
				<CatalogSpeciallists class="homepage-speciallist homepage-speciallist1" :items="items" list="new">
					<template #header>
						<div class="speciallist-header">
							<div v-if="list[0].title" class="speciallist-title">{{ list[0].title }}</div>
							<div v-if="list[0].content" class="speciallist-cnt" v-html="list[0].content" v-interpolation></div>
						</div>
					</template>
				</CatalogSpeciallists>
			</BaseCatalogProductsWidget>
		</template>
	</BaseCatalogLists>

	<BaseCmsRotator
		:fetch="{code: 'c_lvl_one_promo_second', catalogcategory_id: categoryItem[0].id, limit: 2, response_fields: ['id','template','link','url_without_domain','title2','title','content','image_upload_path','image_2_upload_path','image_thumbs','image_2_thumbs', 'catalogcategory_ids']}"
		v-slot="{items}">
		<CmsPromo class="homepage-promo-widget homepage-promo-widget2" :items="items" v-if="items?.length" />
	</BaseCmsRotator>

	<BaseCatalogLists :fetch="{code: 'sale_biciklizam', limit: 1, response_fields: ['code','title','content']}" v-slot="{items: list}">
		<template v-if="list?.length">
			<BaseCatalogProductsWidget :fetch="{list_code: list[0].code, sort: 'list_position', limit: 15, category_code: categoryItem[0].code, only_with_image: true, only_available: true}" :gtm-tracking="{item_list_id: list[0].id, item_list_name: list[0].title}" v-slot="{items}">
				<CatalogSpeciallists class="homepage-speciallist homepage-speciallist2" :items="items" list="sale" v-if="items.length">
					<template #header>
						<div class="speciallist-header">
							<div v-if="list[0].title" class="speciallist-title">{{ list[0].title }}</div>
							<div v-if="list[0].content" class="speciallist-cnt" v-html="list[0].content" v-interpolation></div>
						</div>
					</template>
				</CatalogSpeciallists>
			</BaseCatalogProductsWidget>
		</template>
	</BaseCatalogLists>

	<BaseCmsRotator
		:fetch="{code: 'c_lvl_one_promo_third', catalogcategory_id: categoryItem[0].id, limit: 1, response_fields: ['id','template','link','url_without_domain','title2','title','content','image_upload_path','image_2_upload_path','image_thumbs','image_2_thumbs', 'catalogcategory_ids']}"
		v-slot="{items}">
		<CmsPromo class="homepage-promo-widget homepage-promo-widget3" :items="items" v-if="items?.length" />
	</BaseCmsRotator>

	<BaseCatalogLists :fetch="{code: 'bestsellers_biciklizam', limit: 1, response_fields: ['code','title','content']}" v-slot="{items: list}">
		<template v-if="list?.length">
			<BaseCatalogProductsWidget :fetch="{list_code: list[0].code, sort: 'list_position', limit: 15, only_with_image: true, only_available: true}" :gtm-tracking="{item_list_id: list[0].id, item_list_name: list[0].title}" v-slot="{items}">
				<CatalogSpeciallists class="homepage-speciallist homepage-speciallist3" :items="items" list="bestsellers" v-if="items.length">
					<template #header>
						<div class="speciallist-header">
							<div v-if="list[0].title" class="speciallist-title">{{ list[0].title }}</div>
							<div v-if="list[0].content" class="speciallist-cnt" v-html="list[0].content" v-interpolation></div>
						</div>
					</template>
				</CatalogSpeciallists>
			</BaseCatalogProductsWidget>
		</template>
	</BaseCatalogLists>

	<BaseCatalogManufacturers :fetch="{id:categoryItem[0].related_manufacturers_ids, special: 1, limit: 16, sort: 'position_h', response_fields: ['id','url_without_domain','title','main_image_upload_path']}" v-slot="{items}" v-if="categoryItem?.length">
		<CatalogManufacturers class="homepage-manufacturers-widget" :items="items" v-if="items?.length">
			<template #header>
				<div class="manufacturers-widget-header">
					<BaseCmsLabel code="manufacturers_widget_title" class="manufacturers-widget-title" tag="div" v-interpolation />
					<BaseCmsLabel code="manufacturers_widget_subtitle" class="manufacturers-widget-subtitle" tag="div" v-interpolation />
				</div>
			</template>
			<template #footer>
				<div class="manufacturers-widget-items-btn">
					<NuxtLink :href="getAppUrl('manufacturer')" class="btn"><BaseCmsLabel code="manufacturers_widget_button" /></NuxtLink>
				</div>
			</template>
		</CatalogManufacturers>
	</BaseCatalogManufacturers>

	<BaseCmsRotator :fetch="{code: 'service_widget', limit: 1, catalogcategory_id: categoryItem[0].id, response_fields: ['id','link','image_upload_path','image_2_upload_path','image_thumbs','image_2_thumbs','title','title2','content','catalogcategory_ids']}" v-slot="{items}">
		<CmsService :items="items" v-if="items?.length" />
	</BaseCmsRotator>
</template>

<script setup>
	const props = defineProps(['categoryItem']);
	const {getAppUrl} = useApiRoutes();
</script>
