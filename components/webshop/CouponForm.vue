<template>
	<BaseWebshopCouponForm v-slot="{onSubmit, onRemove, couponCode, message, error, loading, activeCoupon, handleInput}" :mode="props.mode">
		<div class="base-coupon" :class="[{'base-coupon-checkout': mode == 'checkout'},{'base-coupon-auth': mode == 'auth'},{'base-coupon-cart': mode == 'webshop'},{'base-coupon-error': error}, {'base-coupon-active': activeCoupon}, {'loading': loading}]">
			<form v-if="mode == 'webshop' || mode == 'checkout'" class="base-coupons-form" @submit.prevent="couponCode && !activeCoupon && onSubmit()">
				<input class="base-coupons-input" :readonly="activeCoupon?.code" :placeholder="activeCoupon?.code ? activeCoupon.code : labels.get('coupon_code')" type="text" :value="couponCode" name="coupon" @keyup="handleInput" />
				<button v-if="!activeCoupon" class="base-coupons-btn base-coupons-btn-add" type="submit"><BaseCmsLabel code="coupon_add" /></button>
				<button v-if="activeCoupon" class="base-coupons-btn base-coupons-btn-remove" type="button" @click="onRemove"><BaseCmsLabel code="coupon_remove" /></button>
				<BaseCmsLabel v-if="message" class="base-coupons-status" tag="div" :code="message" />
				<BaseAuthCoupons v-if="auth.isLoggedIn()" :only-active="true" v-slot="{items, onActivate, status}">
					<div v-if="!activeCoupon && items?.length" class="ww-coupons-table-cnt">
						<h4 class="ww-coupons-table-title"><BaseCmsLabel code="coupon_available" /></h4>
						<table class="ww-coupons-table">
							<tr v-for="item in items" :key="item.id">
								<td class="col-code">{{item.code}}</td>
								<td class="col-type">
									<span v-if="item.type == 'f'"><BaseUtilsFormatCurrency :price="item.coupon_price" /></span>
									<span v-else>-{{ item.coupon_percent * 100 }}%</span>
								</td>
								<td class="col-link">
									<span class="btn-coupon-add" @click="onActivate(item.code)"><BaseCmsLabel code="coupon_use" /></span>
								</td>
							</tr>
						</table>
					</div>
					<BaseCmsLabel v-if="status" class="base-coupons-status" tag="div" :code="status.label_name" />
				</BaseAuthCoupons>
			</form>
			<form v-else class="base-coupons-form" @submit.prevent="onSubmit()">
				<input class="base-coupons-input" :placeholder="labels.get('coupon_code')" type="text" name="coupon" :value="couponCode" @keyup="handleInput" />
				<button class="base-coupons-btn base-coupons-btn-add" type="submit"><BaseCmsLabel code="coupon_add" /></button>
				<BaseCmsLabel v-if="message" class="base-coupons-status" tag="div" :code="message" />
			</form>
		</div>
	</BaseWebshopCouponForm>
</template>

<script setup>
	const props = defineProps(['mode']);
	const labels = useLabels();
	const auth = useAuth();
</script>

<style lang="less" scoped>
	.base-coupon{position: relative; margin-bottom: 30px;}
	.base-coupons-input{
		padding: 0 96px 0 20px;
		@media (max-width: @m){box-shadow: 0 5px 20px 0 rgba(0,12,26,0.05); border-color: transparent; font-size: 15px; padding-left: 16px;}
	}
	.base-coupons-btn-add, .base-coupons-btn-remove{
		background: #fff; color: var(--colorBaseBlue); border: 1px solid var(--colorBaseBlue); box-shadow: none; padding: 0 18px; height: 40px; font-size: 15px; position: absolute; top: 8px; right: 8px;
		&.disabled{background: #fff!important; color: var(--colorBaseBlue);}
	}

	//status
	.base-coupons-status{position: absolute; padding-left: 20px; font-size: 11px; line-height: 1.3; padding-top: 0px;}

	//checkout
	.base-coupon-checkout{
		margin-bottom: 40px; box-shadow: 0 0 40px 0 rgba(0,89,194,0.1); background: #fff;
		.ww-coupons-table-cnt{padding: 0 15px 10px; border-top: 1px solid var(--colorBorderLightForms);}
		.base-coupons-input{border-color: transparent !important;}
		@media(max-width: @m){margin-bottom: 10px;}
	}

	//auth
	.base-coupon-auth{
		margin-bottom: 0;
		.base-coupons-form{
			width: 415px; flex-shrink: 0; align-items: center; position: relative; box-shadow: 0 0 40px 0 rgba(0,89,194,0.1);
			input{border: unset; padding-right: 100px;}
			@media(max-width: @t){width: auto; flex-grow: 1;}
			@media(max-width: @m){
				box-shadow: 0 5px 20px 0 rgba(0,12,26,0.13);
				input{font-size: 15px; line-height: 21px; padding-left: 16px;}
			}
		}
		.auth-coupons-add{
			position: relative; box-shadow: 0 0 40px 0 rgba(0,89,194,0.1);
			input{border: unset; padding-right: 100px;}
			@media(max-width: @m){
				box-shadow: 0 5px 20px 0 rgba(0,12,26,0.13);
				input{font-size: 15px; line-height: 21px; padding-left: 16px;}
			}
		}
	}

	.ww-coupons-table-title{
		font-size: 18px; padding: 20px 0 5px;
		@media (max-width: @m){font-size: 16px;}
	}
	.ww-coupons-table{width: 100%; font-size: 14px;}
	td{padding: 5px 0;}
	.col-type{text-align: center; padding: 5px 10px;}
	.col-link{text-align: right;}
	.btn-coupon-add{
		text-decoration: underline; cursor: pointer; color: var(--colorBaseBlue);
		@media (min-width: @h){
			&:hover{text-decoration: none;}
		}
	}
</style>
