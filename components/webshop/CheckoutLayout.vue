<template>
	<Body class="page-checkout" />
	<div class="checkout-layout">
		<ClientOnly>
			<BaseWebshopCheckout v-slot="{giftCards}">
				<slot name="header" />
				<div class="wc-cols">
					<div class="wc-col1">
						<div class="wc-col1-cnt">
							<WebshopCouponForm mode="checkout" />

							<div class="checkout-cart wc-gift-cards-container" v-if="giftCards?.length" :class="[{'active': activeGiftCards}]">
								<div class="checkout-cart-intro wc-gift-cards-intro" @click="activeGiftCards = !activeGiftCards">
									<span v-show="mobileBreakpoint" class="checkout-cart-intro-dropdown"></span>
									<h4 class="wc-cart-title"><BaseCmsLabel code="gift_checkout_title" /></h4>
								</div>
								<div class="wc-gift-cards">
									<template v-for="value in giftCards" :key="value[0]">
										<template v-for="(giftCard, index) in value[1]" :key="giftCard">
											<BaseCatalogGiftCardForm v-slot="{emailFields, postFields, loading, values, meta, status, product}" class="form-animated-label" :values="giftCard" :number="index" :code="value[0]">
												<div v-if="product" class="wc-totals wc-gift-card">
													<div class="wc-gift-card-title" v-if="product?.item?.title">{{product.item.title}}</div>
													<div class="wc-gift-card-cnt">
														<div class="global-success" v-if="status?.data?.label_name"><BaseCmsLabel :code="status.data.label_name" /></div>
														<div class="gift-send-option">
															<BaseFormField v-for="field in emailFields" :key="field.name" :item="field" v-slot="{errorMessage, floatingLabel}">
																<div class="field" :class="['field-'+field.name, {'ffl-floated': floatingLabel}]" v-if="field.name == 'type'">
																	<BaseFormInput :id="`gift-email-${field.name}-${value[0]}-${index}`" />
																	<label :for="`gift-email-${field.name}-${value[0]}-${index}`">
																		<BaseCmsLabel :code="(field.name == 'type') ? 'gift_type_email' : 'gift_'+field.name" />
																	</label>
																	<span class="error" v-show="errorMessage" v-html="errorMessage" />
																</div>
															</BaseFormField>
															<div class="gift-form" v-if="values.type == 'email'">
																<BaseFormField v-for="field in emailFields" :key="field.name" :item="field" v-slot="{errorMessage, floatingLabel}">
																	<div class="field" :class="['field-'+field.name, {'ffl-floated': floatingLabel}]" v-if="field.name != 'type'">
																		<BaseFormInput :id="`gift-email-${field.name}-${value[0]}-${index}`" />
																		<label :for="`gift-email-${field.name}-${value[0]}-${index}`">
																			<BaseCmsLabel :code="(field.name == 'type') ? 'gift_type_email' : 'gift_'+field.name" />
																		</label>
																		<span class="error" v-show="errorMessage" v-html="errorMessage" />
																	</div>
																</BaseFormField>
															</div>
														</div>

														<div class="gift-send-option">
															<BaseFormField v-for="field in postFields" :key="field.name" :item="field" v-slot="{errorMessage, floatingLabel}">
																<div class="field" :class="['field-'+field.name, {'ffl-floated': floatingLabel}]" v-if="field.name == 'type'">
																	<BaseFormInput :id="`gift-post-${field.name}-${value[0]}-${index}`" />
																	<label :for="`gift-post-${field.name}-${value[0]}-${index}`">
																		<BaseCmsLabel :code="(field.name == 'type') ? 'gift_type_address' : 'gift_'+field.name" />
																	</label>
																	<span class="error" v-show="errorMessage" v-html="errorMessage" />
																</div>
															</BaseFormField>
															<div class="gift-form" v-if="values.type == 'address'">
																<BaseFormField v-for="field in postFields" :key="field.name" :item="field" v-slot="{errorMessage, floatingLabel}">
																	<div class="field" :class="['field-'+field.name, {'ffl-floated': floatingLabel}]" v-if="field.name != 'type'">
																		<BaseFormInput :id="`gift-post-${field.name}-${value[0]}-${index}`" />
																		<label :for="`gift-post-${field.name}-${value[0]}-${index}`">
																			<BaseCmsLabel :code="(field.name == 'type') ? 'gift_type_address' : 'gift_'+field.name" />
																		</label>
																		<span class="error" v-show="errorMessage" v-html="errorMessage" />
																	</div>
																</BaseFormField>
															</div>
														</div>

														<div class="wc-gift-card-submit">
															<button type="submit" :class="{'loading': loading}" v-if="meta.dirty" :disabled="!meta.valid" @click="onSubmit"><UiLoader v-if="loading" /><BaseCmsLabel code="save" /></button>
														</div>
													</div>
												</div>
											</BaseCatalogGiftCardForm>
										</template>
									</template>
								</div>
							</div>

							<div class="checkout-cart" :class="[{'active': activeCart && mobileBreakpoint}]">
								<div class="checkout-cart-intro" @click="activeCart = !activeCart">
									<span v-show="mobileBreakpoint" class="checkout-cart-intro-dropdown"></span>
									<BaseCmsLabel code="shopping_cart" class="wc-cart-title" tag="h4" /><span v-show="mobileBreakpoint && !activeCart">:</span>
									<BaseUtilsAppUrls v-slot="{items: appUrls}">
										<NuxtLink :to="appUrls.webshop_shopping_cart" class="wc-btn-change">
											<BaseCmsLabel code="edit_shopping_cart" />
										</NuxtLink>
									</BaseUtilsAppUrls>
									<div v-show="mobileBreakpoint">
										<WebshopTotal :simple="true" />
									</div>
								</div>

								<BaseWebshopCart v-slot="{parcels}">
									<div class="wc-totals">
										<div class="wc-totals-inner" v-if="parcels[0]?.items?.length">
											<WebshopCartItemSmall v-for="item in parcels[0].items" mode="checkout" :data="item" :key="item.shopping_cart_code" />
										</div>
										<div class="wc-preview-footer">
											<WebshopTotal :simple="true" />
										</div>
									</div>
								</BaseWebshopCart>
							</div>
						</div>
					</div>

					<div class="wc-col2">
						<div class="wc-col2-cnt">
							<slot name="wcCol2" />
						</div>
					</div>
				</div>
				<div class="checkout-footer">
					<div class="wrapper checkout-footer-wrapper">
						<div class="checkout-footer-col1">
							<BaseCmsLabel code="cards" tag="div" class="checkout-cards" />
						</div>
						<div class="checkout-footer-col2">
							<BaseCmsLabel code="support_info" tag="div" class="checkout-support" />
							<BaseCmsCopyright class="copy" label="copyright" />
						</div>
					</div>
				</div>
			</BaseWebshopCheckout>
			<template #fallback>
				<BaseThemeUiLoading />
			</template>
		</ClientOnly>
	</div>
</template>

<script setup>
	const {onMediaQuery} = useDom();
	const activeCart = ref(false);
	const activeGiftCards = ref(false);
	const {matches: mobileBreakpoint} = onMediaQuery({
		query: '(max-width: 980px)',
	});
</script>

<style scoped lang="less">
	.checkout-layout{
		display: flex; flex-flow: column; height: 100vh;
		@media(max-width: @m){height: unset;}
	}
	.wc-cols{
		display: flex; flex-grow: 1;
		@media(max-width: @m){display: block; flex-grow: unset;}
	}
	.wc-col1{
		background: var(--colorLightBlueBackground); align-items: flex-end; width: 50%; padding: 56px 88px 100px 0; display: flex; flex-flow: column;
		@media(max-width: @t){align-items: unset; padding: 50px 50px 80px 40px;}
		@media(max-width: @m){width: 100%; padding: 24px 16px 22px;}
	}
	.wc-col2{
		width: 50%; align-items: baseline; display: flex; flex-flow: column;
		@media(max-width: @t){align-items: unset;}
		@media(max-width: @m){width: 100%;}
	}
	.wc-col1-cnt,.wc-col2-cnt{
		width: 400px;
		@media(max-width: @t){width: auto;}
	}
	.wc-col1-cnt{
		position: sticky; top: 56px;
		@media(max-width: @m){position: static;}
	}
	.wc-col2-cnt{
		margin: 56px 0 100px 88px;
		@media(max-width: @t){margin: 50px 40px 80px 50px;}
		@media(max-width: @m){margin: unset; padding: 20px 16px 0;}
	}
	@media (max-width: @m){
		.page-webshop-login .wc-col2-cnt{padding-bottom: 35px; border-bottom: 1px solid var(--colorBorderLightForms);}
	}

	//cart
	.checkout-cart{
		@media(max-width: @m){
			box-shadow: 0 8px 18px 0 rgba(0,89,194,0.12); background: var(--colorWhite); border-radius: var(--inputBorderRadius);
			&.active{
				:deep(.checkout-cart-intro){
					display: flex; flex-flow: column; align-items: baseline; padding-bottom: 22px;
					.w-totals-row{display: none;}
				}
				:deep(.wc-totals){display: block; border-top: 1px solid var(--colorBorderLightForms);}
				:deep(.checkout-cart-intro-dropdown){
					&:after{transform: rotate(90deg);}
				}
				:deep(.wc-btn-change){display: block;}
				:deep(.wc-cart-title){font-weight: bold; padding-bottom: 2px;}
			}
		}
	}
	.checkout-cart-intro{
		display: flex; justify-content: space-between; align-items: center; padding-bottom: 10px; position: relative;
		@media(max-width: @m){
			padding: 17px 16px; border: unset; align-items: center; justify-content: unset; min-height: 56px;
			:deep(.w-totals-total-shipping){display: none;}
			:deep(.w-totals-label){display: none;}
			:deep(.w-totals-row){padding-bottom: 0; font-size: 15px; padding-left: 5px;}
		}
	}
	.checkout-cart-intro-dropdown{
		width: 56px; height: auto; display: flex; align-items: center; justify-content: center; display: block; position: absolute; right: 0; top: 0; bottom: 0;
		&:after{.pseudo(auto,auto); top: 50%; left: 50%; margin-left: -4px; margin-top: -4px;  font: 12px/12px var(--fonti); color: var(--colorBaseBlue); transform: rotate(-90deg); .icon-arrow2; display: flex; align-items: center; justify-content: center;}
	}
	.wc-cart-title{
		padding-top: 0; padding-bottom: 0;
		@media(max-width: @m){font-weight: normal; font-size: 15px;}
	}
	.wc-btn-change{
		font-size: 14px; line-height: 19px; text-decoration: underline; color: var(--colorBaseBlue); text-underline-offset: 3px; transition: text-decoration-color .3s;
		&:hover{text-decoration-color: transparent;}
		@media(max-width: @m){display: none;}
	}
	.wc-totals{
		background: var(--colorWhite); border-radius: var(--inputBorderRadius); box-shadow: 0 8px 18px 0 rgba(0,89,194,0.12);
		@media(max-width: @m){background: unset; border-radius: unset; box-shadow: unset; display: none;}
	}
	.wc-preview-footer{
		padding: 12px 20px 12px 108px;
		:deep(.w-totals-row){padding-bottom: 1px;}
		@media(max-width: @m){padding: 10px 16px;}
	}

	// gift cards
	.wc-gift-cards{
		margin-bottom: 40px;
		@media (max-width: @m){display: none; margin-bottom: 0;}
	}
	.wc-gift-card{
		border-bottom: 1px solid var(--colorBorderLightForms); margin-bottom: 20px;
		@media (max-width: @m){margin-bottom: 10px;}
	}
	.wc-gift-card-title{
		font-weight: bold; position: relative; display: flex; align-items: center; margin-bottom: 10px; border-bottom: 1px solid var(--colorBorderLightForms); padding: 15px; font-size: 16px;
		@media (max-width: @m){border: 0; margin-bottom: 0; font-size: 14px;}
		&:before{
			.icon-gift(); font: 25px/1 var(--fonti); color: var(--colorBaseBlue); margin-right: 10px;
			@media (max-width: @m){font-size: 22px;}
		}
	}
	.wc-gift-card-cnt{
		padding: 5px 15px;
		input[type=radio]:checked + label{font-weight: normal;}
	}
	@media (max-width: @m){
		.wc-gift-cards-intro{border-bottom: 1px solid var(--colorBorderLightForms);}
	}
	.wc-gift-card-submit{padding-bottom: 10px;}
	.wc-gift-cards-container{
		@media (max-width: @m){margin-bottom: 10px;}
		&.active{
			.wc-gift-cards{display: block;}
			.wc-gift-cards-intro{padding-bottom: 15px;}
			.wc-gift-card{border-top: 0;}
		}
	}

	//coupons
	.base-coupon-checkout{margin-bottom: 0;}

	//footer
	.checkout-footer{
		padding: 24px 0; border-top: 1px solid var(--colorBorderLightForms); border-bottom: 1px solid var(--colorBorderLightForms);
		@media(max-width: @m){border-top: unset; border-bottom: unset; padding: 0;}
	}
	.checkout-footer-wrapper{
		display: flex; width: 975px; margin: 0 auto;
		@media(max-width: @t){width: auto;}
		@media(max-width: @m){padding: 0; display: block;}
	}
	.checkout-footer-col2{
		display: flex; align-items: center; padding-left: 78px; flex-grow: 1;
		@media(max-width: @t){justify-content: flex-end; padding-left: 15px;}
		@media(max-width: @m){justify-content: unset; display: block; background: var(--colorBase); padding: 22px 16px; color: var(--white);}
	}
	.copy{font-size: 14px; line-height: 18px;}
	.checkout-cards{
		display: flex; flex-wrap: wrap;
		:deep(p){display: flex; padding-bottom: 0;}
		:deep(a){
			display: flex; width: 48px; height: 32px; align-items: center; justify-content: center; flex-grow: 0; flex-shrink: 0; background: var(--colorWhite); border: 1px solid #E6E8EC; border-radius: var(--inputBorderRadius); margin-right: 8px;
			&:last-of-type{
				margin-left: 24px; width: 78px; border: unset; margin-right: 0;
				img{max-width: 78px;}
			}
		}
		@media(max-width: @t){
			:deep(a){
				&:last-of-type{margin-left: 12px;}
			}
		}
		@media(max-width: @m){
			padding: 13px 16px; width: 100%;
			:deep(p){width: 100%; justify-content: center;}
			:deep(a){width: 45px; height: 30px;}
		}
	}
	.checkout-support{
		display: flex; align-items: center;
		:deep(.support-info-title){display: none;}
		:deep(.support-info-desc){display: none;}
		:deep(.support-info-desc){display: none;}
		:deep(.support-info-wh){display: none;}
		:deep(a){
			text-decoration: underline; text-underline-offset: 3px; text-decoration-color: var(--colorBaseBlue); color: var(--colorBase); padding-left: 22px; font-size: 14px; line-height: 18px; margin-right: 32px; position: relative; transition: color .3s, text-decoration-color .3s;
			&:before{.pseudo(auto,auto); .icon-mail(); font: 17px/17px var(--fonti); left: -1px; top: 1px; color: var(--colorBaseBlue);}
			&:hover{
				text-decoration-color: transparent; color: var(--colorBaseBlue);
			}
		}
		:deep(.support-info-tel){text-decoration: none;
			&:before{.icon-mobile(); font: 16px/16px var(--fonti); top: 1px; left: 0;}
		}
		@media(max-width: @t){
			:deep(a){margin-right: 18px;}
		}
		@media(max-width: @m){
			justify-content: space-between;
			:deep(a){color: var(--colorWhite); text-decoration: underline !important; text-decoration-color: var(--colorBaseLighter) !important; text-underline-offset: 4px; font-size: 13px; margin: 0;}
		}
	}
	@media(max-width: @m){
		.copy{color: var(--colorTextLightGray); font-size: 13px; display: block; padding-top: 15px;}
	}
</style>
