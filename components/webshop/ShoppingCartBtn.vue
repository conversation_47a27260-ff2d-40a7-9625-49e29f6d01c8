<template>
	<BaseWebshopCart v-slot="{counter, cartUrl}">
		<div class="ww" @mouseover="cartPreview = true" @mouseleave="cartPreview = false" :class="{'counter': counter, 'empty': !counter, 'active': counter && cartPreview}">
			<NuxtLink :to="cartUrl" class="btn-header ww-items">
				<span class="btn-header-text"><BaseCmsLabel code="header_btn_cart" /></span>
				<ClientOnly>
					<span class="ww-counter btn-header-counter">{{counter}}</span>
				</ClientOnly>
			</NuxtLink>

			<ClientOnly>
				<LazyWebshopCartPreview v-if="counter && cartPreview && !tabletBreakpoint" mode="preview" />
			</ClientOnly>
		</div>
	</BaseWebshopCart>
</template>

<script setup>
	const cartPreview = ref(false);
	const {onMediaQuery} = useDom();
	const {matches: tabletBreakpoint} = onMediaQuery({
		query: '(max-width: 1300px)',
	});
</script>

<style lang="less" scoped>
	.ww {
		position: relative; z-index: 1111; height: 100%;
		&.counter{
			.ww-counter{display: flex; background: var(--colorLightGreen); border: 2px solid var(--colorBaseBlue); color: var(--colorBase);}
		}
		@media (min-width: @t) {
			&:hover{
				.ww-items{background: var(--colorDarkBlue);}
				.btn-header{text-decoration: none; color: var(--colorWhite);}
			}
		}
		@media (max-width: @t){
			margin-right: -8px;
			.btn-header{width: 64px;}
		}
		@media (max-width: @m){
			margin-right: 0;
			.btn-header{width: 20px; min-width: 20px; font-size: 0; line-height: 0; margin: 0 10px;}
		}
	}
	.ww-items:before{
		.icon-cart(); font: 20px/1 var(--fonti);
		@media (max-width: @t){height: 19px; font-size: 19px;}
		@media (max-width: @t){height: 20px; font-size: 20px;}
	}
	@media (min-width: @h){
		.fixed-header{
			.ww.active .ww-items{
				background: var(--colorBaseBlue);
				&:before{color: #fff;}
			}
		}
	}
</style>
