<template>
	<BaseWebshopCartPreview v-slot="{parcels, cartUrl}">
		<div :class="{'ww-preview': mode == 'preview', 'modal-preview': mode == 'modal'}">
			<div class="ww-preview-items" v-if="parcels[0]?.items?.length">
				<WebshopCartItemSmall :mode="mode" :onClose="onCloseItem" v-for="item in parcels[0].items" :data="item" :key="item.shopping_cart_code" />
			</div>
			<div class="ww-preview-footer">
				<WebshopTotal :simple="true" />

				<NuxtLink :to="cartUrl" class="btn btn-green" @click="onCloseItem">
					<BaseCmsLabel code="view_shopping_cart" />
				</NuxtLink>
			</div>
		</div>
	</BaseWebshopCartPreview>
</template>

<script setup>
	const props = defineProps({
		mode: String,
		onClose: Function,
	});

	function onCloseItem() {
		if (props.onClose) {
			props.onClose();
		}
		return false;
	}
</script>

<style lang="less" scoped>
	.ww-preview{
	    position: absolute; right: 0; top: 100%; width: 400px; background: #fff; padding-top: 8px; box-shadow: 0 16px 40px 0 rgba(0,12,26,0.16); border-bottom: 4px solid var(--colorBaseBlue);
	    &:before{display: block; content: ''; width: 0; height: 0; border-left: 6px solid transparent; border-right: 6px solid transparent; border-bottom: 6px solid #fff; position: absolute; right: 36px; top: -6px;}
	}
	.ww-preview-items{overflow: auto; max-height: 460px;}
	.ww-preview-footer{padding: 20px 32px 32px;}
	:deep(.w-totals-cart-total){font-size: 16px;}
	.btn-green{width: 100%; margin-top: 15px; box-shadow: none;}
	.fixed-header .ww-preview:before{right: 20px;}

	//modal
	.modal-preview{
		.ww-preview-items{
			max-height: unset;
			&::-webkit-scrollbar{-webkit-appearance: none; height: 0px; display: none;}
			&::-webkit-scrollbar-thumb{height: 0px; display: none;}
			&::-webkit-scrollbar-track-piece{height: 0px; display: none;}
			&::-webkit-scrollbar-track{height: 0px; display: none;}
		}
		.ww-preview-footer{
			position: fixed; width: 400px; background: var(--colorWhite); right: 0; bottom: 0; z-index: 20; box-shadow: 0 -3px 11px 0 rgba(0,12,26,0.13); padding: 20px 24px;
			.btn{margin-top: 8px;}
		}
		@media(max-width: @m){
			.ww-preview-footer{
				width: 100%; padding: 12px 16px 16px;
				.btn{margin-top: 6px;}
			}
			:deep(.w-totals-cart-total){font-size: 15px;}
		}
	}
</style>
