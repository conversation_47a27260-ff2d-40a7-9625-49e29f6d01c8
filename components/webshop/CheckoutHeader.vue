<template>
	<div class="wc-header">
		<div class="wrapper checkout-wrapper">
			<BaseCmsLogo class="logo" id="logo" />
			<div class="ch-step-progressbar-container">
				<div class="ch-step-progressbar">
					<span :style="'width:'+progress"></span>
					<span></span>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	const props = defineProps({
		step: Number,
	});
	const progress = computed(() => {
		if (props.step === 1) return '20%';
		if (props.step === 2) return '40%';
		if (props.step === 3) return '60%';
		if (props.step === 4) return '80%';
		if (props.step === 5) return '98%';
	});
</script>

<style lang="less" scoped>
	.wc-header{
		width: 100%; background: var(--colorBaseBlue);
		.wrapper{display: flex; min-height: 64px; justify-content: space-between; position: relative; align-items: center; width: 975px;}
		@media(max-width: @t){
			.wrapper{width: auto;}
		}
		@media(max-width: @m){
			.wrapper{min-height: 54px;}
		}
	}
	.logo{width: 160px; height: 41px; position: static; top: 0; left: 0; background: url(assets/images/logo_text.svg) no-repeat center center; background-size: contain;
		@media(max-width: @m){width: 118px; height: 30px;}
	}
	.ch-step-progressbar-container{
		width: 400px; position: relative; display: flex; height: 8px;
		@media(max-width: @t){width: calc(~"50% - 50px");}
		@media(max-width: @m){width: calc(~"100% - 152px"); height: 4px;}
	}
	.ch-step-progressbar{
		position: relative; display: flex; align-items: flex-start; justify-content: flex-start; width: 100%; height: auto; background: var(--colorDarkBlue); border-radius: 25px;
		span{position: absolute; top: 0; bottom: 0; border-radius: 8px; background: var(--colorWhite);}
	}
</style>
