<template>
	<BaseWebshopAddToCartModal v-slot="{items, status, onClose}" :auto-close="0">
		<div v-if="items?.length">
			<div class="add-to-cart-modal">
				<BaseWebshopCart v-slot="{counter}">
					<div class="add-to-cart-header">
						<div class="add-to-cart-header-title">
							<BaseCmsLabel code="in_cart" /> <span class="modal-counter">({{counter}})</span>
						</div>
						<BaseCmsLabel @click="onClose" tag="div" class="add-to-cart-modal-close" code="continue_shopping" />
					</div>

					<div class="add-to-cart-modal-body">
						<div v-if="status?.data?.label_name" class="add-to-cart-modal-status" :class="{'red': status?.data?.label_name == 'error_limitqty'}">
							<span><BaseCmsLabel :code="status.data.label_name" /></span>
						</div>
						<WebshopCartPreview mode="modal" :onClose="onClose" />
					</div>
				</BaseWebshopCart>
			</div>
			<div class="modal-bg-close" @click="onClose"></div>
		</div>
	</BaseWebshopAddToCartModal>
</template>

<style scoped lang="less">
	.add-to-cart-modal{
		position: fixed; inset: 0; z-index: 1113; overflow: auto; left: unset; right: 0; width: 400px; background: var(--colorWhite); padding: 72px 0 149px;
		&::-webkit-scrollbar{-webkit-appearance: none; height: 0px; display: none;}
		&::-webkit-scrollbar-thumb{height: 0px; display: none;}
		&::-webkit-scrollbar-track-piece{height: 0px; display: none;}
		&::-webkit-scrollbar-track{height: 0px; display: none;}
		@media(max-width: @m){width: 100%; left: 0; right: 0; padding: 56px 0 130px; top: 82px; border-radius: 4px 4px 0 0;}
	}
	.add-to-cart-header{
		min-height: 72px; box-shadow: 0 6px 6px 0 rgba(0,12,26,0.2); display: flex; align-items: center; justify-content: space-between; padding: 0 24px; position: fixed; width: 400px; top: 0; right: 0; background: var(--colorWhite); z-index: 20;
		@media(max-width: @m){min-height: 56px; top: 82px; padding: 0 16px; width: 100%; border-radius: 4px 4px 0 0;}
	}
	.add-to-cart-header-title{
		font-size: 20px; line-height: 28px; font-weight: bold;
		@media(max-width: @m){font-size: 17px; line-height: 24px;}
	}
	.add-to-cart-modal-close{
		font-size: 14px; line-height: 19px; color: var(--colorBaseBlue); text-decoration: underline; text-underline-offset: 3px; cursor: pointer; transition: text-decoration-color .3s;
		@media(max-width: @h){
			&:hover{text-decoration-color: transparent;}
		}
		@media(max-width: @m){font-size: 13px; line-height: 18px;}
	}
	.modal-counter{font-size: 16px; line-height: 24px; color: var(--colorBaseBlue); font-weight: normal;}

	.add-to-cart-modal-status{
		min-height: 40px; background: var(--colorBaseBlue); color: var(--colorWhite); padding: 0 24px; font-size: 14px; line-height: 20px; font-weight: bold; display: flex; align-items: center;
		:deep(span){
			position: relative; padding-left: 24px; display: flex; align-items: center;
			&:before{.pseudo(18px,18px); left: 0; top: 1px; background: var(--colorWhite); .icon-check(); font: 9px/9px var(--fonti); display: flex; align-items: center; justify-content: center; border-radius: 50%; color: var(--colorBaseBlue);}
		}
		&.red{
			background: var(--colorRed);
			&:deep(span){
				&:before{.icon-close(); color: var(--colorRed);}
			}
		}
		@media(max-width: @m){
			min-height: 32px; padding: 0 16px; font-size: 13px; line-height: 18px;
			:deep(span){
				padding-left: 22px;
				&:before{width: 16px; height: 16px; font: 9px/9px var(--fonti);}
			}
			&.red{
				&:deep(span){
					&:before{font: 8px/9px var(--fonti);}
				}
			}
		}
	}
	.modal-bg-close{content: ""; position: fixed; display: block; width: auto; height: auto; top: 0; right: 0; bottom: 0; left: 0; background: rgba(0, 12, 26, 0.6); z-index: 1112;}
	/*.modal-item{
		display: flex; padding: 24px 24px 20px;
		&:not(:first-child){border-top: 1px solid var(--colorBorderLightForms); border-bottom: 1px solid var(--colorBorderLightForms); margin-top: -1px;}
	}
	.add-to-cart-modal-cnt{display: flex; flex-flow: column; flex-grow: 1; padding: 0 0 0 16px;}
	.add-to-cart-modal-image{
		flex-shrink: 0; max-width: 80px; max-height: 80px; width: 80px; height: 80px;
		:deep(img){display: block; width: auto; height: auto; max-width: 100%; max-height: 100%;}
	}
	.add-to-cart-modal-manufacturer{
		font-size: 12px; line-height: 14px; text-transform: uppercase; color: var(--colorBaseBlue); padding-bottom: 2px; font-weight: bold;
		a{text-decoration: none;}
	}
	.add-to-cart-modal-title{
		font-size: 14px; line-height: 19px; padding-bottom: 2px; font-weight: bold; color: var(--colorBase);
		a{text-decoration: none; color: var(--colorBase);}
	}
	.add-to-cart-modal-code,.add-to-cart-modal-attr{font-size: 12px; line-height: 18px;}
	.add-to-cart-modal-price{padding-top: 3px; font-size: 14px; line-height: 20px; font-weight: bold; display: flex; align-items: center;}
	.modal-old-price{font-size: 12px; line-height: 17px; margin-right: 5px; text-decoration: line-through; font-weight: normal;}
	.modal-current-price{
		&.discount{color: var(--colorRed);}
	}*/

	/* animations */
	.fade-enter-active,
	.fade-leave-active {
		transition: opacity 0.2s ease;
	}

	.fade-enter-from,
	.fade-leave-to {
		opacity: 0;
	}
</style>
