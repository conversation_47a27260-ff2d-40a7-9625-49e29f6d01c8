<template>
	<div class="wwp" :class="{'wwc': mode == 'checkout', 'wwp-modal': mode == 'modal'}" :id="data.shopping_cart_code">
		<div class="wwp-image">
			<NuxtLink :to="data.item.url_without_domain" @click="mode == 'modal' ? onClose() : null">
				<BaseUiImage loading="lazy" :data="data.item?.image_thumbs?.['width150-height150']" default="/images/no-image-104.jpg" :alt="data.item.title" />
			</NuxtLink>
		</div>
		<div class="wwp-desc">
			<div class="wwp-brand">
				<NuxtLink v-if="data.item.manufacturer.url" :to="data.item.manufacturer.url" @click="mode == 'modal' ? onClose() : null">
					{{data.item.manufacturer.title}}
				</NuxtLink>
				<template v-else>{{data.item.manufacturer.title}}</template>
			</div>
			<div class="wwp-title">
				<NuxtLink :to="data.item.url_without_domain" @click="mode == 'modal' ? onClose() : null">
					{{ data.item.title }}
				</NuxtLink>
			</div>
			<div class="wwp-atts-cnt">
				<div class="wwp-code"><BaseCmsLabel code="code" />: {{ (data.item.variation?.code) ? data.item.variation.code : data.item.code }}</div>
				<div class="wwp-att" v-if="data.item.variation?.attributes">
					<div v-for="attr in data.item.variation.attributes" :key="attr.id">{{attr.attribute_title}}: {{attr.title}}</div>
				</div>
			</div>
			<div class="wwp-price">
				<div v-if="data.total_basic > data.total" class="wwp-old-price"><BaseUtilsFormatCurrency :price="data.gross_amount * data.quantity" /></div>
				<div :class="['wwp-current-price', {'red': data.total_basic > data.total}]"><BaseUtilsFormatCurrency :price="data.total" /></div>
				<div class="wwp-qty-count" v-if="data.quantity > 1">
					<span>{{ data.quantity }} x </span><BaseUtilsFormatCurrency :price="data.unit_price" />
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	const props = defineProps(['data', 'mode', 'onClose']);
</script>

<style lang="less" scoped>
	.wwp{display: flex; padding: 20px 32px; border-bottom: 1px solid var(--colorBorderLightForms);}
	.wwp-image{
		width: 64px; height: 64px; flex-shrink: 0; align-self: baseline;
		:deep(a){display: block;}
		:deep(img){display: block; width: auto; height: auto;}
	}
	.wwp-desc{font-size: 12px; line-height: 1.3; padding: 0 0 0 16px; flex: 1 1 auto;}
	.wwp-brand{
		color: var(--colorBaseBlue); text-transform: uppercase; font-weight: bold; padding-bottom: 2px;
		a{text-decoration: none; color: var(--colorBaseBlue);}
	}
	.wwp-title{
		font-size: 14px; line-height: 1.3; font-weight: bold; padding: 1px 0 3px;
		a{
			color: var(--colorBase); text-decoration: none; .transition(color,0.3s);
			@media (min-width: @h){
				&:hover{color: var(--colorBaseBlue);}
			}
		}
	}
	.wwp-code{padding-bottom: 3px;}
	.wwp-price{font-size: 12px; display: flex; align-items: center; flex-wrap: wrap; padding-top: 4px;}
	.wwp-qty-count{color: var(--colorTextLightGray); padding-top: 3px; width: 100%;}
	.wwp-old-price{text-decoration: line-through; padding-right: 8px;}
	.wwp-current-price{
		font-size: 14px; line-height: 1.3; font-weight: bold;
		&.red{color: var(--colorRed);}
	}

	//checkout
	.wwc{
		padding: 20px;
		.wwp-image{width: 72px; height: 72px;}
		.wwp-title{padding: 0 0 6px;}
		@media(max-width: @m){
			padding: 16px;
			.wwp-title{font-size: 13px;}
			.wwp-current-price{font-size: 13px;}
		}
	}

	//modal
	.wwp-modal{
		padding: 24px 24px 20px;
		.wwp-image{width: 80px; height: 80px;}
		@media(max-width: @m){
			padding: 20px 16px;
			.wwp-image{width: 72px; height: 72px;}
		}
	}
</style>
