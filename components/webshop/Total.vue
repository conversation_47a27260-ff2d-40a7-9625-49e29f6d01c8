<template>
	<div class="w-totals" :class="simple && 'w-totals-simple'">
		<BaseWebshopTotal v-slot="{items}">
			<template v-if="items">
				<!-- <div class="w-totals-row w-totals-without-discount" v-show="items.total_items_basic_without_discount">
					<span class="w-totals-label"><BaseCmsLabel code="total_minus_discount" default="Ukupno bez popusta" />: </span>
					<span class="w-totals-value"><BaseUtilsFormatCurrency :price="items.total_items_basic_without_discount" /></span>
				</div> -->
				<div class="w-totals-row w-totals-total-discount" v-if="items.total_items_basic_discount">
					<span class="w-totals-label"><BaseCmsLabel code="total_discount" default="Popust" />: </span>
					<span class="w-totals-value"><BaseUtilsFormatCurrency :price="items.total_items_basic_discount" /></span>
				</div>
				<!-- <div class="w-totals-row w-totals-total-without-tax" v-if="items.total_items_basic">
					<span class="w-totals-label"><BaseCmsLabel code="total_minus_tax" default="Osnovica za obračun PDV-a" />: </span>
					<span class="w-totals-value"><BaseUtilsFormatCurrency :price="items.total_items_basic" /></span>
					<div v-if="items.total_basic_taxranks_description" class="w-totals-multitax" v-html="items.total_basic_taxranks_description" />
				</div> -->
				<!-- <div class="w-totals-row w-totals-total-tax" v-if="items.total_items_tax">
					<span class="w-totals-label"><BaseCmsLabel code="total_tax" default="Ukupan PDV" /></span>
					<span class="w-totals-value"><BaseUtilsFormatCurrency :price="items.total_items_tax" /></span>
					<div v-if="items.total_tax_taxranks_description" class="w-totals-multitax" v-html="items.total_tax_taxranks_description" />
				</div> -->
				<div class="w-totals-row w-totals-total-without-shipping" v-if="items.total_items_total">
					<span class="w-totals-label"><BaseCmsLabel code="total_minus_shipping" default="Ukupno bez dostave" />: </span>
					<span class="w-totals-value"> <BaseUtilsFormatCurrency :price="items.total_items_total" /> </span>
				</div>
				<div class="w-totals-row w-totals-total-extra-payment-box" v-if="items.total_extra_payment">
					<span class="w-totals-label"><BaseCmsLabel code="discount" default="Popust" />: </span>
					<span class="w-totals-value"><BaseUtilsFormatCurrency :price="items.total_extra_payment" /></span>
				</div>
				<div class="w-totals-row w-totals-total-extra-discount-box" v-if="items.total_extra_discount">
					<span class="w-totals-label"><BaseCmsLabel code="discount" default="Popust" />: </span>
					<span class="w-totals-value"><BaseUtilsFormatCurrency :price="items.total_extra_discount" /></span>
				</div>
				<div class="w-totals-row w-totals-total-extra-coupon-box" v-if="items.total_extra_coupon">
					<span class="w-totals-label"><BaseCmsLabel code="coupon" default="Kupon" />: </span>
					<span class="w-totals-value"> <BaseUtilsFormatCurrency :price="items.total_extra_coupon" /> </span>
				</div>
				<div class="w-totals-row w-totals-total-extra-coupon-product-box" v-if="items.total_extra_coupon_product">
					<span class="w-totals-label"><BaseCmsLabel code="coupon_product" default="Kupon" />: </span>
					<span class="w-totals-value"><BaseUtilsFormatCurrency :price="items.total_extra_coupon_product" /></span>
				</div>
				<div class="w-totals-row w-totals-total-extra-affiliate-box" v-if="items.total_extra_affiliate">
					<span class="w-totals-label"><BaseCmsLabel code="total_extra_affiliate" />: </span>
					<span class="w-totals-value"><BaseUtilsFormatCurrency :price="items.total_extra_affiliate" /></span>
				</div>
				<div class="w-totals-row w-totals-total-extra-abandonment-box" v-if="items.total_extra_abandonment">
					<span class="w-totals-label"><BaseCmsLabel code="total_extra_abandonment" />: </span>
					<span class="w-totals-value"><BaseUtilsFormatCurrency :price="items.total_extra_abandonment" /></span>
				</div>
				<div class="w-totals-row w-totals-total-extra-loyalty-box" v-if="items.total_extra_loyalty">
					<span class="w-totals-label">
						<BaseCmsLabel code="loyalty" default="Kartica kupca" />
						<span>{{ items.total_extra_loyalty_discount_percent }}</span> %:
					</span>
					<span class="w-totals-value cart_info_total_extra_loyalty"><BaseUtilsFormatCurrency :price="items.total_extra_loyalty" /></span>
				</div>
				<div class="w-totals-row w-totals-total-extra-cc-discount-box" v-if="items.total_extra_cc_discount">
					<span class="w-totals-label"><BaseCmsLabel code="total_extra_cc_discount" />: </span>
					<span class="w-totals-value"><BaseUtilsFormatCurrency :price="items.total_extra_cc_discount" /></span>
				</div>
				<div class="w-totals-row w-totals-total-extra-cover-box" v-if="items.total_extra_cover">
					<span class="w-totals-label"><BaseCmsLabel code="total_extra_cover" />: </span>
					<span class="w-totals-value"><BaseUtilsFormatCurrency :price="items.total_extra_cover" /></span>
				</div>
				<div class="w-totals-row w-totals-total-extra-relatedlist-box" v-if="items.total_extra_relatedlist">
					<span class="w-totals-label"><BaseCmsLabel code="total_extra_relatedlist" />: </span>
					<span class="w-totals-value"><BaseUtilsFormatCurrency :price="items.total_extra_relatedlist" /></span>
				</div>
				<div class="w-totals-row w-totals-total-extra-supplement-box" v-if="items.total_extra_supplement">
					<span class="w-totals-label"><BaseCmsLabel code="total_extra_supplement" />: </span>
					<span class="w-totals-value"><BaseUtilsFormatCurrency :price="items.total_extra_supplement" /></span>
				</div>
				<div class="w-totals-row w-totals-total-shipping">
					<span class="w-totals-label"><BaseCmsLabel code="total_extra_shipping" default="Dostava" />: </span>
					<span class="w-totals-value">
						<template v-if="items.total_extra_shipping == 0">
							<span class="w-totals-label-free"><BaseCmsLabel code="free" /></span>
						</template>
						<template v-else>
							<BaseUtilsFormatCurrency :price="items.total_extra_shipping" />
						</template>
					</span>
				</div>
				<div class="w-totals-row w-totals-cart-total" v-if="items.total">
					<span class="w-totals-label"><BaseCmsLabel code="total_to_pay" />: </span>
					<span class="w-totals-value"><BaseUtilsFormatCurrency :price="items.total" /></span>
				</div>
			</template>
		</BaseWebshopTotal>
	</div>
</template>

<style lang="less" scoped>
	.w-totals{
		font-size: 14px; line-height: 1.4;
		@media (max-width: @t){font-size: 13px;}
	}
	:deep(.w-totals-row){display: flex; justify-content: space-between; padding-bottom: 5px;}
	:deep(.w-totals-cart-total){font-weight: bold;}
	:deep(.w-totals-label-free){font-weight: bold; color: var(--colorGreen);}
	.w-totals-simple{
		:deep(.w-totals-row:not(.w-totals-total-shipping, .w-totals-cart-total)){display: none;}
	}
</style>

<script setup>
	const props = defineProps(['simple']);
</script>
