<template>
	<div class="wp" :id="data.shopping_cart_code">
		<div class="wp-image">
			<NuxtLink :to="data.item.url_without_domain">
				<BaseUiImage loading="lazy" :data="data.item?.image_thumbs?.['width150-height150']" default="/images/no-image-104.jpg" :alt="data.item.title" />
			</NuxtLink>
		</div>
		<div class="wp-desc">
			<div class="wp-brand">
				<NuxtLink v-if="data.item.manufacturer.url" :to="data.item.manufacturer.url">
					{{data.item.manufacturer.title}}
				</NuxtLink>
				<template v-else>{{data.item.manufacturer.title}}</template>
			</div>
			<div class="wp-title">
				<NuxtLink :to="data.item.url_without_domain">
					{{ data.item.title }}
				</NuxtLink>
			</div>
			<div class="wp-atts-cnt">
				<div class="wp-code"><BaseCmsLabel code="code" />: {{ (data.item.variation?.code) ? data.item.variation.code : data.item.code }}</div>
				<div class="wp-att" v-if="data.item.variation?.attributes">
					<div v-for="attr in data.item.variation.attributes" :key="attr.id">{{attr.attribute_title}}: {{attr.title}}</div>
				</div>
			</div>
			<BaseWebshopRemoveProduct :item="data" v-slot="{onRemove, loading}">
				<div class="wp-remove" :class="{'loading': loading}" @click="onRemove"><BaseCmsLabel code="remove_product" /></div>
			</BaseWebshopRemoveProduct>
		</div>
		<div class="wp-qty-cnt">
			<BaseThemeWebshopQty :quantity="data.quantity" :limit="data.available_quantity" :item="data" mode="cart" />
			<div class="wp-unit" v-if="data?.unit">{{data.unit}}</div>
			<div class="wp-unit" v-else><BaseCmsLabel code="unit" /></div>
		</div>
		<div class="wp-price">
			<div v-if="data.total_basic > data.total" class="wp-old-price"><BaseUtilsFormatCurrency :price="data.gross_amount * data.quantity" /></div>
			<div :class="['wp-current-price', {'red': data.total_basic > data.total}]"><BaseUtilsFormatCurrency :price="data.total" /></div>
			<div class="wp-qty-count" v-if="data.quantity > 1">
				<span>{{ data.quantity }} x </span><BaseUtilsFormatCurrency :price="data.unit_price" />
			</div>
		</div>
	</div>
</template>

<script setup>
	const props = defineProps(['data']);
</script>

<style lang="less" scoped>
	.wp{
		display: flex; align-items: center; padding: 25px 0; border-bottom: 1px solid var(--colorBorderLightForms);
		@media (max-width: @t){padding: 23px 0;}
		@media (max-width: @m){padding: 20px 16px; flex-wrap: wrap; position: relative;}
	}
	.wp-image{
		flex: 0 0 104px;
		@media (max-width: @t){flex: 0 0 72px; align-self: flex-start;}
		img{display: block;}
	}
	.wp-desc{
		padding: 0 56px 0 20px; font-size: 12px; line-height: 1.3; flex: 1 1 auto;
		@media (max-width: @t){padding: 0 26px 0 16px;}
		@media (max-width: @m){flex: 1 1 calc(~'100% - 72px'); padding: 0 0 13px 16px; align-self: flex-start;}
	}
	.wp-brand{
		color: var(--colorBaseBlue); text-transform: uppercase; font-weight: bold;
		a{text-decoration: none; color: var(--colorBaseBlue);}
	}
	.wp-title{
		font-size: 14px; line-height: 1.4; font-weight: bold; padding: 1px 0 8px;
		@media (max-width: @m){font-size: 13px; padding-bottom: 5px;}
		a{
			color: var(--colorBase); text-decoration: none; .transition(color,0.3s);
			@media (min-width: @h){
				&:hover{color: var(--colorBaseBlue);}
			}
		}
	}
	.wp-code{padding-bottom: 3px;}
	.wp-remove{
		display: inline-block; cursor: pointer; padding-left: 20px; margin-top: 11px; position: relative; .transition(color,0.3s);
		@media (max-width: @m){position: absolute; bottom: 36px; left: 20px; margin: 0; padding-left: 14px;}
		&:before, &:after{
			.pseudo(16px,2px); top: 6px; left: -1px; background: var(--colorRed); .rotate(45deg);
			@media (max-width: @m){width: 12px; height: 1px; top: 7px;}
		}
		&:after{.rotate(-45deg);}
		&.loading{
			&:before{
				.pseudo(18px,18px); background: url(assets/images/loader.svg) no-repeat 0 0; background-size: contain; top: -2px; left: 0;
				@media (max-width: @m){left: -6px;}
			}
			&:after{display: none;}
		}
		@media (min-width: @h){
			&:hover{color: var(--colorRed);}
		}
	}
	.wp-price{
		font-size: 12px; text-align: right; flex: 0 0 150px;
		@media (max-width: @t){flex: 0 0 120px;}
		@media (max-width: @m){flex: 1 1 auto; line-height: 1.3;}
	}
	.wp-old-price{text-decoration: line-through;}
	.wp-current-price{
		font-size: 14px; line-height: 1.3; font-weight: bold;
		@media (max-width: @m){font-size: 13px;}
		&.red{color: var(--colorRed);}
	}
	.wp-qty-count{
		color: var(--colorTextLightGray); padding-top: 4px;
		@media (max-width: @m){padding-top: 1px;}
	}
	.wp-qty-cnt{
		flex: 0 0 104px; position: relative;
		@media (max-width: @m){margin-left: 88px;}
	}
	.wp-unit{position: absolute; left: 36px; right: 36px; bottom: 1px; text-align: center; font-size: 10px; text-transform: lowercase;}
	:deep(.qty-input-container){display: flex; height: 48px; border-radius: var(--inputBorderRadius); border: 1px solid var(--colorBorderLightForms);}
	:deep(.qty-input){
		display: block; height: 100%; border: 0; text-align: center; padding: 0;
		@media (max-width: @m){font-size: 15px;}
	}
	:deep(.qty-btn){
		display: block; height: 100%; font-size: 0; flex: 0 0 36px; position: relative; cursor: pointer;
		&:before{ .pseudo(12px,2px); top: 50%; left: 50%; margin: 0 0 0 -6px; background: var(--colorBaseBlue); }
	}
	:deep(.qty-btn-inc){
		&:after{ .pseudo(2px,12px); top: 50%; left: 50%; background: var(--colorBaseBlue); margin: -5px 0 0 -1px;}
	}
	:deep(.qty-status){
		position: absolute; left: -10px; right: -10px; top: 108%; font-size: 11px; line-height: 1.2; text-align: center; z-index: 10;
		@media (max-width: @m){background: #fff;}
	}
</style>
