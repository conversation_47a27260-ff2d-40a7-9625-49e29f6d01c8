<template>
	<div class="ww-fs-cnt">
		<div class="ww-fs-bar" :style="'width: '+item.percent"></div>
		<div class="ww-fs-title">
			<BaseCmsLabel code="free_shipping_missing_title" tag="span" /> <strong><BaseUtilsFormatCurrency :price="item.amount" /></strong>
		</div>
	</div>
</template>

<script setup>
	const props = defineProps(['item']);
</script>

<style lang="less" scoped>
	.ww-fs-cnt{
		display: flex; align-items: center; justify-content: center; height: 32px; border-radius: 8px; background: var(--colorYellow); font-size: 12px; position: relative; overflow: hidden;
		@media (max-width: @m){border-radius: 0;}
		@media (max-width: 350px){font-size: 10px;}
	}
	.ww-fs-bar{position: absolute; top: 0; bottom: 0; left: 0; .transition(width,0.3s); background: rgba(0,0,0,0.1);}
	.ww-fs-title{
		position: relative; padding: 1px 0 0 31px;
		&:before{position: absolute; .icon-truck2(); font: 16px/16px var(--fonti); color: var(--colorBase); left: 0px; top: 2px;}
	}
</style>
