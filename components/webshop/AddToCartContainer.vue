<template>
	<div class="cd-add-to-cart-container" :class="{'unavailable': !product.is_available}">
		<template v-if="props.item.variation_request != 0 && !props.selectedVariation">
			<div class="btn btn-green cd-btn-add cd-btn-select-variation" @click="scrollTo('#cd-price-container', {offset: 230})">
				<BaseCmsLabel code="select_size" />
			</div>
		</template>
		<template v-else>
			<template v-if="product.is_available && ![3,4].includes(Number(product.status))">
				<div class="cd-qty-container" v-show="product.available_qty > 1">
					<BaseThemeWebshopQty :quantity="1" :limit="product.available_qty" v-model="qty" />
					<div class="wp-unit" v-if="product?.unit">{{product.unit}}</div>
					<div class="wp-unit" v-else><BaseCmsLabel code="unit" /></div>
				</div>
				<BaseWebshopAddToCart v-slot="{onAddToCart, loading}" :data="{modalData: product, shopping_cart_code: product.shopping_cart_code, quantity: qty}">
					<div class="btn btn-green cd-btn-add" :class="{'loading': loading}" @click="onAddToCart">
						<UiLoader v-if="loading" /><span><BaseCmsLabel code="add_to_shopping_cart" /></span>
					</div>
				</BaseWebshopAddToCart>
			</template>
			<template v-else>
				<FeedbackNotificationForm :status="Number(item.status) == 4 ? 'on-request' : 'not-available'" :item="product" />
			</template>
		</template>

		<ClientOnly>
			<CatalogDetailSetCompare :item="item" />
		</ClientOnly>
	</div>
</template>

<script setup>
	const {scrollTo} = useDom();
	const props = defineProps({
		item: Object,
		selectedVariation: Object,
	})

	const product = computed(() => props.selectedVariation || props.item)
	const qty = ref();
</script>

<style lang="less" scoped>
	.cd-add-to-cart-container{
		position: relative; display: flex; padding-right: 140px;
		&.variations-container{align-items: flex-end; margin-top: 15px;}
		@media (max-width: @t){display: flex; flex-wrap: wrap; padding-right: 0; padding-bottom: 10px;}
	}
	.cd-qty-container{position: relative; display: block; width: 104px; flex-grow: 0; flex-shrink: 0; margin-right: 8px;}
	.wp-unit{position: absolute; left: 36px; right: 36px; bottom: 1px; text-align: center; font-size: 10px; text-transform: lowercase;}
	.cd-btn-add{
		display: flex; align-items: center; justify-content: center; width: 100%; flex-grow: 1; flex-shrink: 1; cursor: pointer; box-shadow: none;
		span{
			position: relative; display: block; padding-left: 32px;
			&:before{.pseudo(20px,20px); .icon-cart(); font: 20px/20px var(--fonti); color: var(--colorWhite); top: 0; left: 0;}
		}
		&.loading span{opacity: 0;}
		@media (max-width: @t){width: calc(~"100% - 112px");}
	}
	.cd-btn-select-variation{flex-grow: 1; cursor: default; opacity: .5; width: 100%;
		@media (min-width: @h){
			background: var(--colorGreen);
		}
	}
</style>
