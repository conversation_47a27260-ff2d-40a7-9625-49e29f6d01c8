<template>
	<div class="wc-step" :class="[{'wc-step-subtitle': title == true},{'wc-step1': step === 1},{'wc-step2': step === 2},{'wc-step3': step === 3},{'wc-step4': step === 4},{'wc-step-completed': completed == true}]">
		<template v-if="title === true">
			<span class="num">{{step}}</span>
			<BaseCmsLabel tag="span" :code="'step'+step" class="title" />
		</template>
		<template v-else>
			<BaseUtilsAppUrls v-slot="{items: appUrls}">
				<template v-if="step === 1">
					<NuxtLink class="step" :to="appUrls.webshop_customer">
						<span class="num">1</span>
						<BaseCmsLabel tag="span" code="step1" class="title" />
						<BaseCmsLabel v-if="completed == true" tag="span" class="edit" code="edit_shopping_cart" />
					</NuxtLink>
				</template>
				<template v-if="step === 2">
					<NuxtLink class="step" :to="appUrls.webshop_shipping">
						<span class="num">2</span>
						<BaseCmsLabel tag="span" code="step2" class="title" />
						<BaseCmsLabel v-if="completed == true" tag="span" class="edit" code="edit_shopping_cart" />
					</NuxtLink>
				</template>
				<template v-if="step === 3">
					<NuxtLink class="step" :to="appUrls.webshop_payment">
						<span class="num">3</span>
						<BaseCmsLabel tag="span" code="step3" class="title" />
						<BaseCmsLabel v-if="completed == true" tag="span" class="edit" code="edit_shopping_cart" />
					</NuxtLink>
				</template>
				<template v-if="step === 4">
					<NuxtLink class="step" :to="appUrls.webshop_review_order">
						<span class="num">4</span>
						<BaseCmsLabel tag="span" code="step4" class="title" />
						<BaseCmsLabel v-if="completed == true" tag="span" class="edit" code="edit_shopping_cart" />
					</NuxtLink>
				</template>
			</BaseUtilsAppUrls>
		</template>
	</div>
</template>

<script setup>
	const props = defineProps({
		step: {
			type: null,
			default: false,
		},
		completed:{
			type: Boolean,
			default: false,
		},
		title:{
			type: Boolean,
			default: false,
		},
	});
</script>

<style scoped lang="less">
	.wc-step-subtitle{
		font-size: 20px; line-height: 28px; color: var(--colorBaseBlue); font-weight: bold; padding-bottom: 16px;
		.num{background: var(--colorLightBlueBackground) !important; color: var(--colorBaseBlue) !important; font-weight: bold;}
		@media(max-width: @m){font-size: 19px; line-height: 24px; padding-bottom: 20px;}
	}
	.wc-step{
		position: relative; display: flex; align-items: center;
		.step{
			display: flex; align-items: center; padding: 15px 0; border-top: 1px solid var(--colorBorderLightForms); border-bottom: 1px solid var(--colorBorderLightForms); width: 100%; margin-top: -1px; text-decoration: none; font-weight: bold; font-size: 20px; line-height: 28px; color: var(--colorBase); .transition(color);
			&:hover{
				color: var(--colorBaseBlue);
				.edit{text-decoration-color: transparent;}
			}
		}
		.num{display: flex; align-items: center; justify-content: center; width: 32px; height: 32px; border-radius: 50%; background: var(--colorBaseBlue); margin-right: 12px; color: var(--colorWhite); font-size: 14px; line-height: 16px;}
		@media(max-width: @m){
			.step{font-size: 19px; line-height: 24px; padding: 20px 16px; margin-left: -16px; margin-right: -16px; width: calc(~"100% - -32px");}
			.num{width: 24px; height: 24px; margin-right: 8px; font-size: 12px; line-height: 16px;}
		}
	}
	.wc-step-completed{
		&:first-child{
			.step{padding-top: 0; border-top: 0;}
		}
		.num{
			background: var(--colorGreen); font-size: 0; left: 0;
			&:after{.pseudo(auto,auto); .icon-check(); font: 12px/12px var(--fonti); color: var(--colorWhite);}
		}
		@media(max-width: @m){
			.num:after{font: 10px/10px var(--fonti);}
		}
	}
	.edit{font-weight: normal; position: absolute; right: 0; text-decoration: underline; right: 0; color: var(--colorBaseBlue); text-underline-offset: 3px; font-size: 14px; line-height: 18px; .transition(text-decoration-color);}
</style>
