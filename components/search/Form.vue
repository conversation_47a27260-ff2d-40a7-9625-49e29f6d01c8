<template>
	<BaseSearchForm :fetch="searchFormConfig" :reset-on-empty-input="mobileBreakpoint ? false : true" v-slot="{searchResults, updateValue, totalSearchResults, searchTerm, loading, handleInput, resetInput, onReset, selectedIndex, onSubmit}" ref="searchForm" @afterReset="onAfterReset">
		<Body :class="{'search-active': swOpen}" />
		<div class="sw" :class="[{'loading': loading}, {'active': searchTerm}, {'fixed-search' : mode == 'fixedSearch'}]" ref="sw">
			<span class="btn-header sw-toggle" @click="showSearch">
				<span class="btn-header-text"><BaseCmsLabel code="header_btn_search" /></span>
			</span>
			<span class="m-search-title"><BaseCmsLabel code="mobile_search_title" /></span>

			<div class="sw-form" id="main_search" method="get">
				<input ref="searchInput" class="sw-input" name="search_q" id="search_q" type="text" :value="searchTerm" @input="updateValue" autocomplete="off" @blur="toggleListContainer(false)" @keyup="handleInput" @keyup.enter="onMobileSearch" @focus="toggleListContainer(true)" />
				<label class="sw-label" :class="{'hidden': searchTerm}" for="search_q" v-html="labels.get('enter_search_term')" />

				<div v-show="searchTerm" @click="mobileBreakpoint ? resetInput() : onReset(); toggleListContainer(true); focusInput()" class="sw-clear"></div>
				<button @click="onSubmit(), onMobileSearch($event)" class="sw-btn" type="submit"><BaseCmsLabel code="search_button" /></button>

				<ClientOnly>
					<div class="sw-list-container" :class="{'active': (!mobileBreakpoint && showListContainer && !totalSearchResults) || (mobileBreakpoint && !totalSearchResults && !searchResults)}">
						<div class="sw-list-title"><BaseCmsLabel code="search_list_title" /></div>
						<BaseCmsNav code="search_list" v-slot="{items}">
							<ul class="sw-list">
								<li v-for="item in items" :key="item.id">
									<NuxtLink :target="item.target_blank != 0 ? '_blank' : null" :to="item.url_without_domain" @click="onAfterReset">{{ item.title }}</NuxtLink>
								</li>
							</ul>
						</BaseCmsNav>
					</div>
					<LazySearchAutocomplete v-if="totalSearchResults || loading" :searchResults="searchResults" :totalSearchResults="totalSearchResults" :loading="loading" :selectedIndex="selectedIndex" :onReset="onReset" :formatCategory="formatCategory" />
				</ClientOnly>
			</div>
		</div>
	</BaseSearchForm>
</template>

<script setup>
	const {onClickOutside, onMediaQuery} = useDom();
	const props = defineProps(['mode']);

	const searchFormConfig = {
		'allow_models': ['catalogcategory', 'catalogmanufacturer', 'catalogproduct', 'publish', 'cms'],
		'result_fields': {
			'catalogproduct': ['image', 'price', 'price_custom', 'manufacturer_title', 'code', 'category', 'basic_price', 'basic_price_custom', 'discount_percent', 'discount_percent_custom'],
			'catalogcategory': ['id', 'url', 'title', 'code', 'parents'],
		},
		'result_per_page': {
			'catalogcategory': 5,
			'catalogmanufacturer': 12,
			'_default': 4
		},
		'result_image': '80x80_r'
	}

	const labels = useLabels();
	const {matches: mobileBreakpoint} = onMediaQuery({
		query: '(max-width: 980px)',
	});

	const showListContainer = ref(false);

	function toggleListContainer(val) {
		showListContainer.value = val;
	}

	const sw = ref(null);
	const swOpen = ref(false);
	const searchForm = ref(null);
	onClickOutside(sw, () => {
		searchForm.value.resetAutocomplete();
		showListContainer.value = false;
		if(!mobileBreakpoint.value){
			swOpen.value = false;
		}
	});

	//mobile menu
	const searchInput = ref(null);
	let windowOffset = 0;
	function showSearch() {
		if (swOpen.value == false) {
			windowOffset = window.scrollY;
			swOpen.value = true;
			showListContainer.value = true;

			// ios hack. Focus does not work on initially hidden input elements
			const dummyInput = document.createElement("input");
			dummyInput.style.position = "absolute";
			dummyInput.style.top = "-500px";
			document.body.appendChild(dummyInput);
			dummyInput.focus();

			setTimeout(()	=> {
				focusInput();
				document.body.removeChild(dummyInput);
			}, 100);
		} else {
			swOpen.value = false;
			setTimeout(function(){
				window.scroll(0,windowOffset);
			},50);
		}
	}

	function focusInput() {
		if(searchInput.value) searchInput.value.focus();
	}

	function onMobileSearch(e) {
		if(mobileBreakpoint.value) swOpen.value = false;
	}

	// after reset hook to remove body active class
	function onAfterReset() {
		swOpen.value = false;
	}

	// add parent category titles (same category titles)
	function formatCategory(item) {
		const parents = (item.parents?.length) ? item.parents.map(parent => parent.title) : [];
		if(parents?.length) {
			return `${parents.join(' - ')} - ${item.title}`;
		}
		return item.title;
	}
</script>

<style lang="less" scoped>
	.sw {
		flex-grow: 1;position: relative;
		&.active{
			.sw-input{padding-left: 40px;}
		}
		@media (max-width: @m){
			height: 100%; flex-grow: initial;
			&.active{
				.sw-input{padding-left: 47px;}
			}
		}
	}
	.sw-toggle{
		position: relative; z-index: 1111; height: 100%; display: none;
		&:before{.icon-search(); font: 17px/1 var(--fonti);}
		@media (max-width: @m){display: flex;}
	}
	.sw-form {
		display: flex;
		@media (max-width: @m){display: none;}
	}
	.sw-label {
		position: absolute; left: 20px; top: 13px; font-size: 14px; color: var(--colorTextLightGray); padding: 0; cursor: text; z-index: 1;
		&.hidden {display: none;}
		@media (max-width: @m){top: 29px; left: 33px; font-size: 15px;}
	}
	.sw-input {
		width: 100%; height: 48px; border: none; padding: 0 50px 0 20px; border-radius: var(--inputBorderRadius); font-size: 14px; font-weight: bold;
		&:focus + label {opacity: 0.4;}
		&::-webkit-search-cancel-button {display: none;}
		@media (min-width: @h){
			&:hover, &:focus {border: none;}
		}
		@media (max-width: @m){font-size: 15px; padding: 0 50px 0 17px; border: 1px solid var(--colorBorderLightForms);}
	}
	.sw-clear {
		width: 14px; height: 14px; background: var(--colorWhite); display: flex; align-items: center; justify-content: center; flex-shrink: 0; text-decoration: none; position: absolute; left: 19px; top: 17px; cursor: pointer;
		&:after {.icon-close(); font: 14px/1 var(--fonti); color: var(--colorRed); text-align: center; text-indent: 1px; width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; .transition(all);}
		@media (min-width: @h){
			&:hover {text-decoration: none;}
		}
		@media (max-width: @m){
			left: 33px; top: 33px;
			&:after{color: var(--colorBaseBlue);}
		}
	}
	.sw button {
		display: block; font-size: 0; line-height: 0; background: var(--colorYellow); width: 40px; height: 40px; padding: 0; display: flex; align-items: center; justify-content: center; flex-shrink: 0; z-index: 1; position: absolute; right: 4px; top: 4px; border-radius: var(--inputBorderRadius); box-shadow: none; .transition(background);
		&:after {.icon-search(); font: 18px/40px var(--fonti); color: var(--colorBase); position: relative; .transition(color);}
		&.disabled {pointer-events: none;}
		@media (min-width: @h){
			&:hover {
				text-decoration: none;
				&:after {color: #fff;}
			}
		}
		@media (max-width: @m){right: 20px; top: 20px;}
	}

	.sw-list-container{
		position: absolute; background: var(--colorWhite); top: 64px; padding: 32px 42px 29px; box-shadow: 0 16px 40px rgba(0,12,26,0.16); right: 0; left: 0; z-index: 1000; opacity: 0; visibility: hidden; transition: opacity .3s, visibility .3s;
		&:after{.pseudo(10px,10px); background: var(--colorWhite); .rotate(45deg); top: -5px; left: 40px; opacity: 0; .transition(opacity);}
		&.active{
			opacity: 1; visibility: visible;
			&:after{opacity: 1;}
		}
		@media (max-width: @m){
			position: relative; top: unset; padding: 16px 0 0; box-shadow: none; right: unset; left: unset; display: none;
			&:after{display: none;}
			&.active{display: block;}
		}
	}
	.sw-list-title{
		display: block; font-size: 12px; line-height: 1.4; font-weight: bold; margin-bottom: 15px; text-transform: uppercase; color: var(--colorTextLightGray);
		p{padding-bottom: 0;}
		@media (max-width: @m){line-height: 1.3; margin-bottom: 12px;}
	}
	.sw-list{
		position: relative; display: block; column-count: 3; list-style: none; padding: 0; margin: 0;
		li{display: block; padding: 0 0 3px;}
		a{display: inline-block; color: var(--colorBase); .transition(color); text-decoration: none;
			@media (min-width: @t){
				&:hover{color: var(--colorBaseBlue); text-decoration: underline;}
			}
		}
		@media (max-width: @m){
			column-count: 2;
			li{padding-bottom: 12px;}
			a{text-decoration: underline; text-underline-offset: 5px; text-decoration-color: var(--colorBaseBlue);}
		}
	}
	.m-search-title{display: none; align-items: center; height: 56px; position: absolute; left: 24px; right: 60px; font-size: 15px; line-height: 1.3; color: var(--colorWhite); font-weight: 600;}
</style>

<style lang="less">
	@media (max-width: @m) {
		.search-active #__nuxt {
			.page-wrapper > *:not(.header, .header > *){height: 0!important; overflow: hidden; min-height: 0; padding: 0; margin: 0;}
			.logo, .header-buttons, .m-btn, .cw-compare, .ww{display: none!important;}
			.m-search-title{display: flex;}
			.sw{width: 100%; z-index: 11; position: fixed; top: 0; bottom: 0; left: 0; right: 0;}
			.sw-form{display: block; position: fixed; top: 0; left: 0; right: 0; bottom: 0; margin-top: 56px; background: var(--colorWhite); overflow: auto; overflow-x: hidden; padding: 16px 16px 0;}
			.sw-toggle{
				width: 56px; height: 56px; margin: 0; right: 0; position: absolute;
				&:before{.icon-close(); font-size: 14px; height: 14px;}
			}
		}
	}
</style>
