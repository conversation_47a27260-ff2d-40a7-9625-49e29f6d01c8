<template>
	<div class="ac">
		<div class="ac-loading" v-if="loading" />
		<div class="ac-wrapper">
			<div class="ac-col1" v-if="searchResults?.catalogcategory?.length">
				<div class="ac-menu ac-categories">
					<div class="ac-title">{{ labels.get('autocomplete_categories') }}</div>
					<NuxtLink @click="onReset" v-for="item in searchResults.catalogcategory" :key="item.id" :to="item.url_without_domain" class="ac-menu-item ac-menu-cube" :class="{'active': item.index == selectedIndex}">
						{{ formatCategory(item) }}
					</NuxtLink>
				</div>
				<div v-if="searchResults?.catalogmanufacturer?.length" class="ac-menu ac-brands">
					<div class="ac-title">{{ labels.get('autocomplete_manufacturers') }}</div>
					<NuxtLink @click="onReset" v-for="item in searchResults.catalogmanufacturer" :key="item.id" :to="item.url_without_domain" class="ac-menu-item ac-menu-cube" :class="{'active': item.index == selectedIndex}">
						{{ item.title }}
					</NuxtLink>
				</div>
			</div>
			<div class="ac-col2" v-if="searchResults?.catalogproduct?.length">
				<div class="ac-title">{{ labels.get('autocomplete_products') }}</div>
				<NuxtLink @click="onReset" v-for="item in searchResults.catalogproduct" :key="item.id" :to="item.url_without_domain" class="ac-item" :class="{'active': item.index == selectedIndex}">
					<div class="ac-item-img">
						<BaseUiImage loading="lazy" :src="item.image_upload_path" width="70" height="70" default="/images/no-image-80.jpg" />
					</div>
					<div class="ac-item-cnt">
						<div class="ac-item-brand">{{ item.manufacturer_title }}</div>
						<div class="ac-item-title">{{ item.title }}</div>
						<div class="ac-item-price">
							<template v-if="(item.discount_percent_custom > 0 || item.price_custom < item.basic_price_custom)">
								<div class="ac-old-price"><BaseUtilsFormatCurrency :price="item.basic_price_custom" /></div>
								<div class="ac-current-price ac-discount-price"><BaseUtilsFormatCurrency :price="item.price_custom" /></div>
							</template>
							<template v-else>
								<div class="ac-current-price"><BaseUtilsFormatCurrency :price="item.price_custom" /></div>
							</template>
						</div>
					</div>
				</NuxtLink>
				<div class="ac-showall" v-if="searchResults?.catalogproduct_show_all?.show">
					<NuxtLink @click="onReset" class="ac-showall-btn" :to="searchResults?.catalogproduct_show_all.url_without_domain">
						{{ labels.get('show_all') }} <span>({{ searchResults.catalogproduct_show_all.total }})</span>
					</NuxtLink>
				</div>
			</div>
			<div class="ac-col3" v-if="searchResults?.publish?.length || searchResults?.cms?.length">
				<div v-if="searchResults?.publish?.length" class="ac-menu ac-posts">
					<div class="ac-title">{{ labels.get('autocomplete_publish') }}</div>
					<NuxtLink @click="onReset" v-for="item in searchResults.publish" :key="item.id" :to="item.url_without_domain" class="ac-menu-item" :class="{'active': item.index == selectedIndex}">
						{{ item.title }}
					</NuxtLink>
					<div class="ac-showall-blog" v-if="searchResults?.publish_show_all?.show">
						<NuxtLink @click="onReset" :to="searchResults.publish_show_all.url_without_domain" class="ac-showall-btn-blog">
							{{ labels.get('show_all_posts') }}
						</NuxtLink>
					</div>
				</div>
				<div v-if="searchResults?.cms?.length" class="ac-menu ac-cms">
					<div class="ac-title">{{ labels.get('autocomplete_cms') }}</div>
					<NuxtLink @click="onReset" v-for="item in searchResults.cms" :key="item.id" :to="item.url_without_domain" class="ac-menu-item" :class="{'active': item.index == selectedIndex}">
						{{ item.title }}
					</NuxtLink>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	const labels = useLabels();
	const props = defineProps(['searchResults', 'totalSearchResults', 'loading', 'selectedIndex', 'onReset', 'formatCategory']);
</script>

<style lang="less" scoped>
	/*------- autocomplete -------*/
	.ac {
		position: absolute; top: 64px; min-width: 150px; min-height: 50px; left: 0; background: #fff; box-shadow: 0 16px 40px rgba(0, 12, 26, 0.16); z-index: 11111;
		&:before {.pseudo(auto,20px); left: 0; right: 0; top: -20px; z-index: 1;}
		&:after {.pseudo(10px,10px); top: -4px; left: 40px; background: var(--colorWhite); .rotate(45deg); z-index: 1;}

		@media (max-width: @t){
			left: -183px; right: -297px;
			&:after{left: 225px;}
		}
		@media (max-width: @m){
			position: relative; left: unset; right: unset; top: unset; box-shadow: none;
			&:before{display: none;}
			&:after{display: none;}
		}
	}
	.ac-wrapper {
		display: flex; position: relative;
		@media (max-width: @m){flex-flow: column; padding-top: 16px; padding-bottom: 27px;}
	}
	.ac-title {
		display: block; font-size: 12px; line-height: 1.3; color: var(--colorTextLightGray); margin-bottom: 11px; text-transform: uppercase; width: 100%; font-weight: bold;
		@media (max-width: @m){margin-bottom: 12px;}
	}
	.ac-col1, .ac-col3 {
		width: auto; max-width: 100%; flex-grow: 1; flex-shrink: 1; padding: 32px 40px 41px; border-right: 1px solid var(--colorLightGray); position: relative; min-width: 340px;
		@media (max-width: @t){min-width: unset; width: 100%; padding: 20px 25px 30px;}
		@media (max-width: @m){padding: 0; border-right: 0;}
	}
	.ac-col2 {
		display: block; flex-shrink: 0; flex-grow: 0; width: 372px; padding: 32px 41px 41px; border-right: 1px solid var(--colorLightGray);
		@media (max-width: @t){padding: 20px 25px 30px; width: 340px;}
		@media (max-width: @m){width: 100%; padding: 0; border-right: 0;}
	}
	.ac-col3 {border-right: none;}
	.ac-item {
		display: flex; align-items: flex-start; justify-content: flex-start; width: 100%; padding: 0 0 15px; text-decoration: none; .transition(background);
		@media (max-width: @m){align-items: center; padding-bottom: 17px;}
		@media (min-width: @h){
			&:hover{
				.ac-item-title{color: var(--colorBaseBlue);}
			}
		}
	}
	.ac-item-img {
		display: flex; justify-content: center; align-items: center; width: 64px; height: 64px; flex-shrink: 0; margin-right: 16px;
		:deep(img) {width: auto; height: auto; max-width: 100%; max-height: 100%;}
		@media (max-width: @m){width: 72px; height: 72px;}
	}
	.ac-item-cnt {display: block; width: 100%; position: relative;}
	.ac-item-brand{
		font-size: 11px; line-height: 1.1; font-weight: normal; font-weight: bold; color: var(--colorBaseBlue); text-transform: uppercase;
		@media (max-width: @m){font-size: 12px; line-height: 1.3;}
	}
	.ac-item-title {
		display: block; font-size: 12px; color: var(--colorBase); text-decoration: none; position: relative; padding-top: 2px; .transition(color);
		@media (max-width: @m){font-size: 13px; line-height: 1.3; padding-top: 0; font-weight: bold;}
	}
	.ac-item-price {
		display: flex; align-items: center; margin-top: 2px; font-size: 12px; line-height: 1.5; font-weight: bold; color: var(--colorBase); position: relative;
		@media (max-width: @m){font-size: 13px; line-height: 1.3;}
	}
	.ac-old-price{line-height: 1.3; font-weight: normal; margin-right: 6px; text-decoration: line-through;}
	.ac-discount-price{color: var(--colorRed);}
	.ac-showall {
		display: block; width: 100%; padding: 15px 0 0;
		@media (max-width: @t){padding-top: 5px;}
		@media (max-width: @m){padding-top: 8px;}
	}
	.ac-showall-btn {
		display: flex; align-items: center; justify-content: center; width: 100%; height: 48px; background: var(--colorBaseBlue); font-size: 16px; line-height: 1.4; color: var(--colorWhite); font-weight: bold; text-decoration: none; padding: 0 25px; cursor: pointer; box-shadow: 0 10px 16px 0 rgba(0, 89, 194, 0.2); border-radius: var(--inputBorderRadius);
		:deep(span) {opacity: 0.5; padding-left: 2px;}
		@media (max-width: @t){font-size: 15px;}
		@media (max-width: @m){
			line-height: 1.3;
			span{padding-left: 4px;}
		}
	}
	.ac-menu {
		display: block; width: 100%;
		&:last-child {margin-bottom: 0;}
	}
	.ac-menu-item {
		display: block; font-size: 14px; line-height: 1.3; color: var(--colorBase); text-decoration: none; padding-left: 17px; position: relative; margin-bottom: 5px; transition: color .3s;
		&:before {.pseudo(5px, 5px); border: 2px solid var(--colorBaseBlue); top: 5px; left: 0; border-radius: 100%;}
		@media (min-width: @h){
			&:hover{color: var(--colorBaseBlue); text-decoration: underline;}
		}
		&.active {color: var(--colorBaseBlue); text-decoration: underline;}
		@media (max-width: @t){
			font-size: 13px;
			&:before{top: 3px;}
		}
		@media (max-width: @m){margin-bottom: 10px;}
	}
	.ac-categories,.ac-posts {
		padding-bottom: 32px;
		@media (max-width: @t){padding-bottom: 29px;}
	}
	.ac-categories, .ac-brands{
		@media (max-width: @m){padding-bottom: 24px;}
	}
	.ac-col2{
		@media (max-width: @m){padding-bottom: 32px;}
	}
	.ac-categories,.ac-brands {display: flex; flex-wrap: wrap;}

	.ac-menu-cube {
		width: auto; padding-left: 0; padding: 9px 15px; margin-right: 8px; margin-bottom: 8px; border: 1px solid var(--colorBorderLightForms); border-radius: var(--inputBorderRadius); transition: color .3s, border-color .3s;
		&:before {display: none;}
		@media (min-width: @h){
			&:hover{color: var(--colorBaseBlue); border-color: var(--colorBaseBlue); text-decoration: none;}
		}
		&.active {color: var(--colorBaseBlue); border-color: var(--colorBaseBlue); text-decoration: none;}

		@media (max-width: @t){padding: 5px 11px 4px;}
	}

	.ac-showall-blog {
		display: block; width: 100%; padding: 4px 0 0 17px;
		@media (max-width: @m){padding: 2px 0 0 0;}
	}
	.ac-showall-btn-blog {
		font-size: 14px; line-height: 1.3; font-weight: bold; color: var(--colorBaseBlue); text-decoration: underline; text-underline-offset: 3px; text-decoration-color: var(--colorBaseBlue); cursor: pointer; .transition(text-decoration-color);
		@media (min-width: @h){
			&:hover {text-decoration-color: transparent;}
		}
		@media (max-width: @t){font-size: 12px;}
		@media (max-width: @m){font-size: 13px;}
	}
	.ac-loading {
		display: flex; align-items: center; justify-content: center; background: rgba(255, 255, 255, 0.8); position: absolute; top: 0; right: 0; bottom: 0; left: 0; z-index: 1;
		&:before {.pseudo(35px,35px); background: url(/assets/images/loader.svg); background-size: contain;}
	}
</style>
