<template>
	<div class="s-header">
		<div class="wrapper s-header-wrapper">
			<div class="s-header-title">
				<BaseCmsLabel code="search_term" />
				<strong>{{extraterm}}</strong>
			</div>
			<div class="s-nav-cnt special">
				<BaseThemeSearchNavigation>
					<template #title="{item}">
						<span>
							{{item.title}} <span class="s-counter">{{item.total}}</span>
						</span>
					</template>
				</BaseThemeSearchNavigation>
			</div>
		</div>
	</div>
</template>

<script setup>
	const props = defineProps(['extraterm']);
</script>

<style lang="less" scoped>
	.s-header{
		position: relative; display: block; width: 100%; background: var(--colorLightBlueBackground); padding: 56px 0 32px;
		@media (max-width: @t){padding: 40px 0 30px;}
		@media (max-width: @m){padding: 16px 0;}
	}
	.s-header-wrapper{display: flex; flex-flow: column; align-items: center;}
	.s-header-title{
		display: flex; flex-flow: column; align-items: center; font-size: var(--fontSizeLabel); text-transform: uppercase; color: var(--colorBaseBlue); font-weight: bold;
		:deep(strong){text-transform: none; color: var(--colorBase); font-size: var(--fontSizeH3); padding-top: 3px;}
		@media (max-width: @m){line-height: 21px;}
	}
	.s-nav-cnt{position: relative; }
	:deep(.s-nav){
		position: relative; display: flex; gap: 8px; list-style: none; padding: 0; margin: 28px 0 0;
		li{
			display: block;
			&.selected{
				a{
					background: var(--colorBaseBlue); color: var(--colorWhite);
					&:after{opacity: 1;}
				}
				.s-counter{color: var(--colorWhite);}
			}
		}
		a{
			position: relative; display: flex; align-items: center; justify-content: center; font-weight: bold; background: var(--colorWhite); padding: 11px 24px; color: var(--colorBase); text-decoration: none; border-radius: var(--inputBorderRadius); box-shadow: 0 8px 18px rgba(0,89,194,0.12); transition: color 0.3s, background 0.3s;
			&:after{.pseudo(8px,8px); background: var(--colorBaseBlue); .rotate(45deg); bottom: -3px; left: calc(~"50% - 4px"); opacity: 0; .transition(opacity);}
			@media (min-width: @h){
				&:hover{
					background: var(--colorBaseBlue); color: var(--colorWhite);
					.s-counter{color: var(--colorWhite);}
				}
			}
		}
		.s-counter{font-weight: normal; font-size: 14px; color: var(--colorBaseBlue); .transition(color);}
		@media (max-width: @t){margin-top: 15px;}
		@media (max-width: @m){
			margin-top: 24px; gap: 4px;
			a{font-size: 13px; line-height: 17px; padding: 15px 24px;}
			.s-counter{font-size: 11px;}
		}
	}
</style>
