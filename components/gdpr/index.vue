<template>
	<div>
		<BaseThemeGdpr :enable-consents="true" />
	</div>
</template>

<style lang="less" scoped>
	:deep(input[type=checkbox] + label){
		padding: 2px 0 4px 34px;
		&:before{width: 22px; height: 22px; line-height: 22px;}
	}
	:deep(.gdpr-cookie-warning){
	       max-width: 600px;
	       @media (max-width: @m){max-width: 420px; font-size: 13px;}
		   a{color: #fff; font-weight: bold;}
	   }
	:deep(.gdpr-body){
		background: var(--colorBaseBlue); padding: 120px 0 100px; line-height: 1.5;
		@media (max-width: @m){padding: 98px 40px 60px;}
	}
	:deep(.gdpr-icon){
		width: 160px; height: 160px; top: -80px; left: calc(50% - 80px); border: 12px solid var(--colorBaseBlue); background: #fff url(assets/images/custom-icons/cookie.svg) center no-repeat;
		@media (max-width: @m){width: 128px; height: 128px; top: -64px; left: calc(50% - 64px); background-size: 48px; }
	}
	:deep(.gdpr-icon-img){display: none;}
	:deep(.gdpr-cookie-btns){
		display: flex; flex-flow: column; max-width: 280px; margin: auto;
	    @media (max-width: @m){max-width: none;}
		.gdpr_submit_all_button{background: #fff; color: var(--colorBaseBlue); box-shadow: 0 5px 20px 0 rgba(0,12,26,0.3); margin-top: 20px;}
		.gdpr_configurator_button{
			background: 0; height: auto; text-decoration: underline; font-size: 14px; font-weight: normal; padding: 0; margin-top: 32px; box-shadow: none;
			@media (max-width: @m){font-size: 13px; margin-top: 20px;}
			@media (min-width: @h){
				&:hover{text-decoration: none;}
			}
		}
	}
	:deep(.active-configurator){
		background: var(--colorLightBlueBackground);
		@media (max-width: @m){background: #fff;}
		.gdpr-icon{
			box-shadow: 0 10px 16px 0 rgba(0,89,194,0.2); margin: 0 auto 50px;
			@media (max-width: @m){box-shadow: none; margin-bottom: 34px;}
		}
	}
	:deep(.gdpr-configurator-btn-necessary), :deep(.gdpr-configurator-btn-all){
		background: 0; height: auto; text-decoration: underline; font-size: 14px; font-weight: normal; padding: 0; color: var(--colorBaseBlue); box-shadow: none;
		@media (min-width: @h){
			&:hover{text-decoration: none;}
		}
	}
	:deep(.gdpr-configurator){max-width: 640px; overflow: initial;}
	:deep(.gdpr-configurator-items){
		background: 0; padding: 0;
		a{font-weight: bold;}
	}
	:deep(.gdpr-configurator-item){
		@media (max-width: @m){
			&:not(:last-child){padding-bottom: 6px;}
		}
	}
	:deep(input:disabled+label){color: var(--colorTextLightGray);}
	:deep(.gdpr-configurator-buttons){margin-top: 20px; gap: 30px;}
	:deep(.gdpr-configurator-title){
		margin-bottom: 12px;
		@media (max-width: @m){margin-bottom: 4px;}
	}
	:deep(.gdpr-configurator-reject-allow-btns){
		padding: 0 101px;
		@media (max-width: @m){padding: 0 26px;}
		.btn{
			width: auto; flex-grow: 0; text-underline-offset: 4px;
			&:hover{background: none; color: var(--colorBaseBlue);}
		}
	}
	:deep(.gdpr-configurator-btn-all){margin-right: 18px;}
</style>

<style lang="less">
	.gdpr-active-configurator{overflow: hidden;}
</style>
