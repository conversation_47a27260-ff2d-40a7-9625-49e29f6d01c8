<?php

/*
m = mode, default none
	db - generate database config
d = domain name, default current domain
s = db sufix, default '_web'
*/

define('SYSPATH', 1);

$mode = (isset($_GET['m']) AND $_GET['m']) ? $_GET['m'] : '';

$original_domain = $_SERVER['HTTP_HOST'];
$domain = (isset($_GET['d']) AND $_GET['d']) ? $_GET['d'] : $original_domain;

$domain_clear = preg_replace("/[^A-Za-z0-9 ]/", '', $domain);
$random_string = substr(str_shuffle("0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!#$%&/()=?*"), 0, 20);

$original_root = $_SERVER['DOCUMENT_ROOT']; // bez / na kraju
$root = str_replace($original_domain, $domain, $original_root);

$sufix = (isset($_GET['s']) AND $_GET['s']) ? $_GET['s'] : '_web';

if ($mode == 'db')
{
	$db_name = substr($domain_clear, 0, 12).$sufix;
	$db_user = $db_name;
	$db_pass = $random_string;

	$commands = array();

	$commands[] = "\t\t\t'database'   => '{$db_name}',";
	$commands[] = "\t\t\t'username'   => '{$db_user}',";
	$commands[] = "\t\t\t'password'   => '{$db_pass}',";

	header("Content-type: text/plain");
	echo implode("\n", $commands)."\n ";
}
else
{
	//define('SYSPATH', 1);
	$exist_db = @include 'application/config/database.php';
	$exist_db_host = @$exist_db['default']['connection']['hostname'];
	$exist_db_name = @$exist_db['default']['connection']['database'];
	$exist_db_user = @$exist_db['default']['connection']['username'];
	$exist_db_pass = @$exist_db['default']['connection']['password'];
		
	$create_db = TRUE;
	
	if ($exist_db_host AND $exist_db_name AND $exist_db_user)
	{
		$db_name = $exist_db_name;
		$db_user = $exist_db_user;
		$db_pass = $exist_db_pass;
		// Create connection
		$con = mysqli_connect($exist_db_host, $exist_db_user, $exist_db_pass, $exist_db_name);

		// Check connection
		if ( ! mysqli_connect_errno($con))
			$create_db = FALSE;
	}
	

	$commands = array();

	$commands[] = "# set chmod";
	$commands[] = "chmod -R 777 {$root}/application/cache/ {$root}/application/logs/ {$root}/upload/ {$root}/upload_data/ {$root}/data/";
	$commands[] = "\n# create sym link";
	$commands[] = "ln -s /var/www/vhosts/_marker_app/shared/ {$root}";
	//$commands[] = "ln -s /var/www/vhosts/_marker_app_beta/shared/ {$root}";
	//$commands[] = "unlink {$root}/shared";

	if ($create_db)
	{
		if ( ! isset($db_name))
			$db_name = substr($domain_clear, 0, 12).$sufix;
		if ( ! isset($db_user))
			$db_user = $db_name;
		if ( ! isset($db_pass))
			$db_pass = $random_string;

		$commands[] = "\n# create db";

		$commands[] = "mysql -p";
		$commands[] = "create database {$db_name};";
		$commands[] = "grant all privileges on {$db_name}.* to '{$db_user}'@'localhost' identified by \"{$db_pass}\"; ";
		$commands[] = "flush privileges;";
		$commands[] = "exit;";
	}

	header("Content-type: text/plain");
	echo implode("\n", $commands);
}

?>