<?php

defined('SYSPATH') or die('No direct script access.');

require SYSPATH . 'classes/kohana/core' . EXT;
if (is_file(APPPATH . 'classes/kohana' . EXT)) {
    require APPPATH . 'classes/kohana' . EXT;
} else {
    require SYSPATH . 'classes/kohana' . EXT;
}

date_default_timezone_set('Europe/Zagreb');
setlocale(LC_ALL, 'en_US.utf-8');
spl_autoload_register(['Kohana', 'auto_load']);
ini_set('unserialize_callback_func', 'spl_autoload_call');
I18n::lang('en-us');
Cookie::$salt = 'cms_cookie';

if (isset($_SERVER['KOHANA_ENV'])) {
    Kohana::$environment = constant('Kohana::' . strtoupper($_SERVER['KOHANA_ENV']));
}

Kohana::init();
Kohana::$log->attach(new Log_File(APPPATH . 'logs'));
Kohana::$config->attach(new Config_File);

Kohana::modules([
    // basic application
    'admincp' => MODPATH . 'admincp',
    'auth' => MODPATH . 'auth',
    'app' => MODPATH . 'app',
    'admin' => MODPATH . 'admin',
    'minify' => MODPATH . 'minify',
    // extra aplication
    'hapi' => MODPATH . 'hapi',
    'cms' => MODPATH . 'cms2',
    'siteforms' => MODPATH . 'siteforms',
    'gdpr' => MODPATH . 'gdpr',
    'catalog' => MODPATH . 'catalog2',
    'webshop' => MODPATH . 'webshop2',
    'search' => MODPATH . 'search2',
    'faq' => MODPATH . 'faq',
    'newsletter' => MODPATH . 'newsletter2',
    'location' => MODPATH . 'location2',
    'rotator' => MODPATH . 'rotator2',
    'feedback'     => MODPATH.'feedback2',
    'api'   => MODPATH.'api2',
    'sitemap' => MODPATH . 'sitemap',
    'publish' => MODPATH . 'publish2',
    'cmsinit' => MODPATH . 'cmsinit',
]);
