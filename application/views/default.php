<!DOCTYPE html>
<html lang="<?php echo Arr::get(Kohana::config('app.html_lang'), $info['lang'], $info['lang']); ?>" data-default_lang="<?php echo Arr::get(Kohana::config('app.html_lang'), Kohana::config('app.language'), Kohana::config('app.language')); ?>">
<?php /*<html lang="<?php echo Arr::get(Kohana::config('app.html_lang'), $info['lang'], $info['lang']); ?>" data-default_lang="<?php echo Arr::get(Kohana::config('app.html_lang'), Kohana::config('app.language'), Kohana::config('app.language')); ?>" data-currency_code="<?php echo $currency['code']; ?>" data-currency_display="<?php echo $currency['display']; ?>" data-currency_exchange="<?php echo $currency['exchange']; ?>" data-webshop_min_order="<?php echo intval(Arr::get($shopping_cart, 'total_extra_shipping_min_total', '0')); ?>">*/ ?>
<head>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
	<meta name="format-detection" content="telephone=no">
	<meta name="author" content="Marker.hr">
	<link rel="dns-prefetch" href="//www.google-analytics.com">
	<link rel="dns-prefetch" href="//ssl.google-analytics.com">
	<link rel="dns-prefetch" href="//connect.facebook.net">
	<link rel="dns-prefetch" href="//static.ak.facebook.com">
	<link rel="dns-prefetch" href="//s-static.ak.facebook.com">
	<link rel="dns-prefetch" href="//fbstatic-a.akamaihd.net">
	<link rel="dns-prefetch" href="//maps.gstatic.com">
	<link rel="dns-prefetch" href="//maps.google.com">
	<link rel="dns-prefetch" href="//maps.googleapis.com">
	<link rel="dns-prefetch" href="//mt0.googleapis.com">
	<link rel="dns-prefetch" href="//mt1.googleapis.com">
	<!--
	<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
	<link rel="icon" type="image/png" href="/favicon-32x32.png" sizes="32x32">
	<link rel="icon" type="image/png" href="/favicon-16x16.png" sizes="16x16">
	<link rel="mask-icon" href="/safari-pinned-tab.svg" color="#5bbad5">
	<link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">
	<meta name="theme-color" content="#232323">
	<meta name="msapplication-navbutton-color" content="#232323">
	<meta name="apple-mobile-web-app-status-bar-style" content="#232323">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
	-->
	<title><?php $this->block('title'); ?><?php $this->endblock('title'); ?></title>
	<?php if (Kohana::$environment !== 1): ?><meta name="robots" content="noindex, nofollow"><?php endif; ?>
	<?php $this->block('seo'); ?><?php $this->endblock('seo'); ?>
	<?php echo Html::media('js_gmodernizr'); ?>
	<?php echo Html::media('css_gdefault'); ?>
	<?php if (Kohana::$environment === 1): ?><?php echo Google::universal_analytics($info['ganalytics_code'], FALSE, $info['controller'].'_'.$info['action']); ?><?php endif; ?>
	<?php $this->block('extrahead'); ?><?php $this->endblock('extrahead'); ?>
</head>
<body class="<?php echo $info['page_class']; ?><?php $this->block('page_class'); ?><?php $this->endblock('page_class'); ?>" data-module="<?php echo $info['controller']; ?>" data-autoscroll="<?php echo $info['identical_url']; ?>">
	<?php //echo Facebook::init('<App ID>', $info['lang']); ?>

	<header class="header">
		<a href="<?php echo Utils::homepage($info['lang']); ?>" id="logo"></a>

		<ul class="nav">
			<?php $active_menu_item = Utils::active_urls($info['lang'], $info['cmspage_url']); ?>
			<?php echo Widget_Cms::menu(['lang' => $info['lang'], 'generate_tree' => TRUE, 'code' => 'main', 'selected' => $active_menu_item]); ?>
		</ul>

		<?php echo View::factory('cms/widget/languages'); ?>
		<?php //echo View::factory('search/widget/form'); ?>
		<?php //echo View::factory('auth/widget/user_box'); ?>
		<?php //echo View::factory('webshop/widget/shopping_cart'); ?>
	</header>

	<section class="main">
		<?php $this->block('content_layout'); ?>
			<div class="bc">
				<?php $this->block('breadcrumb'); ?>
				<?php $breadcrumb = Cms::breadcrumb($info['lang'], $info['cmspage_url']); ?>
				<?php echo View::factory('cms/widget/breadcrumb', ['breadcrumb' => $breadcrumb]); ?>
				<?php $this->endblock('breadcrumb'); ?>
			</div>
			<?php $this->block('content'); ?><?php $this->endblock('content'); ?>
		<?php $this->endblock('content_layout'); ?>
	</section>

	<aside class="sidebar">
		<?php $this->block('sidebar'); ?><?php $this->endblock('sidebar'); ?>
	</aside>

	<footer class="footer">
		<p class="copy"><?php echo str_replace('%YEAR%', date('Y'), Arr::get($cmslabel, 'copyright')); ?></p>
		<p class="dev"><?php echo Kohana::config('signature.webshop.'.$info['lang']); ?></p>

		<?php $footer_menu = Widget_Cms::menu(array('lang' => $info['lang'], 'code' => 'footer', 'selected' => $active_menu_item)); ?>
		<?php if($footer_menu): ?>
			<ul class="menu">
				<?php foreach ($footer_menu as $menu_item): ?>
					<li><a href="<?php echo $menu_item['url']; ?>" title="<?php echo $menu_item['anchor_text']; ?>"<?php if($menu_item['target_blank']): ?> target="_blank"<?php endif; ?><?php if($info['basic_url'] == $menu_item['url']): ?> class="active"<?php endif; ?>><?php echo $menu_item['title']; ?></a></li>
				<?php endforeach; ?>
			</ul>
		<?php endif; ?>
	</footer>

	<?php echo Html::media('js_gdefault'); ?>
	<?php //echo Html::media('js_gcookie'); ?>
	<?php $this->block('extrabody'); ?><?php $this->endblock('extrabody'); ?>
	<?php //$newsletter_form = Widget_Newsletter::form(array('lang' => $info['lang'], 'code' => 'list')); ?>
	<?php //echo View::factory('newsletter/widget/manage_leaving', ['newsletter_form' => $newsletter_form]); ?>
	<?php echo View::factory('admin/widget_fe/toolbar'); ?>
</body>
</html>