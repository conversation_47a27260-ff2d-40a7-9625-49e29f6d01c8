<div class="cd-inquiry cd-preorder">
	<div class="cd-inquiry-title cd-preorder-title"><?php echo Arr::get($cmslabel, 'preorder_title'); ?></div>

	<div class="cd-notify-form">
		<div id="notifyme-<?php echo $form_content; ?>">
			<form class="form-inline" action="#notifyme_form" method="post" name="notifyme_form" id="notifyme_add_form_<?php echo $form_content; ?>">
				<input type="hidden" name="id" value="<?php echo $form_content; ?>" />
				<p>
					<label for="name">Ime i prezime</label>
					<input type="text" name="full_name" id="field-full_name" <?php if (!empty($info['user_fullname'])): ?>value="<?php echo $info['user_fullname']; ?>"<?php endif; ?>>
					<span id="field-error-full_name" class="field_error error" style="display: none"><?php echo Arr::get($cmslabel, 'error_name', 'error_name'); ?></span>
				</p>
				<p>
					<label for="email">Email adresa</label>
					<input type="text" name="email" id="field-email" <?php if (!empty($info['user_email'])): ?>value="<?php echo $info['user_email']; ?>"<?php endif; ?>>
					<span id="field-error-email" class="field_error error" style="display: none"><?php echo Arr::get($cmslabel, 'error_email', 'error_email'); ?></span>
				</p>
				<p>
					<label for="name">Telefon</label>
					<input type="text" name="phone" id="field-phone" <?php if (!empty($info['user_phone'])): ?>value="<?php echo $info['user_phone']; ?>"<?php endif; ?>>
					<span id="field-error-phone" class="field_error error" style="display: none"><?php echo Arr::get($cmslabel, 'error_phone', 'error_phone'); ?></span>
				</p>
				<p><button type="submit" class="btn btn-red btn-large btn-preorder"><?php echo Arr::get($cmslabel, 'notifyme'); ?></button></p>
			</form>
			<div class="cd-inquiry-success notifyme_success message" style="display: none">
				<?php echo Arr::get($cmslabel, 'notifyme_catalog_ty'); ?>
			</div>
		</div>
	</div>
</div>