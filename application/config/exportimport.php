<?php

defined('SYSPATH') or die('No direct script access.');

return [
    'webshop' => [
        // mbikeshop
        'order_mbikeshop' => [
            'title' => 'Narudžbe',
            'model' => ['webshoporderitem', 'ite'],
            'related_model' => ['webshoporder', 'ord', 'order_id'],
            'extra_models' => ['status'],
            'available_filters' => [
                'datetime_created-s' => 'Narudžbe od',
                'datetime_created-e' => 'Narudžbe do',
                'item' => 'Proizvod',
                'category_select' => 'Kategorija',
                'manufacturer' => 'Brand',
                'status_id' => 'Status',
                'payment_id' => 'Način plaćanja',
                'paid_cc_type' => 'Tip kartice',
                'paid_cc_installments' => 'Broj rata',
                'orderitem_ean_code' => 'EAN',
                'order_ids' => 'ID narudžbi',
            ],
            'fields' => [
                'id' => 'ite.id',
                'skladiste' => 'skladiste',
                'datumdokumenta' => 'datumdokumenta',
                'brojdokumenta' => 'brojdokumenta',
                'obracunskajedinica' => 'obracunskajedinica',
                'vrstadokumenta' => 'vrstadokumenta',
                'podvd' => 'podvd',
                'napomena' => 'napomena',
                'kupac' => 'kupac',
                'primalac' => 'primalac',
                'dpo' => 'dpo',
                'bf' => 'bf',
                'rf' => 'rf',
                'nacinplacanja' => 'nacinplacanja',
                'uplata' => 'ord.total',
                'oznaka' => 'oznaka',
                'staid' => 'ite.id',
                'zagid' => 'ord.id',
                'sifra' => 'ite.code',
                'izlaz' => 'ite.qty',
                'vpc' => 'vpc',
                'mpc' => 'mpc',
                'tarifa' => 'tarifa',
                'popustpos' => 'popustpos',
                'dodatniopis' => 'ite.description',
                'shi_staid' => 'shi_staid',
                'shi_zagid' => 'ord.id',
                'shi_sifra' => 'shi_sifra',
                'shi_izlaz' => 'shi_izlaz',
                'shi_vpc' => 'shi_vpc',
                'shi_mpc' => 'shi_mpc',
                'shi_tarifa' => 'shi_tarifa',
                'shi_popustpos' => 'shi_popustpos',
                'shi_dodatniopis' => 'shi_dodatniopis',
            ],
            'alternative_field_title' => [
                'qty' => 'Naručena količina',
                'skladiste' => 'Skladište',
                'brojdokumenta' => 'Broj dokumenta',
                'obracunskajedinica' => 'Obračunska jedinica',
                'vrstadokumenta' => 'Vrsta dokumenta',
                'podvd' => 'podvd',
                'napomena' => 'Napomena',
                'kupac' => 'Kupac',
                'primalac' => 'Primalac',
                'dpo' => 'Datum dospijeća',
                'nacinplacanja' => 'Način placanja',
                'uplata' => 'Uplata',
                'oznaka' => 'Oznaka',
                'staid' => 'ID stavke',
                'zagid' => 'ID narudžbe',
                'sifra' => 'Šifra stavke',
                'izlaz' => 'Izlaz',
                'vpc' => 'VPC',
                'mpc' => 'MPC',
                'tarifa' => 'Tarifa',
                'popustpos' => 'Popust u postocima',
                'dodatniopis' => 'Dodatni opis',

            ],
            'use_ajax' => true,
            'per_part' => 20,
            'limit' => 500,
            'show_export_fields' => false,
            'custom_export_structure' => ['bikeshopxml' => 'xml', 'bikeshopcsv' => 'csv'],
            'item_filter_field' => 'ite.product_id',
            'credit_card_list' => true,
            'manufacturer_filter_field' => 'ite.manufacturer_id',
            'db_expression' => [
                'skladiste' => 8,
                'datumdokumenta' => "(SELECT FROM_UNIXTIME(datetime_created,'%Y-%m-%dT%T+02:00') as 'datumdokumenta' from webshop_orders WHERE id = ord.id)",
                'brojdokumenta' => "''",
                'obracunskajedinica' => 4000,
                'vrstadokumenta' => 22,//"(SELECT IF(paid_datetime > 0, 22,22) as 'vrstadokumenta' from webshop_orders WHERE id = ord.id)",
                'podvd' => 0,
                'kupac' => "(SELECT IF(is_credit_card=1, '00', '000') as 'kupac' FROM webshop_payments WHERE id = ord.payment_id)",
                'primalac' => "(SELECT IF(is_credit_card=1, '00', '000') as 'primalac' FROM webshop_payments WHERE id = ord.payment_id)",
                'napomena' => "(SELECT CONCAT(first_name, ' ', last_name, ', ', address, ', ' , zipcode, ' ', city, ', ', email, ', ', phone) as 'napomena' FROM webshop_orders WHERE id = ord.id)",
                'dpo' => "(SELECT DATE_FORMAT(DATE_ADD(FROM_UNIXTIME(datetime_created), INTERVAL 8 DAY), '%Y-%m-%dT%T+02:00') as 'dpo' FROM webshop_orders WHERE id = ord.id)",
                'bf' => "''",
                'rf' => "''",
                'nacinplacanja' => "(SELECT IF(id=4, '1', IF(id=1, '4', payment_id)) as 'nacinplacanja' FROM webshop_payments WHERE id = ord.payment_id)",
                'oznaka' => "''",
                'vpc' => "(SELECT ROUND((price / (ite.tax + 1)), 2) as vpc FROM catalog_products WHERE id = ite.product_id)",
                'mpc' => "(SELECT price FROM catalog_products WHERE id = ite.product_id)",
                'tarifa' => "'E'",
                'popustpos' => 0,//"(SELECT ROUND((discount * 100), 0) as popustpos FROM webshop_order_items WHERE id = ite.id)",
                'shi_staid' => "(SELECT CONCAT('999999', id)  FROM webshop_order_extraitems WHERE order_id=ord.id AND category='shipping')",
                'shi_sifra' => "'USLDOST'",
                'shi_izlaz' => 1,
                'shi_vpc' => "(SELECT total_basic FROM webshop_order_extraitems WHERE order_id=ord.id AND category='shipping')",
                'shi_mpc' => "(SELECT total FROM webshop_order_extraitems WHERE order_id=ord.id AND category='shipping')",
                'shi_tarifa' => "'E'",
                'shi_popustpos' => 0,
                'shi_dodatniopis' => "(SELECT title FROM webshop_order_extraitems WHERE order_id=ord.id AND category='shipping')",
            ],

            'available_autocomplete' => [
                'status_id' => 'webshopstatus',
                'payment_id' => 'webshoppayment',
                'category_select' => 'catalogcategory',
                'manufacturer' => 'catalogmanufacturer',
            ],
        ],
    ],
];
