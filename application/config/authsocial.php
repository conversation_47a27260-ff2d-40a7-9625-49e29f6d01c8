<?php
/*!
* HybridAuth
* http://hybridauth.sourceforge.net | https://github.com/hybridauth/hybridauth
* (c) 2009-2011 HybridAuth authors | hybridauth.sourceforge.net/licenses.html
* HybridAuth Config file: http://hybridauth.sourceforge.net/userguide/Configuration.html
*/


return array(
	'providers' => array (
		'Facebook' 	=> array('enabled' => TRUE, 'keys' => array('id' => '660863102601525', 'secret' => '0e1c34bc1976b3c9206fa25153a0764d')),
		'Google' 	=> array('enabled' => TRUE, 'keys' => array('id' => '************-smdp05b1jkqdf6bs88klcl88eass2n2u.apps.googleusercontent.com', 'secret' => '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')),
	),
    'version' => 2,
);
