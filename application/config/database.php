<?php

defined('SYSPATH') OR die('No direct access allowed.');

$host = (isset($_SERVER['HTTP_HOST'])) ? $_SERVER['HTTP_HOST'] : str_replace('/var/www/vhosts/m-bikeshop.com/httpdocs/', 'm-bikeshop.com', dirname(__FILE__));

if (in_array($host, ['m-bikeshop.com', 'www.m-bikeshop.com'])) {
    return [
        'default' => [
            'connection' => [
                'hostname' => '***********',
                'username' => 'marker-app',
                'password' => 'HNTYj87%*nm91@hnYNm*$kahn7849kN',
                'database' => 'mbikeshop_web',
            ],
        ],
    ];
} else if (in_array($host, ['mbikeshop.markerdev.info'])) {
    return [
        'default' => [
            'connection' => [
                'hostname' => '***********',
                'username' => 'marker-app',
                'password' => 'HNTYj87%*nm91@hnYNm*$kahn7849kN',
                'database' => 'mbikeshop_dev',
            ],
        ],
    ];
} else {
    return [
        'default' => [
            'connection' => [
                'database' => 'mbikeshop_dev',
                //'password' => 'm2011!dev'
            ],
            'caching'      => FALSE, // remove in production
            'profiling'    => TRUE, // remove in production
        ],
    ];
}