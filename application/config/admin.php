<?php defined('SYSPATH') or die('No direct script access.');

return [
    'force_language' => 'ba',
    'import2' => [
        'display_errors' => true,
        'model' => 'catalogproduct',
        'title' => 'Import',
        'ignore_columns' => [
        ],
        'main_separator' => ',',
        'catalogcategory' => [
            'fields' => [
                'title_%LANG%',
                'position_h',
            ],
            'taxonomy_separator' => ' > ',
            'code_md5' => true,
            'use_previous_category_position_as_default' => true,
        ],
        'catalogmanufacturer' => [
            'fields' => [
                'title_%LANG%',
                'position_h',
            ],
            'taxonomy_separator' => ' > ',
            'code_md5' => true,
        ],
        'catalogattribute' => [
            'fields' => [

            ],
            'attr_identificator' => 'catalogattribute.',
            'ignore_attribute_start_string' => '_catalogattribute.',
            'attributeitem_separator' => '|',
        ],
        'catalogproduct' => [
            'fields' => [
                'code',
                'title_ba',
                'manufacturer',
                'category',
                'categories',
                'content_ba',
                'attributes',
                'datetime_last_sync',
                'seo_description_ba',
                'seo_title_ba',
                'seo_h1_ba',
                'seo_keywords_ba',
                'visible',
            ],
            'default_tax_amount' => '17',
            'extra_category_columns' => [
                'extra_category_1',
                'extra_category_2',
                'extra_category_3',
            ],
            'use_code_as_string' => true,
            'use_advanced_logger' => true,
        ],
        'catalogproductvariation' => [
            'fields' => [
                'code',
                'title',
                'product',
                'visible',
                'attributes',
                'datetime_last_sync',
            ],
            'ignore_fields' => [
                'basic_price',
                'discount_percent',
                'price',
                'available_qty',
            ],
            'get_product_from_variation' => true,
            'use_advanced_logger' => true,
        ],
    ],
    'exportcustom_models' => ['webshoporder'],
    'exportcustom_models_redirect_url' => [
        'webshoporder' => 'export/order_mbikeshop/?order_ids=%IDS%&&search=1'
    ],
];