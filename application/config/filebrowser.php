<?php

defined('SYSPATH') or die('No direct script access.');

return [
	'generate_thumb' => [
		'big' => ['width' => 750, 'height' => NULL, 'crop' => FALSE],
		'big_crop' => ['width' => 750, 'height' => 320, 'crop' => TRUE],
		'medium' => ['width' => 350, 'height' => NULL, 'crop' => FALSE],
		'medium_crop' => ['width' => 350, 'height' => 230, 'crop' => TRUE],
		'small_crop' => ['width' => 220, 'height' => 140, 'crop' => TRUE],
	],

	'generate_thumb_upload' => [
		// <model-field>, [dimenions]
		'menuitem-image' => [
			['width' => 60, 'height' => 60, 'crop' => FALSE],
		],
		'rotatorelement-image' => [
			['width' => 160, 'height' => 64, 'crop' => TRUE],
			['width' => 1920, 'height' => 624, 'crop' => TRUE],
			['width' => 1920, 'height' => 432, 'crop' => FALSE],
			['width' => 416, 'height' => 416, 'crop' => TRUE],
			['width' => 632, 'height' => 416, 'crop' => TRUE],
			['width' => 1280, 'height' => 424, 'crop' => TRUE],
			['width' => 980, 'height' => 980, 'crop' => TRUE],
			['width' => 210, 'height' => 210, 'crop' => FALSE],
		],
		'rotatorelement-image_2' => [
			['width' => 980, 'height' => 153, 'crop' => TRUE],
			['width' => 980, 'height' => 1089, 'crop' => TRUE],
			['width' => 980, 'height' => 762, 'crop' => TRUE],
			['width' => 416, 'height' => 416, 'crop' => TRUE],
			['width' => 632, 'height' => 416, 'crop' => TRUE],
			['width' => 1280, 'height' => 424, 'crop' => TRUE],
			['width' => 980, 'height' => 980, 'crop' => TRUE],
		],
		'catalogproductfile-file' => [
			['width' => 1090, 'height' => 1090, 'crop' => FALSE],
			['width' => 400, 'height' => 400, 'crop' => FALSE],
			['width' => 980, 'height' => 980, 'crop' => FALSE],
			['width' => 150, 'height' => 150, 'crop' => FALSE],
		],
		'catalogcategoryrotatorelement-image' => [
			['width' => 260, 'height' => 435, 'crop' => TRUE],
		],
		'catalogcategoryrotatorelement-image2' => [
			['width' => 600, 'height' => 600, 'crop' => TRUE],
		],
		'publishfile-file' => [
			['width' => 550, 'height' => 325, 'crop' => TRUE],
			['width' => 275, 'height' => 150, 'crop' => TRUE],
			['width' => 640, 'height' => 360, 'crop' => TRUE],
			['width' => 400, 'height' => 225, 'crop' => TRUE],
			['width' => 1280, 'height' => 720, 'crop' => TRUE],
		],
		'locationpointfile-file' => [
			['width' => 504, 'height' => 320, 'crop' => FALSE],
			['width' => 980, 'height' => 623, 'crop' => FALSE],
		]
	],
];
