<template>
	<Link rel="apple-touch-icon" sizes="76x76" href="/apple-touch-icon.png" />
	<Link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
	<Link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
	<Link rel="manifest" href="/site.webmanifest" />
	<Link rel="mask-icon" href="/safari-pinned-tab.svg" color="#5bbad5" />
	<Meta name="msapplication-TileColor" content="#ffffff" />
	<Meta name="theme-color" content="#ffffff" />
	<Body :class="{'fixed-header': fixedHeader && showHeader}" />
	<div class="page-wrapper">
		<ClientOnly>
			<LazyBaseThemeUiAdminBar v-if="user?.staff || user?.superuser || user?.developer" />
			<LazyCmsPageLoadingIndicator :hydrate-after="2000" />
		</ClientOnly>
		<template v-if="showHeader">
			<CmsHeader />
		</template>
		<slot />
		<template v-if="showHeader">
			<ClientOnly>
				<LazyCmsNewsletter hydrate-on-visible />
			</ClientOnly>
			<BaseCmsRotator :fetch="{code: 'testimonials', limit: 12, response_fields: ['id','image_upload_path','title','image_2_upload_path','content']}" v-slot="{items}" v-if="nuxtApp.$appGlobalData.template == 'CmsHomepage'">
				<LazyCmsTestimonials :items="items" v-if="items?.length" />
			</BaseCmsRotator>
			<CmsBenefits />
			<CmsFooter />
		</template>
	</div>
	<ClientOnly>
		<LazyGdpr :hydrate-after="1000" />
		<LazyNewsletterLeaving hydrate-on-visible />
		<LazyWebshopAddToCartModal hydrate-on-visible />
		<LazyBaseThemeUiModal v-if="Object.keys(modal.activeModals()).includes('quick')" name="quick" :zoom="true" :mask-closable="true" :svgicons="false" />
		<div @click="scrollTo('body')" class="to-top" :class="{'active': showToTopButton}"></div>
	</ClientOnly>
</template>

<script setup>
	const nuxtApp = useNuxtApp();
	const {user} = useAuth();
	const {scrollTo, insertBefore, appendTo, onMediaQuery} = useDom();
	const modal = useModal();
	const {addScript} = useMeta();

	// show/hide header for specific pages
	const route = useRoute();
	const showHeader = computed(() => {
		if(!route.meta.action) return true;
		return (route.meta.controller == 'webshop' && ['login', 'customer', 'shipping', 'payment', 'review_order'].includes(route.meta.action)) ? false : true;
	});

	const fixedHeader = ref(null);
	const showToTopButton = ref(false);
	const onScroll = () => {
		const scroll = window.scrollY;
		if (!document.body.classList.contains('page-checkout')) {
			showToTopButton.value = (scroll > 270) ? true : false; //to top button
			fixedHeader.value = (scroll > 300) ? true : false; //fixed header
		}
	};

	// move categories to fixed header element
	const {matches: mobileBreakpoint} = onMediaQuery({
		query: '(max-width: 980px)',
		leave: () => {
			window.location.reload();
		}
	});

	watch(fixedHeader, (value) => {
		if(mobileBreakpoint.value) return;
		if (value) {
			insertBefore('.nav-categories', '.logo');
		} else {
			appendTo('.nav-categories', '.wrapper-nav');
		}
	});

	onMounted(async () => {
		window.addEventListener('scroll', onScroll, {passive: true});
		addScript({
			innerHTML: `var Tawk_API=Tawk_API||{}, Tawk_LoadStart=new Date(); (function(){ var s1=document.createElement("script"),s0=document.getElementsByTagName("script")[0]; s1.async=true; s1.src='https://embed.tawk.to/5d1b5b4922d70e36c2a3c484/1hpimbgdg'; s1.charset='UTF-8';
			s1.setAttribute('crossorigin','*'); s0.parentNode.insertBefore(s1,s0); })(); setTimeout(() => { const liveChat = document.querySelector('.widget-visible iframe'); if(liveChat) { liveChat.style.bottom = '20px'; liveChat.style.left = '20px'; liveChat.style.transitionProperty = ''; }
			},3000);`,
			async: true,
			defer: true,
			key: 'tawktolivechat',
			gdpr: 'marketing',
		});
	});

	onBeforeUnmount(() => {
		window.removeEventListener('scroll', onScroll);
	})

	provide('layout', {fixedHeader});
</script>

<style lang="less">
	.widget-visible iframe{.transition(bottom);}
	.fixed-footer .widget-visible iframe{
		@media (max-width: 1460px){bottom: 120px!important;}
		@media (max-width: @m){bottom: 73px!important;}
	}
	.page-catalog-compare.has-compare-items .widget-visible iframe{
		@media (max-width: 1460px){bottom: 120px!important;}
		@media (max-width: @m){bottom: 90px!important;}
	}
	.page-webshop-shopping_cart .widget-visible iframe{
		@media (max-width: @m){bottom: 80px!important;}
	}
	.gdpr-active{overflow: hidden;}
	.gdpr-active.modal-active{
		.base-modal{z-index: 99999;}
		.base-modal-mask{background: none;}
	}
	.active-nav-m .widget-visible iframe{left: -400px!important;}
</style>

<style lang="less" scoped>
	.to-top{
		position: fixed; width: 56px; height: 56px; right: 80px; bottom: -80px; background: var(--colorBaseBlue); border-radius: 50%; z-index: 100; cursor: pointer; transition: background 0.3s, bottom 0.3s; box-shadow: 0 5px 20px 0 rgba(0,12,26,0.3);
		&:before{.pseudo(100%,100%); .icon-arrow(); font: 16px/16px var(--fonti); color: var(--colorWhite); display: flex; align-items: center; justify-content: center;}
		&.active{bottom: 80px;}
		@media (min-width: @h){
			&:hover{background: var(--colorDarkBlue);}
		}
		@media (max-width: @l){right: 40px;}
		@media (max-width: @t){
			width: 40px; height: 40px; right: 44px;
			&:before{font-size: 12px; line-height: 12px;}
			&.active{bottom: 60px;}
		}
		@media (max-width: @m){
			width: 48px; height: 48px; right: 16px;
			&:before{font-size: 16px; line-height: 16px;}
			&.active{
				bottom: 32px;
				@media (max-width: @m){bottom: 20px;}
			}
		}
	}
	@media (max-width: @m){
		.fixed-footer .to-top{bottom: 75px;}
	}
	.page-catalog-compare .to-top{display: none;}

	/*------- modal -------*/
	:deep(.base-modal) {
		svg{display: none;}
		h1{
			font-size: 32px; line-height: 1.4; padding: 0 0 15px;
			@media (max-width: @h){font-size: 21px; padding: 0 0 10px;}
		}
		.base-modal-toolbar{
			top: 30px; right: 30px;
			@media (max-width: 1200px){top: 15px; right: 15px;}
		}
		.base-modal-toolbar-close{
			background: var(--colorBaseBlue); .transition(background); margin-bottom: 20px;
			@media (max-width: 1200px){margin-bottom: 10px;}
			&:hover{background: var(--colorDarkBlue);}
			&:before{.icon-close(); font: 16px/1 var(--fonti); color: var(--colorWhite); font-weight: bold;
				@media (max-width: 1200px){font-size: 13px;}
			}
		}
		.base-modal-toolbar-zoom-in, .base-modal-toolbar-zoom-out{
			height: 32px;
			@media (max-width: 1200px){height: 40px;}
			&:before{.icon-zoom-in(); font: 24px/1 var(--fonti); color: var(--colorBase);}
		}
		.base-modal-toolbar-zoom-out:before{.icon-zoom-out();}
		.base-modal-gallery-nav-btn{
			width: 56px; height: 56px; background: var(--colorLightBlueBackground); border-radius: 100px; .transition(background);
			@media (max-width: 1200px){width: 40px; height: 40px;}
			&:before{
				.icon-arrow2(); font: 20px/1 var(--fonti); color: var(--colorBase); width: 16px; height: 20px; .transition(color);
				@media (max-width: 1200px){font-size: 15px; width: auto; height: auto;}
			}
			@media (min-width: @h){
				&:hover{
					background: var(--colorBaseBlue);
					&:before{color: #fff;}
				}
			}
			&.swiper-button-disabled{display: none;}
		}
		.base-modal-gallery-nav{
			left: 15%; right: 15%;
			@media (max-width: 1200px){left: 15px; right: 15px;}
		}
		.base-modal-gallery-thumbs{
			width: 105px; box-sizing: content-box; padding: 15px 15px 15px 65px;
			@media (max-width: 1200px){width: 100%; padding: 15px;}
		}
		.base-modal-gallery-thumbs-item{
			border: 1px solid var(--colorBorderLightForms);
			&.active{border-color: var(--colorBaseBlue);}
		}
		.base-modal-content-wrapper{overflow: hidden;}
		.base-modal-content{
			overflow: auto; max-height: 60vh; margin: 0 -10px 0 0; padding-right: 10px;
			&::-webkit-scrollbar{-webkit-appearance: none; width: 3px; height: 3px; background: var(--colorBorderLightForms);border-radius: 4px;}
			&::-webkit-scrollbar-thumb{
				background-color: var(--colorBaseBlue); border-radius: 4px;
				box-shadow: 0 0 1px rgba(255,255,255,.5);
			}
		}
		.base-modal-cnt-close{
			&:before{.icon-close(); font: 25px/1 var(--fonti); color: var(--colorRed);
				@media (max-width: @h){font-size: 14px;}
			}
			@media (max-width: @h){top: 5px; right: 5px;}
		}
	}
	/*------- /modal -------*/
</style>
