export default defineAppConfig({
	host: 'https://mbikeshop.markerdev.info',
	lang: 'ba',
	baseCompatibility: '1.29.0',
	hapi: {
		tracking: {
			events: ['catalogproduct'],
		},
	},
	auth: {
		dashboard: 'auth_my_webshoporder',
	},
	compare: {
		limit: 5,
	},
	viewOrder: {
		warehouse: {
			enabled: true,
			permission: 'staff',
			includeEmpty: false,
		},
	},
	catalog: {
		productsResponseFields: [
			'id',
			'code',
			'is_available',
			'priority_details',
			'category_title',
			'variation_available',
			'variation_request',
			'variation_price_same',
			'variation_price_min',
			'variation_request',
			'variation_quickdata',
			'variation_attributes',
			'variation_attributes_all',
			'variation_discount_percent_max',
			'discount_percent_custom',
			'price_custom',
			'basic_price_custom',
			'url_without_domain',
			'main_image_thumbs',
			'main_image_title',
			'main_image_description',
			'title',
			'is_available',
			'attributes_special',
			'manufacturer_code',
			'manufacturer_url_without_domain',
			'manufacturer_title',
			'shopping_cart_code',
			'status',
		],
		listsResponseFields: ['id', 'code', 'landing_page', 'level', 'search_id', 'title', 'url_without_domain', 'languages_urls', 'breadcrumbs', 'content', 'seo_description', 'seo_h1', 'seo_keywords', 'seo_title', 'short_description'],
	},
	publish: {
		postsResponseFields: ['id', 'url_without_domain', 'title', 'category_title', 'main_image_upload_path', 'main_image_thumbs'],
	},
	google: {
		gtm: {
			env: ['production'],
			gdpr: 'analytics',
		},
		ga4: {
			env: ['production'],
			gdpr: 'analytics',
		},
		tracking: {
			gdpr: 'analytics',
			events: ['gdprConsents', 'pageView', 'viewItem', 'viewItemList', 'selectItem', 'viewCart', 'removeProduct', 'addProduct', 'login', 'beginCheckout', 'addShippingInfo', 'addPaymentInfo', 'purchase', 'refund', 'addToWishlist', 'viewPromotion', 'selectPromotion'],
		},
	},
	facebook: {
		pixel: {
			env: ['development', 'production'],
			apiKey: '515720375820902',
			target: 'head',
			gdpr: 'analytics',
		},
		conversionApi: {
			events: ['addPaymentInfo', 'addToCart', 'completeRegistration', 'initiateCheckout', 'pageView', 'purchase', 'search', 'viewContent'],
			search: {
				data: ['search_string'],
			},
		},
	},
	cache: {
		debug: false,
		enabled: process.env.NODE_ENV == 'development' ? false : true,
		refresh: {
			currency: 1440, // 24 hours
			endpoints: 1440,
			info: 1440,
			labels: 5,
			menus: 5,
			redirects: 5,
			rotators: 5,
			routes: 5,
			token: 1440,
			newsletter: 1440,
			gdprTemplate: 1440,
			thumbs: 5,
			cmsPages: 5,
			catalogCategories: 5,
			catalogLists: 5,
			catalogManufacturers: 5,
			catalogProduct: 5,
			catalogProducts: 5,
			catalogSellers: 5,
			faqCategories: 5,
			faqQuestions: 5,
			faqQuestion: 5,
			publishCategories: 5,
			publishPost: 5,
			publishPosts: 5,
			locationPoints: 5,
			staticContent: 5,
		},
	},
});
