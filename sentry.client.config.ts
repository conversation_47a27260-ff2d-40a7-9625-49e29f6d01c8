import * as Sentry from '@sentry/nuxt';
const config = useAppConfig();

Sentry.init({
	dsn: 'https://<EMAIL>/4508165975441408',
	environment: process.env.NODE_ENV == 'development' ? 'local' : 'development',
	release: process.env.NODE_ENV == 'development' ? `core ${config.version}` : `core ${config.version}, build ${import.meta.env.VITE_BUILD_VERSION}`,
	integrations: [Sentry.browserTracingIntegration(), Sentry.replayIntegration()],
	ignoreErrors: [
		"Failed to execute 'insertBefore' on 'Node': The node before which the new node is to be inserted is not a child of this node.", // vue error
		'Importing a module script failed.',
		'Error 401',
		'[GET] "/api/nuxtapi/checkInitData/": <no response> Load failed',
		"Cannot destructure property 'bum' of 'v' as it is null.",
		"null is not an object (evaluating 'H.value.off')", // vueform
		"undefined is not an object (evaluating 'e.default')", // nuxt build
		"Cannot read properties of null (reading 'emitsOptions')", // vue
		'/_nuxt/builds/meta/',
		'/node_modules/@sentry-internal/',
		'/api/nuxtapi/fbcapi',
		'[POST] "/api/nuxtapi/fbcapi": <no response> Load failed',
		'Event `Event` (type=error) captured as promise rejection',
		"undefined is not an object (evaluating 'a.J')",
		'No error message',
	],

	// Error monitoring
	sampleRate: process.env.NODE_ENV == 'development' ? 0 : 0,
	replaysOnErrorSampleRate: process.env.NODE_ENV == 'development' ? 0 : 0, // Screen capture

	// Performance monitoring
	tracesSampleRate: process.env.NODE_ENV == 'development' ? 0 : 0,
	replaysSessionSampleRate: 0, // Screen capture
});
